# 🚌 GL Bus Reservation System

A modern, comprehensive bus reservation system built with Laravel 11, featuring an advanced walk-in booking terminal, real-time analytics, and professional-grade UI/UX design. Perfect for bus companies looking to digitize their operations with both online and terminal-based booking capabilities.

![GL Bus Logo](public/images/gl-logo.png)

## 🆕 Latest Updates (v2.0)

### 🎫 **Modern Walk-in Booking Terminal**
- **Compact List Interface**: Space-efficient trip display with professional styling
- **Modal-Based Workflow**: Seamless customer info → seat selection → payment process
- **Real-time Seat Map**: Interactive seat selection with live availability updates
- **Toast Notifications**: Professional notification system replacing browser alerts
- **Advanced Search**: Support for both today's trips and future date/route combinations

### 📊 **Live Sales Analytics**
- **Real-time Daily Reports**: Live data from database instead of static numbers
- **Comprehensive Metrics**: Total bookings, revenue, walk-in vs online breakdown
- **Peak Hour Analysis**: Automatic detection of busiest booking periods
- **Popular Route Tracking**: Data-driven insights on most booked routes
- **Print-Ready Reports**: Professional PDF generation with actual sales data

## ✨ Features

### 🎫 **Customer Features**
- **Online Booking**: Book bus tickets with real-time seat selection
- **Interactive Seat Map**: Visual seat selection with availability status
- **Multiple Payment Options**: GCash, Credit Cards, and Cash payments
- **Digital Tickets**: No need to print - digital tickets with QR codes
- **Booking Management**: View, modify, and cancel bookings
- **Route Search**: Find trips by origin, destination, and date
- **12-Hour Time Format**: User-friendly time display with AM/PM

### 👨‍💼 **Agent Features**
- **Modern Walk-in Terminal**: Professional booking interface with compact trip listings
- **Modal-Based Booking**: Streamlined workflow - customer info → seat selection → payment
- **Interactive Seat Maps**: Real-time seat availability with visual selection
- **Multiple Payment Methods**: Cash, GCash, Maya payment processing
- **Advanced Trip Search**: Today's trips + future date/route combinations
- **Live Sales Dashboard**: Real-time daily sales reports and analytics
- **Toast Notifications**: Professional notification system for better UX
- **Print Functionality**: Generate tickets and receipts for walk-in customers
- **Customer Management**: Create and manage walk-in customer accounts

### 👑 **Admin Features**
- **Complete Dashboard**: Overview of all system operations with real-time data
- **Bus Management**: Add, edit, and manage bus fleet with detailed specifications
- **Route Management**: Configure routes with start/end points and distance calculation
- **Schedule Management**: Set departure and arrival times with timezone support
- **Trip Management**: Create and manage trip schedules with capacity tracking
- **Fare Management**: Dynamic pricing based on routes and distance
- **User Management**: Manage customers, agents, and admins with role-based access
- **Advanced Reports**: Live sales analytics, revenue tracking, and business intelligence
- **PDF Reports**: Generate professional reports for daily sales, monthly revenue, and bus utilization
- **Real-time Analytics**: Live dashboard with booking trends and performance metrics

### 🎨 **UI/UX Features**
- **Modern Design**: Professional gradient-based design system with gold/teal color scheme
- **Responsive Layout**: Works perfectly on desktop, tablet, and mobile devices
- **Interactive Maps**: Route visualization with distance calculation and mapping
- **Real-time Updates**: AJAX-powered dynamic content with live data refresh
- **Professional Branding**: Consistent GL Bus branding throughout all interfaces
- **Toast Notifications**: Professional Toastr-based notification system
- **Modal Workflows**: Smooth, non-intrusive booking processes
- **Loading States**: Professional loading animations and progress indicators
- **Accessibility**: WCAG-compliant design with keyboard navigation support

## 🛠️ Tech Stack

### **Backend**
- **Laravel 11.x** - PHP framework with modern features
- **MySQL** - Relational database for data storage
- **Laravel Sanctum** - API authentication
- **Laravel Events** - Event-driven architecture for bookings

### **Frontend**
- **Tailwind CSS** - Utility-first CSS framework with custom design system
- **jQuery & AJAX** - Dynamic content and real-time interactions
- **Leaflet Maps** - Interactive route visualization and mapping
- **SweetAlert2** - Beautiful alert dialogs and confirmations
- **Toastr** - Professional toast notification system
- **Chart.js** - Analytics and reporting charts with real-time data
- **Custom CSS** - Professional gradient designs and animations

### **Additional Features**
- **OSRM Routing** - Route calculation and distance mapping
- **Responsive Design** - Mobile-first approach with breakpoint optimization
- **Real-time Updates** - Live seat availability and booking status
- **File Upload** - Image handling for logos and assets
- **API Integration** - RESTful APIs for mobile app development
- **Live Analytics** - Real-time sales data and business intelligence
- **Professional Reporting** - PDF generation with actual business data
- **Multi-language Support** - Internationalization ready
- **Performance Optimization** - Caching and query optimization

## 📋 System Requirements

### **Server Requirements**
- **PHP** >= 8.1
- **Composer** (PHP dependency manager)
- **Node.js** >= 16.x and NPM (for asset compilation)
- **MySQL** >= 8.0 or MariaDB >= 10.3
- **Apache/Nginx** web server
- **SSL Certificate** (recommended for production)

### **PHP Extensions Required**
- BCMath PHP Extension
- Ctype PHP Extension
- Fileinfo PHP Extension
- JSON PHP Extension
- Mbstring PHP Extension
- OpenSSL PHP Extension
- PDO PHP Extension
- Tokenizer PHP Extension
- XML PHP Extension
- GD PHP Extension (for image processing)

## 🚀 Installation Guide

### **Step 1: Prerequisites Setup**

Before installing, make sure you have the following installed on your system:

#### **For Windows (XAMPP/WAMP)**
1. **Download and install XAMPP** from [https://www.apachefriends.org/](https://www.apachefriends.org/)
2. **Start Apache and MySQL** from XAMPP Control Panel
3. **Install Composer** from [https://getcomposer.org/](https://getcomposer.org/)
4. **Install Node.js** from [https://nodejs.org/](https://nodejs.org/)

#### **For Linux/Ubuntu**
```bash
# Update package list
sudo apt update

# Install PHP and required extensions
sudo apt install php8.1 php8.1-cli php8.1-fpm php8.1-mysql php8.1-xml php8.1-curl php8.1-gd php8.1-mbstring php8.1-zip

# Install MySQL
sudo apt install mysql-server

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### **For macOS**
```bash
# Install Homebrew (if not installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install PHP
brew install php@8.1

# Install MySQL
brew install mysql

# Install Composer
brew install composer

# Install Node.js
brew install node
```

### **Step 2: Download and Setup Project**

#### **Option A: Clone from Repository**
```bash
git clone https://github.com/doriemon267/GL_BUS.git
cd GL_BUS
```

#### **Option B: Download ZIP**
1. Download the project ZIP file
2. Extract to your web server directory (e.g., `C:\xampp\htdocs\GL_BUS`)
3. Open terminal/command prompt in the project directory

### **Step 3: Install Dependencies**

```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### **Step 4: Environment Configuration**

#### **Create Environment File**
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

#### **Configure Database**
Edit the `.env` file with your database settings:

```env
APP_NAME="GL Bus Reservation System"
APP_ENV=local
APP_KEY=base64:your-generated-key
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=bus_reservation
DB_USERNAME=root
DB_PASSWORD=your_mysql_password
```

### **Step 5: Database Setup**

#### **Create Database**
```sql
-- Login to MySQL and create database
CREATE DATABASE bus_reservation;
```

#### **Run Migrations and Seeders**
```bash
# Run database migrations
php artisan migrate

# Seed the database with sample data
php artisan db:seed

# Or run both in one command
php artisan migrate:fresh --seed
```

### **Step 6: Compile Assets**

```bash
# For development
npm run dev

# For production
npm run build
```

### **Step 7: Start the Application**

```bash
# Start Laravel development server
php artisan serve

# The application will be available at:
# http://127.0.0.1:8000
```

## 🔑 Default Login Credentials

After seeding the database, you can login with these accounts:

### **Admin Account**
- **Email:** `<EMAIL>`
- **Password:** `Admin@2025`
- **Access:** Full system administration

### **Agent Account**
- **Email:** `<EMAIL>`
- **Password:** `agent123`
- **Access:** Modern walk-in booking terminal, sales analytics, and customer management

### **Test Customer Account**
- **Email:** `<EMAIL>`
- **Password:** `Admin@2025`
- **Access:** Customer booking interface

## 🌐 Application URLs

Once the application is running, you can access:

- **Homepage:** `http://127.0.0.1:8000/`
- **Customer Login:** `http://127.0.0.1:8000/login`
- **Customer Registration:** `http://127.0.0.1:8000/register`
- **Customer Dashboard:** `http://127.0.0.1:8000/home`
- **Admin Dashboard:** `http://127.0.0.1:8000/admin`
- **Agent Dashboard:** `http://127.0.0.1:8000/agent/dashboard`
- **Walk-in Booking Terminal:** `http://127.0.0.1:8000/agent/improved-walk-in`
- **Daily Sales Reports:** Available from agent dashboard
- **API Endpoints:** `/api/agent/*` for mobile integration

## 🎯 Key Features Walkthrough

### **For Bus Terminal Agents**

#### **Modern Walk-in Booking Process:**
1. **Access Terminal:** Login and click "Walk-in Booking" from dashboard
2. **Select Trip:** Choose from today's trips or search future dates/routes
3. **Customer Info:** Enter passenger details in modal form
4. **Seat Selection:** Interactive seat map with real-time availability
5. **Payment:** Process cash, GCash, or Maya payments
6. **Confirmation:** Generate tickets and receipts instantly

#### **Daily Sales Analytics:**
- **Live Dashboard:** Real-time booking and revenue data
- **Peak Hour Analysis:** Identify busiest booking periods
- **Route Performance:** Track most popular routes
- **Print Reports:** Professional PDF reports with actual data

### **For Administrators**

#### **Comprehensive Management:**
- **Fleet Management:** Add buses with detailed specifications
- **Route Planning:** Configure routes with distance calculation
- **Schedule Management:** Set departure times and capacity
- **User Management:** Manage agents, customers, and permissions
- **Business Intelligence:** Advanced analytics and reporting

### **For Customers**

#### **Online Booking Experience:**
- **Trip Search:** Find trips by date, origin, and destination
- **Seat Selection:** Visual seat map with real-time availability
- **Secure Payment:** Multiple payment options with confirmation
- **Digital Tickets:** QR code tickets with booking management

## 🔧 Troubleshooting

### **Common Issues and Solutions**

#### **1. Composer Install Fails**
```bash
# Clear composer cache
composer clear-cache

# Update composer
composer self-update

# Install with verbose output
composer install -v
```

#### **2. NPM Install Fails**
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### **3. Database Connection Error**
- Check MySQL service is running
- Verify database credentials in `.env`
- Ensure database exists
- Check PHP MySQL extension is installed

#### **4. Permission Errors (Linux/Mac)**
```bash
# Set proper permissions
sudo chown -R $USER:www-data storage
sudo chown -R $USER:www-data bootstrap/cache
chmod -R 775 storage
chmod -R 775 bootstrap/cache
```

#### **5. Asset Compilation Errors**
```bash
# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan view:clear
php artisan route:clear

# Recompile assets
npm run dev
```

## � API Documentation

### **Agent API Endpoints**

The system provides RESTful APIs for mobile app integration and external systems:

#### **Trip Management**
- `GET /api/agent/todays-trips` - Get today's available trips
- `GET /api/agent/search-trips` - Search trips by date/route
- `GET /api/agent/trip-seats/{trip}` - Get seat availability for specific trip
- `GET /api/agent/trip-details/{trip}` - Get detailed trip information

#### **Booking Management**
- `POST /api/agent/process-walk-in-booking` - Process walk-in booking
- `GET /api/agent/daily-sales` - Get real-time daily sales data

#### **Response Format**
```json
{
    "success": true,
    "data": {
        "trips": [...],
        "totalBookings": 25,
        "totalRevenue": 15000.00
    },
    "message": "Success"
}
```

## �🔒 Security Features

- **Input Validation**: All user inputs are validated and sanitized
- **CSRF Protection**: Laravel's built-in CSRF protection with token verification
- **Authentication**: Secure user authentication with role-based access control
- **Password Hashing**: Bcrypt password hashing with salt
- **SQL Injection Prevention**: Eloquent ORM with prepared statements
- **XSS Protection**: Output escaping and content security policies
- **API Security**: Token-based authentication for API endpoints
- **Session Management**: Secure session handling with encryption

## 📱 Mobile Responsiveness

The application is fully responsive and works on:
- **Desktop** (1920px and above)
- **Laptop** (1024px - 1919px)
- **Tablet** (768px - 1023px)
- **Mobile** (320px - 767px)

## 🚀 Production Deployment

### **For Production Environment:**

1. **Set environment to production**
```env
APP_ENV=production
APP_DEBUG=false
```

2. **Configure production database**
3. **Set up SSL certificate**
4. **Configure web server (Apache/Nginx)**
5. **Set up cron jobs for Laravel scheduler**
6. **Configure email settings for notifications**
7. **Set up backup procedures**

### **Performance Optimization:**
```bash
# Cache configuration
php artisan config:cache

# Cache routes
php artisan route:cache

# Cache views
php artisan view:cache

# Optimize autoloader
composer install --optimize-autoloader --no-dev
```

## � Performance & Optimization

### **Database Optimization**
- **Query Optimization**: Efficient database queries with proper indexing
- **Real-time Data**: Live sales analytics and booking status updates
- **Connection Pooling**: Optimized database connections for high traffic
- **Data Pagination**: Efficient handling of large datasets

### **Frontend Performance**
- **Asset Optimization**: Minified CSS/JS with Laravel Mix
- **Toast Notifications**: Professional Toastr system for better UX
- **Modal Workflows**: Smooth, non-blocking user interactions
- **Progressive Enhancement**: Core functionality works without JavaScript

## 📈 Business Benefits

### **For Bus Companies**
- **Increased Revenue**: Online + walk-in booking channels
- **Operational Efficiency**: Automated booking and real-time reporting
- **Customer Insights**: Live analytics for business decision making
- **Cost Reduction**: Reduced manual processes and paperwork

### **For Terminal Agents**
- **Modern Interface**: Professional walk-in booking terminal
- **Real-time Data**: Live sales reports and analytics
- **Efficient Workflow**: Modal-based booking process
- **Professional Tools**: Toast notifications and print functionality

## �📞 Support

For support and questions about GL Bus Reservation System v2.0:
- **GitHub Issues:** [Create an issue](https://github.com/doriemon267/GL_BUS/issues)
- **Documentation:** Check this README for detailed instructions
- **Email:** <EMAIL>
- **API Documentation:** Available at `/api/agent/*` endpoints
- **Feature Requests:** Submit via GitHub Issues

## 📄 License

This project is open-source software licensed under the [MIT license](https://opensource.org/licenses/MIT).

---

**GL Bus Reservation System v2.0** - Professional bus terminal management with modern walk-in booking and real-time analytics 🚌✨💼

*Featuring: Modern UI/UX • Real-time Sales Analytics • Professional Walk-in Terminal • Toast Notifications • Live Data Dashboard*