<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Ticket;
use App\Models\payment;
use App\Models\User;
use App\Models\Trip;
use Carbon\Carbon;

class GenerateSampleRevenue extends Command
{
    protected $signature = 'revenue:generate {--count=20 : Number of sample bookings to create}';
    protected $description = 'Generate sample revenue data for testing';

    public function handle()
    {
        $count = $this->option('count');
        
        $this->info("Generating {$count} sample bookings with revenue...");
        
        // Get existing data
        $users = User::all();
        $trips = Trip::all();
        
        if ($users->isEmpty() || $trips->isEmpty()) {
            $this->error('No users or trips found. Please create some first.');
            return 1;
        }
        
        $paymentMethods = ['gcash', 'maya', 'qrph'];
        $statuses = ['paid', 'pending'];
        
        $totalRevenue = 0;
        
        for ($i = 1; $i <= $count; $i++) {
            $user = $users->random();
            $trip = $trips->random();
            $price = rand(800, 2000);
            $status = $statuses[array_rand($statuses)];
            $paymentMethod = $paymentMethods[array_rand($paymentMethods)];
            
            // Create ticket
            $ticket = Ticket::create([
                'user_id' => $user->id,
                'trip_id' => $trip->id,
                'seat_number' => rand(1, 50),
                'price' => $price
            ]);
            
            // Create payment
            $payment = payment::create([
                'ticket_id' => $ticket->id,
                'amount' => $price,
                'payment_method' => $paymentMethod,
                'status' => $status,
                'payment_date' => $status === 'paid' ? Carbon::now()->subDays(rand(1, 90)) : null,
                'passenger_name' => $user->name,
                'passenger_email' => $user->email,
                'passenger_mobile' => '09' . rand(100000000, 999999999),
                'passenger_address' => 'Sample Address ' . $i
            ]);
            
            if ($status === 'paid') {
                $totalRevenue += $price;
            }
            
            $this->line("Created booking #{$ticket->id} - ₱{$price} ({$status})");
        }
        
        $this->info("\n✅ Successfully generated {$count} sample bookings!");
        $this->info("💰 Total Revenue Generated: ₱" . number_format($totalRevenue, 2));
        
        // Show summary
        $totalPaidPayments = payment::where('status', 'paid')->count();
        $totalRevenue = payment::where('status', 'paid')->sum('amount');
        $totalPendingPayments = payment::where('status', 'pending')->count();
        $totalPendingAmount = payment::where('status', 'pending')->sum('amount');
        
        $this->info("\n📊 Current System Summary:");
        $this->info("Paid Payments: {$totalPaidPayments} (₱" . number_format($totalRevenue, 2) . ")");
        $this->info("Pending Payments: {$totalPendingPayments} (₱" . number_format($totalPendingAmount, 2) . ")");
        
        return 0;
    }
}
