<?php

namespace App\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\User;
use App\Models\Ticket;

class UserBookedTicket
{
    use Dispatchable, SerializesModels;

    public $user;
    public $ticket;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, Ticket $ticket)
    {
        $this->user = $user;
        $this->ticket = $ticket;
    }
}
