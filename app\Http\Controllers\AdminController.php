<?php

namespace App\Http\Controllers;

use App\Models\Bus;
use App\Models\Route;
use App\Models\Trip;
use App\Models\User;
use App\Models\Ticket;
use App\Models\payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminController extends Controller
{
    public function __construct()
    {
        // Routes are already protected by adminAuth middleware in web.php
        // No additional middleware needed here
    }

    /**
     * Show the admin dashboard.
     */
    public function dashboard()
    {
        // Get dashboard statistics
        $totalBuses = Bus::count();
        $totalRoutes = Route::count();
        $totalUsers = User::count();
        $todaysTrips = Trip::whereHas('schedule', function($query) {
            $query->whereDate('trip_date', Carbon::today());
        })->count();
        $activeBuses = Bus::count();
        
        // Calculate today's revenue from payments table
        $todaysRevenue = payment::whereDate('created_at', Carbon::today())
            ->where('status', 'paid')
            ->sum('amount');

        // Get recent bookings
        $recentBookings = Ticket::with(['user', 'trip.route'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return view('admin.dashboard', compact(
            'totalBuses',
            'totalRoutes', 
            'totalUsers',
            'todaysTrips',
            'activeBuses',
            'todaysRevenue',
            'recentBookings'
        ));
    }

    /**
     * Show buses management page.
     */
    public function buses()
    {
        $buses = Bus::paginate(15);
        $routes = Route::all();
        $totalBuses = Bus::count();
        $activeBuses = Bus::count(); // All buses are considered active since no status column
        $inactiveBuses = 0; // No inactive buses without status column
        $maintenanceBuses = 0; // No maintenance buses without status column

        return view('admin.buses.index', compact(
            'buses',
            'routes',
            'totalBuses',
            'activeBuses', 
            'inactiveBuses',
            'maintenanceBuses'
        ));
    }

    /**
     * Show routes management page.
     */
    public function routes()
    {
        $routes = Route::withCount(['trips', 'buses'])->paginate(15);
        $totalRoutes = Route::count();
        $activeRoutes = Route::whereHas('trips', function($query) {
            $query->where('departure_time', '>=', Carbon::now());
        })->count();

        return view('admin.routes.index', compact(
            'routes',
            'totalRoutes',
            'activeRoutes'
        ));
    }

    /**
     * Show trips management page.
     */
    public function trips()
    {
        $trips = Trip::with(['route', 'bus', 'schedule'])
            ->join('schedule', 'trip.schedule_id', '=', 'schedule.id')
            ->orderBy('schedule.trip_date', 'desc')
            ->orderBy('schedule.start_time', 'desc')
            ->select('trip.*')
            ->paginate(15);
        
        $routes = Route::all();
        $buses = Bus::all();
        
        $totalTrips = Trip::count();
        $todaysTrips = Trip::whereHas('schedule', function($query) {
            $query->whereDate('trip_date', Carbon::today());
        })->count();
        $upcomingTrips = Trip::whereHas('schedule', function($query) {
            $query->where('trip_date', '>', Carbon::today())
                ->orWhere(function($q) {
                    $q->whereDate('trip_date', Carbon::today())
                      ->whereTime('start_time', '>', Carbon::now()->toTimeString());
                });
        })->count();
        $completedTrips = Trip::whereHas('schedule', function($query) {
            $query->where('trip_date', '<', Carbon::today())
                ->orWhere(function($q) {
                    $q->whereDate('trip_date', Carbon::today())
                      ->whereTime('start_time', '<=', Carbon::now()->toTimeString());
                });
        })->count();

        return view('admin.trips.index', compact(
            'trips',
            'routes',
            'buses',
            'totalTrips',
            'todaysTrips',
            'upcomingTrips',
            'completedTrips'
        ));
    }

    /**
     * Show fares management page.
     */
    public function fares()
    {
        $fares = DB::table('fares')
            ->join('routes', 'fares.route_id', '=', 'routes.id')
            ->select('fares.*', 'routes.origin', 'routes.destination')
            ->paginate(15);

        $routes = Route::all();
        $totalFares = DB::table('fares')->count();

        return view('admin.fares.index', compact(
            'fares',
            'routes', 
            'totalFares'
        ));
    }

    /**
     * Show reports page.
     */
    public function reports()
    {
        // Revenue reports from payments table
        $dailyRevenue = payment::selectRaw('DATE(created_at) as date, SUM(amount) as revenue')
            ->where('status', 'paid')
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->get();

        $monthlyRevenue = payment::selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, SUM(amount) as revenue')
            ->where('status', 'paid')
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        // Trip reports
        $tripStats = [
            'total_trips' => Trip::count(),
            'completed_trips' => Trip::whereHas('schedule', function($query) {
                $query->where('trip_date', '<', Carbon::today())
                    ->orWhere(function($q) {
                        $q->whereDate('trip_date', Carbon::today())
                          ->whereTime('start_time', '<=', Carbon::now()->toTimeString());
                    });
            })->count(),
            'upcoming_trips' => Trip::whereHas('schedule', function($query) {
                $query->where('trip_date', '>', Carbon::today())
                    ->orWhere(function($q) {
                        $q->whereDate('trip_date', Carbon::today())
                          ->whereTime('start_time', '>', Carbon::now()->toTimeString());
                    });
            })->count(),
            'cancelled_trips' => Trip::where('status', 'cancelled')->count(),
        ];

        // Popular routes
        $popularRoutes = Route::withCount(['trips' => function($query) {
            $query->whereHas('schedule', function($scheduleQuery) {
                $scheduleQuery->where('trip_date', '>=', Carbon::now()->subDays(30));
            });
        }])
        ->orderBy('trips_count', 'desc')
        ->limit(10)
        ->get();

        return view('admin.reports.index', compact(
            'dailyRevenue',
            'monthlyRevenue',
            'tripStats',
            'popularRoutes'
        ));
    }
}
