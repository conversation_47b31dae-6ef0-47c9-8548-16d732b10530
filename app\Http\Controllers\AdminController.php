<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\Bus;
use App\Models\Route;
use App\Models\Trip;
use App\Models\Schedule;
use App\Models\Ticket;
use App\Models\Role;

class AdminController extends Controller
{
    /**
     * Check if user has admin access
     */
    private function checkAdminAccess()
    {
        if (!Auth::check()) {
            abort(401, 'Unauthorized');
        }

        $user = Auth::user();
        $hasAdminRole = $user->roles()->where('type', 'admin')->exists();

        if (!$hasAdminRole) {
            abort(403, 'Access denied. Admin privileges required.');
        }
    }

    /**
     * Show the enhanced admin dashboard
     */
    public function dashboard()
    {
        $this->checkAdminAccess();

        // Get comprehensive statistics
        $totalUsers = User::count();
        $totalBuses = Bus::count();
        $totalRoutes = Route::count();
        $totalTrips = Trip::count();
        $availableTrips = Trip::available()->count();
        $expiredTrips = Trip::expired()->count();
        $totalTickets = Ticket::count();

        // Get user statistics by role
        $adminUsers = User::whereHas('roles', function($query) {
            $query->where('type', 'admin');
        })->count();

        $agentUsers = User::whereHas('roles', function($query) {
            $query->where('type', 'agent');
        })->count();

        $customerUsers = User::whereDoesntHave('roles')->orWhereHas('roles', function($query) {
            $query->where('type', 'customer');
        })->count();

        // Revenue data (mock for now)
        $todaysRevenue = $totalTickets * 150; // Assuming average ticket price
        $monthlyRevenue = $todaysRevenue * 30;
        $todaysTrips = $availableTrips; // Mock data

        // Active buses (all buses are considered active for now)
        $activeBuses = $totalBuses;

        return view('admin.dashboard', compact(
            'totalUsers',
            'totalBuses',
            'totalRoutes',
            'totalTrips',
            'availableTrips',
            'expiredTrips',
            'totalTickets',
            'adminUsers',
            'agentUsers',
            'customerUsers',
            'todaysRevenue',
            'monthlyRevenue',
            'activeBuses',
            'todaysTrips'
        ));
    }

    /**
     * Show buses management page
     */
    public function buses()
    {
        $this->checkAdminAccess();

        $buses = Bus::paginate(15);
        $routes = Route::all();
        $totalBuses = Bus::count();
        $activeBuses = Bus::count();

        return view('admin.buses.index', compact(
            'buses',
            'routes',
            'totalBuses',
            'activeBuses'
        ));
    }

    /**
     * Show routes management page
     */
    public function routes()
    {
        $this->checkAdminAccess();

        $routes = Route::paginate(15);
        $totalRoutes = Route::count();

        return view('admin.routes.index', compact(
            'routes',
            'totalRoutes'
        ));
    }

    /**
     * Show trips management page
     */
    public function trips()
    {
        $this->checkAdminAccess();

        $trips = Trip::with(['bus', 'route', 'schedule'])->paginate(15);
        $totalTrips = Trip::count();
        $availableTrips = Trip::available()->count();
        $expiredTrips = Trip::expired()->count();

        return view('admin.trips.index', compact(
            'trips',
            'totalTrips',
            'availableTrips',
            'expiredTrips'
        ));
    }

    /**
     * Show schedules management page
     */
    public function schedules()
    {
        $this->checkAdminAccess();

        $schedules = Schedule::paginate(15);
        $totalSchedules = Schedule::count();

        return view('admin.schedules.index', compact(
            'schedules',
            'totalSchedules'
        ));
    }

    /**
     * Show reports dashboard
     */
    public function reports()
    {
        $this->checkAdminAccess();

        // Get report data
        $totalRevenue = Ticket::count() * 150; // Mock calculation
        $totalBookings = Ticket::count();
        $totalTrips = Trip::count();
        $availableTrips = Trip::available()->count();
        $busUtilization = ($totalTrips > 0) ? ($availableTrips / $totalTrips) * 100 : 0;

        return view('admin.reports.index', compact(
            'totalRevenue',
            'totalBookings',
            'busUtilization'
        ));
    }

    /**
     * Show fares management page
     */
    public function fares()
    {
        $this->checkAdminAccess();

        // For now, just return a simple view
        return view('admin.fares.index');
    }
}
