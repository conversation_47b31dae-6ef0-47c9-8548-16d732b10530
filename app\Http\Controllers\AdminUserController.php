<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class AdminUserController extends Controller
{
    /**
     * Check if user has admin access
     */
    private function checkAdminAccess()
    {
        $user = Auth::user();
        if (!$user) {
            abort(401, 'Authentication required.');
        }
        
        $role = $user->roles->first();
        
        if (!$role || $role->type !== 'admin') {
            abort(403, 'Access denied. Admin role required.');
        }
    }

    /**
     * Show user management page
     */
    public function index()
    {
        $this->checkAdminAccess();

        $users = User::with('roles')->orderBy('created_at', 'desc')->paginate(10);
        $roles = Role::all();

        return view('admin.users.index', compact('users', 'roles'));
    }

    /**
     * Show individual user data
     */
    public function show(User $user)
    {
        $this->checkAdminAccess();

        return response()->json([
            'success' => true,
            'user' => $user->load('roles')
        ]);
    }

    /**
     * Create new user (agent, customer, etc.)
     */
    public function store(Request $request)
    {
        $this->checkAdminAccess();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:6',
            'phone' => 'nullable|string|max:20',
            'role_id' => 'required|exists:roles,id'
        ]);

        // Create user
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'email_verified_at' => now(),
        ]);

        // Assign role
        $user->roles()->attach($request->role_id);

        return response()->json([
            'success' => true,
            'message' => 'User created successfully!',
            'user' => $user->load('roles')
        ]);
    }

    /**
     * Update user
     */
    public function update(Request $request, $userId)
    {
        $this->checkAdminAccess();
        
        $user = User::findOrFail($userId);
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $userId,
            'phone' => 'nullable|string|max:20',
            'role_id' => 'required|exists:roles,id'
        ]);

        // Update user
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
        ]);

        // Update password if provided
        if ($request->password) {
            $user->update(['password' => Hash::make($request->password)]);
        }

        // Update role
        $user->roles()->sync([$request->role_id]);

        return response()->json([
            'success' => true,
            'message' => 'User updated successfully!',
            'user' => $user->load('roles')
        ]);
    }

    /**
     * Delete user
     */
    public function destroy($userId)
    {
        $this->checkAdminAccess();
        
        $user = User::findOrFail($userId);
        
        // Prevent deleting current admin
        if ($user->id === Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete your own account!'
            ], 400);
        }

        $user->delete();

        return response()->json([
            'success' => true,
            'message' => 'User deleted successfully!'
        ]);
    }

    /**
     * Quick create agent
     */
    public function createAgent(Request $request)
    {
        $this->checkAdminAccess();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'nullable|string|max:20'
        ]);

        // Get agent role
        $agentRole = Role::where('type', 'agent')->first();
        
        if (!$agentRole) {
            return response()->json([
                'success' => false,
                'message' => 'Agent role not found!'
            ], 400);
        }

        // Create agent with default password
        $defaultPassword = 'agent123';
        
        $agent = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($defaultPassword),
            'phone' => $request->phone,
            'email_verified_at' => now(),
        ]);

        // Assign agent role
        $agent->roles()->attach($agentRole->id);

        return response()->json([
            'success' => true,
            'message' => 'Agent created successfully!',
            'agent' => $agent->load('roles'),
            'default_password' => $defaultPassword
        ]);
    }
}
