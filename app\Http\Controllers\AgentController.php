<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Trip;
use App\Models\Ticket;
use App\Models\User;
use App\Models\payment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AgentController extends Controller
{
    /**
     * Check if user has agent or admin role
     */
    private function checkAgentAccess()
    {
        $user = Auth::user();
        if (!$user) {
            abort(401, 'Authentication required.');
        }

        $role = $user->roles->first();

        if (!$role || !in_array($role->type, ['admin', 'agent'])) {
            abort(403, 'Access denied. Agent or Admin role required.');
        }
    }

    /**
     * Show agent dashboard
     */
    public function dashboard()
    {
        $this->checkAgentAccess();

        $data = [
            'trips' => Trip::with('bus', 'route', 'schedule')->get(),
            'todayBookings' => Ticket::whereDate('created_at', today())->count(),
            'todayRevenue' => Ticket::whereDate('created_at', today())->sum('price'),
            'recentBookings' => Ticket::with('user', 'trip.route', 'payments')
                ->orderBy('created_at', 'desc')
                ->take(10)
                ->get()
        ];

        return view('agent.dashboard', compact('data'));
    }

    /**
     * Show walk-in booking form
     */
    public function walkInBooking($tripId)
    {
        $this->checkAgentAccess();

        $trip = Trip::with('bus', 'route', 'schedule')->findOrFail($tripId);
        
        // Get booked seats
        $bookedSeats = Ticket::where('trip_id', $tripId)
            ->whereHas('payments', function($q) {
                $q->where('status', '!=', 'cancelled');
            })
            ->pluck('seat_number')
            ->toArray();

        return view('agent.walk-in-booking', compact('trip', 'bookedSeats'));
    }

    /**
     * Process walk-in booking
     */
    public function processWalkInBooking(Request $request, $tripId)
    {
        $this->checkAgentAccess();

        $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'seat_numbers' => 'required|string',
            'payment_method' => 'required|in:cash,visa,master,gpay,gcash',
            'payment_reference' => 'nullable|string|max:255',
            'customer_id_number' => 'nullable|string|max:50',
            'cash_received' => 'nullable|numeric|min:0',
            'change_amount' => 'nullable|numeric|min:0',
            'total_amount' => 'nullable|numeric|min:0'
        ]);

        // Parse seat numbers
        $seatNumbers = array_map('intval', explode(',', $request->seat_numbers));
        $seatNumbers = array_filter($seatNumbers, function($seat) { return $seat > 0; });

        if (empty($seatNumbers)) {
            return response()->json(['error' => 'Please select at least one seat.'], 400);
        }

        if (count($seatNumbers) > 5) {
            return response()->json(['error' => 'You can only book up to 5 seats at once.'], 400);
        }

        $trip = Trip::with('bus', 'route')->findOrFail($tripId);

        // Validate seat numbers are within bus capacity
        foreach ($seatNumbers as $seatNumber) {
            if ($seatNumber > $trip->bus->seat) {
                return response()->json(['error' => "Invalid seat number {$seatNumber}. This bus only has {$trip->bus->seat} seats."], 400);
            }
        }

        try {
            DB::beginTransaction();

            // Check for double booking
            $conflictingSeats = Ticket::where('trip_id', $tripId)
                ->whereIn('seat_number', $seatNumbers)
                ->whereHas('payments', function($q) {
                    $q->where('status', '!=', 'cancelled');
                })
                ->lockForUpdate()
                ->pluck('seat_number')
                ->toArray();

            if (!empty($conflictingSeats)) {
                DB::rollback();
                return response()->json(['error' => 'Seats ' . implode(', ', $conflictingSeats) . ' are already booked. Please select different seats.'], 409);
            }

            // Create or find customer (for walk-in, we create a guest user record)
            $customer = User::firstOrCreate(
                ['email' => $request->customer_email ?: 'guest_' . time() . '@walkin.local'],
                [
                    'name' => $request->customer_name,
                    'phone' => $request->customer_phone,
                    'password' => bcrypt('guest_password_' . time()),
                    'email_verified_at' => now(),
                    'is_guest' => true
                ]
            );

            // Calculate price
            $fare = $trip->route->activeFare;
            $pricePerSeat = $fare ? $fare->calculateFare() : 100;

            $tickets = [];
            $payments = [];
            $totalAmount = 0;

            // Create tickets for each seat
            foreach ($seatNumbers as $seatNumber) {
                $ticket = Ticket::create([
                    'user_id' => $customer->id,
                    'trip_id' => $tripId,
                    'seat_number' => $seatNumber,
                    'price' => $pricePerSeat,
                    'booking_type' => 'walk_in',
                    'booked_by_agent' => Auth::id()
                ]);

                // Generate invoice number
                $invoiceNumber = 'INV-' . date('Ymd') . '-' . str_pad($ticket->id, 6, '0', STR_PAD_LEFT);

                // Prepare payment details
                $paymentDetails = [
                    'method' => $request->payment_method,
                    'reference' => $request->payment_reference ?? null,
                    'booking_date' => now()->toDateTimeString(),
                    'booking_type' => 'walk_in',
                    'agent_id' => Auth::id(),
                    'customer_name' => $request->customer_name,
                    'customer_phone' => $request->customer_phone,
                    'customer_email' => $request->customer_email,
                    'customer_id_number' => $request->customer_id_number,
                    'seats_booked' => count($seatNumbers),
                    'all_seats' => $seatNumbers
                ];

                // Add cash payment details if cash payment
                if ($request->payment_method === 'cash' && $request->cash_received) {
                    $paymentDetails['cash_received'] = floatval($request->cash_received);
                    $paymentDetails['change_amount'] = floatval($request->change_amount ?? 0);
                    $paymentDetails['total_amount'] = floatval($request->total_amount ?? $pricePerSeat);
                }

                $payment = payment::create([
                    'ticket_id' => $ticket->id,
                    'payment_method' => $request->payment_method,
                    'invoice_number' => $invoiceNumber,
                    'amount' => $pricePerSeat,
                    'status' => $request->payment_method === 'cash' ? 'completed' : 'pending',
                    'payment_details' => json_encode($paymentDetails)
                ]);

                $tickets[] = $ticket;
                $payments[] = $payment;
                $totalAmount += $pricePerSeat;
            }

            DB::commit();

            // Calculate seats left
            $seats_left = $trip->bus->seat - $trip->tickets()->whereHas('payments', function($q) {
                $q->where('status', '!=', 'cancelled');
            })->count();

            // Prepare response data
            $responseData = [
                'success' => true,
                'seats_left' => $seats_left,
                'tickets_count' => count($tickets),
                'seats_booked' => $seatNumbers,
                'total_amount' => $totalAmount,
                'customer_name' => $request->customer_name,
                'invoice_numbers' => array_column($payments, 'invoice_number'),
                'payment_method' => $request->payment_method,
                'message' => count($seatNumbers) > 1
                    ? 'Seats ' . implode(', ', $seatNumbers) . ' booked successfully for ' . $request->customer_name . '! Total: ₱' . number_format($totalAmount, 2)
                    : 'Seat ' . $seatNumbers[0] . ' booked successfully for ' . $request->customer_name . '! Amount: ₱' . number_format($totalAmount, 2)
            ];

            // Add cash payment details to response if cash payment
            if ($request->payment_method === 'cash' && $request->cash_received) {
                $responseData['cash_received'] = floatval($request->cash_received);
                $responseData['change_amount'] = floatval($request->change_amount ?? 0);
            }

            return response()->json($responseData);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Walk-in booking error: ' . $e->getMessage());
            return response()->json(['error' => 'An error occurred while processing the booking. Please try again.'], 500);
        }
    }

    /**
     * Get available seats for a trip
     */
    public function getAvailableSeats($tripId)
    {
        $this->checkAgentAccess();

        $trip = Trip::with('bus')->findOrFail($tripId);
        
        $bookedSeats = Ticket::where('trip_id', $tripId)
            ->whereHas('payments', function($q) {
                $q->where('status', '!=', 'cancelled');
            })
            ->pluck('seat_number')
            ->toArray();

        $totalSeats = $trip->bus->seat;
        $seatsLeft = $totalSeats - count($bookedSeats);

        return response()->json([
            'booked_seats' => $bookedSeats,
            'total_seats' => $totalSeats,
            'seats_left' => $seatsLeft
        ]);
    }

    /**
     * Print ticket
     */
    public function printTicket($ticketId)
    {
        $this->checkAgentAccess();

        $ticket = Ticket::with('user', 'trip.bus', 'trip.route', 'trip.schedule', 'payments')
            ->findOrFail($ticketId);

        return view('agent.print-ticket', compact('ticket'));
    }

    /**
     * Print receipt
     */
    public function printReceipt($ticketId)
    {
        $this->checkAgentAccess();

        $ticket = Ticket::with('user', 'trip.bus', 'trip.route', 'trip.schedule', 'payments')
            ->findOrFail($ticketId);

        $payment = $ticket->payments->first();

        if (!$payment) {
            abort(404, 'Payment not found for this ticket.');
        }

        return view('agent.receipt', compact('ticket', 'payment'));
    }

    /**
     * Complete a pending payment (mark as paid)
     */
    public function completePayment($paymentId)
    {
        $this->checkAgentAccess();

        try {
            $payment = payment::findOrFail($paymentId);

            // Check if payment is already completed
            if ($payment->status === 'paid') {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment is already marked as paid.'
                ]);
            }

            // Update payment status
            $payment->status = 'paid';
            $payment->payment_date = now();
            $payment->save();

            Log::info('Payment completed by agent', [
                'payment_id' => $paymentId,
                'agent_id' => Auth::id(),
                'amount' => $payment->amount,
                'ticket_id' => $payment->ticket_id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment completed successfully.',
                'payment' => [
                    'id' => $payment->id,
                    'status' => $payment->status,
                    'amount' => $payment->amount,
                    'payment_date' => $payment->payment_date->format('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error completing payment', [
                'payment_id' => $paymentId,
                'agent_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while completing the payment.'
            ], 500);
        }
    }

    /**
     * Get ticket details for modal display
     */
    public function getTicketDetails($ticketId)
    {
        try {
            $ticket = Ticket::with([
                'user',
                'trip.route',
                'trip.schedule',
                'trip.bus',
                'payments'
            ])->findOrFail($ticketId);

            return response()->json([
                'success' => true,
                'ticket' => $ticket
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ticket not found'
            ], 404);
        }
    }

    /**
     * Get today's available trips for agent booking
     */
    public function getTodaysTrips()
    {
        try {
            $trips = Trip::with(['bus', 'route', 'schedule'])
                ->whereHas('schedule', function($query) {
                    $query->whereDate('trip_date', today());
                })
                ->available()
                ->get()
                ->map(function($trip) {
                    $bookedSeats = Ticket::where('trip_id', $trip->id)
                        ->whereHas('payments', function($q) {
                            $q->where('status', '!=', 'cancelled');
                        })
                        ->count();

                    return [
                        'id' => $trip->id,
                        'route' => [
                            'start_point' => $trip->route->start_point,
                            'end_point' => $trip->route->end_point,
                        ],
                        'bus' => [
                            'name' => $trip->bus->name,
                            'bus_code' => $trip->bus->bus_code ?? 'N/A',
                        ],
                        'schedule' => [
                            'start_time' => $trip->schedule->formatted_start_time ?? $trip->schedule->start_time,
                            'end_time' => $trip->schedule->formatted_end_time ?? $trip->schedule->end_time,
                        ],
                        'total_seats' => $trip->bus->seat,
                        'booked_seats' => $bookedSeats,
                        'fare' => $trip->route->activeFare ? $trip->route->activeFare->base_fare : 1000.00,
                    ];
                });

            return response()->json([
                'success' => true,
                'trips' => $trips
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting today\'s trips: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error loading trips'
            ], 500);
        }
    }

    /**
     * Search trips for agent booking
     */
    public function searchTrips(Request $request)
    {
        try {
            $date = $request->get('date');
            $origin = $request->get('origin');
            $destination = $request->get('destination');

            $trips = Trip::with(['bus', 'route', 'schedule'])
                ->whereHas('schedule', function($query) use ($date) {
                    $query->whereDate('trip_date', $date);
                })
                ->whereHas('route', function($query) use ($origin, $destination) {
                    $query->where('start_point', $origin)
                          ->where('end_point', $destination);
                })
                ->available()
                ->get()
                ->map(function($trip) {
                    $bookedSeats = Ticket::where('trip_id', $trip->id)
                        ->whereHas('payments', function($q) {
                            $q->where('status', '!=', 'cancelled');
                        })
                        ->count();

                    return [
                        'id' => $trip->id,
                        'route' => [
                            'start_point' => $trip->route->start_point,
                            'end_point' => $trip->route->end_point,
                        ],
                        'bus' => [
                            'name' => $trip->bus->name,
                            'bus_code' => $trip->bus->bus_code ?? 'N/A',
                        ],
                        'schedule' => [
                            'start_time' => $trip->schedule->formatted_start_time ?? $trip->schedule->start_time,
                            'end_time' => $trip->schedule->formatted_end_time ?? $trip->schedule->end_time,
                        ],
                        'total_seats' => $trip->bus->seat,
                        'booked_seats' => $bookedSeats,
                        'fare' => $trip->route->activeFare ? $trip->route->activeFare->base_fare : 1000.00,
                    ];
                });

            return response()->json([
                'success' => true,
                'trips' => $trips
            ]);

        } catch (\Exception $e) {
            Log::error('Error searching trips: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error searching trips'
            ], 500);
        }
    }

    /**
     * Get trip seats for agent booking
     */
    public function getTripSeats($tripId)
    {
        try {
            $trip = Trip::with('bus')->findOrFail($tripId);

            $bookedSeats = Ticket::where('trip_id', $tripId)
                ->whereHas('payments', function($q) {
                    $q->where('status', '!=', 'cancelled');
                })
                ->pluck('seat_number')
                ->toArray();

            return response()->json([
                'success' => true,
                'seats' => $trip->bus->seat,
                'bookedSeats' => $bookedSeats
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting trip seats: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error loading seat information'
            ], 500);
        }
    }

    /**
     * Get trip details for agent booking
     */
    public function getTripDetailsForAgent($tripId)
    {
        try {
            $trip = Trip::with(['bus', 'route', 'schedule'])->findOrFail($tripId);

            return response()->json([
                'success' => true,
                'trip' => [
                    'id' => $trip->id,
                    'route' => [
                        'start_point' => $trip->route->start_point,
                        'end_point' => $trip->route->end_point,
                    ],
                    'bus' => [
                        'name' => $trip->bus->name,
                        'bus_code' => $trip->bus->bus_code ?? 'N/A',
                    ],
                    'schedule' => [
                        'start_time' => $trip->schedule->formatted_start_time ?? $trip->schedule->start_time,
                        'end_time' => $trip->schedule->formatted_end_time ?? $trip->schedule->end_time,
                    ],
                    'fare' => $trip->route->activeFare ? $trip->route->activeFare->base_fare : 1000.00,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting trip details: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error loading trip details'
            ], 500);
        }
    }

    /**
     * Process new walk-in booking
     */
    public function processWalkInBookingNew(Request $request)
    {
        try {
            $request->validate([
                'trip_id' => 'required|exists:trip,id',
                'seats' => 'required|array',
                'passenger.first_name' => 'required|string|max:255',
                'passenger.last_name' => 'required|string|max:255',
                'passenger.mobile' => 'required|string|max:20',
                'passenger.address' => 'required|string|max:500',
                'payment_method' => 'required|in:cash,gcash,maya',
                'booking_type' => 'required|in:today,advanced'
            ]);

            DB::beginTransaction();

            $trip = Trip::with(['route', 'bus'])->findOrFail($request->trip_id);
            $passenger = $request->passenger;
            $seats = $request->seats;

            // Check seat availability
            $bookedSeats = Ticket::where('trip_id', $request->trip_id)
                ->whereHas('payments', function($q) {
                    $q->where('status', '!=', 'cancelled');
                })
                ->pluck('seat_number')
                ->toArray();

            $unavailableSeats = array_intersect($seats, $bookedSeats);
            if (!empty($unavailableSeats)) {
                throw new \Exception('Seats ' . implode(', ', $unavailableSeats) . ' are no longer available');
            }

            // Calculate total amount
            $farePerSeat = $trip->route->activeFare ? $trip->route->activeFare->base_fare : 1000.00;
            $totalAmount = $farePerSeat * count($seats);

            // Create tickets for each seat
            $tickets = [];
            foreach ($seats as $seatNumber) {
                $ticket = Ticket::create([
                    'user_id' => Auth::id() ?? 1, // Use agent ID or default to user ID 1
                    'trip_id' => $request->trip_id,
                    'seat_number' => $seatNumber,
                    'price' => $farePerSeat,
                    'booking_type' => 'walk_in',
                    'booked_by_agent' => Auth::id() ?? 1
                ]);
                $tickets[] = $ticket;
            }

            // Create payment record
            $payment = Payment::create([
                'ticket_id' => $tickets[0]->id, // Primary ticket
                'amount' => $totalAmount,
                'payment_method' => $request->payment_method,
                'status' => $request->payment_method === 'cash' ? 'paid' : 'pending',
                'passenger_name' => $passenger['first_name'] . ' ' . $passenger['last_name'],
                'passenger_email' => $passenger['email'] ?? null,
                'passenger_mobile' => $passenger['mobile'],
                'passenger_address' => $passenger['address'],
                'booking_type' => 'one_way',
                'booking_details' => json_encode([
                    'agent_booking' => true,
                    'booking_type' => $request->booking_type,
                    'all_tickets' => collect($tickets)->pluck('id')->toArray(),
                    'seats' => $seats,
                    'booked_by_agent' => Auth::id()
                ])
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Walk-in booking completed successfully!',
                'ticket_id' => $tickets[0]->id,
                'payment_id' => $payment->id,
                'total_amount' => $totalAmount,
                'seats' => $seats
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Walk-in booking error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get daily sales data for agent dashboard
     */
    public function getDailySales()
    {
        try {
            // Start with basic counts
            $totalBookings = Ticket::whereDate('created_at', today())->count();

            // Total revenue today (paid payments) - handle if payment_date column doesn't exist
            $totalRevenue = 0;
            try {
                $totalRevenue = \App\Models\payment::where('status', 'paid')
                    ->whereDate('created_at', today()) // Use created_at if payment_date doesn't exist
                    ->sum('amount') ?: 0;
            } catch (\Exception $e) {
                Log::warning('Could not get revenue data: ' . $e->getMessage());
                $totalRevenue = 0;
            }

            // Walk-in bookings today
            $walkInBookings = Ticket::where('booking_type', 'walk_in')
                ->whereDate('created_at', today())
                ->count();

            // Online bookings today
            $onlineBookings = $totalBookings - $walkInBookings;

            // Peak hour analysis (simplified)
            $peakHour = 'No data';
            try {
                $hourlyBookings = Ticket::whereDate('created_at', today())
                    ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
                    ->groupBy('hour')
                    ->orderBy('count', 'desc')
                    ->first();

                if ($hourlyBookings) {
                    $peakHour = sprintf('%02d:00 - %02d:00', $hourlyBookings->hour, $hourlyBookings->hour + 1);
                }
            } catch (\Exception $e) {
                Log::warning('Could not get peak hour data: ' . $e->getMessage());
            }

            // Most popular route today (simplified)
            $mostPopularRoute = 'No data';
            try {
                $popularRouteData = DB::table('tickets')
                    ->join('trip', 'tickets.trip_id', '=', 'trip.id')
                    ->join('routes', 'trip.route_id', '=', 'routes.id')
                    ->whereDate('tickets.created_at', today())
                    ->select('routes.start_point', 'routes.end_point', DB::raw('COUNT(*) as booking_count'))
                    ->groupBy('routes.id', 'routes.start_point', 'routes.end_point')
                    ->orderBy('booking_count', 'desc')
                    ->first();

                if ($popularRouteData) {
                    $mostPopularRoute = $popularRouteData->start_point . ' → ' . $popularRouteData->end_point;
                }
            } catch (\Exception $e) {
                Log::warning('Could not get popular route data: ' . $e->getMessage());
            }

            // Average ticket price today
            $averageTicketPrice = 0;
            try {
                $averageTicketPrice = Ticket::whereDate('created_at', today())
                    ->avg('price') ?: 0;
            } catch (\Exception $e) {
                Log::warning('Could not get average price data: ' . $e->getMessage());
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'totalBookings' => $totalBookings,
                    'totalRevenue' => (float) $totalRevenue, // Return as number, not formatted string
                    'walkInBookings' => $walkInBookings,
                    'onlineBookings' => $onlineBookings,
                    'peakHour' => $peakHour,
                    'mostPopularRoute' => $mostPopularRoute,
                    'averageTicketPrice' => (float) $averageTicketPrice, // Return as number
                    'date' => today()->format('n/j/Y')
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get daily sales data: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Failed to load daily sales data: ' . $e->getMessage()
            ], 500);
        }
    }

}
