<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Trip;
use App\Models\Route;
use App\Models\Schedule;
use App\Models\Bus;
use App\Models\Ticket;
use App\Models\Payment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use App\Mail\BookingConfirmation;
use App\Services\SmsService;
use App\Services\PaymentGatewayService;

class BookingController extends Controller
{
    /**
     * Show the enhanced booking page
     */
    public function index()
    {
        $routes = Route::all();
        $origins = Route::distinct()->pluck('start_point')->sort();
        $destinations = Route::distinct()->pluck('end_point')->sort();
        $pageTitle = 'GL Bus - Book Your Trip';

        return view('booking.index', compact('routes', 'origins', 'destinations', 'pageTitle'));
    }

    /**
     * Search for available trips based on criteria
     */
    public function searchTrips(Request $request)
    {
        try {
            $origin = $request->get('origin');
            $destination = $request->get('destination');
            $date = $request->get('date');

            Log::info('Search trips request:', [
                'origin' => $origin,
                'destination' => $destination,
                'date' => $date,
                'parsed_date' => Carbon::parse($date)->format('Y-m-d')
            ]);

            $trips = Trip::with(['bus', 'route', 'schedule'])
                ->whereHas('route', function($q) use ($origin, $destination) {
                    $q->where('start_point', $origin)
                      ->where('end_point', $destination);
                })
                ->whereHas('schedule', function($q) use ($date) {
                    $q->whereDate('trip_date', $date);
                })
                ->available()
                ->get()
                ->each(function($trip) {
                    Log::info('Found trip:', [
                        'trip_id' => $trip->id,
                        'schedule_date' => $trip->schedule->trip_date,
                        'formatted_date' => Carbon::parse($trip->schedule->trip_date)->format('Y-m-d')
                    ]);
                })
                ->map(function($trip) {
                    // Get fare for this route
                    $fare = $trip->route->activeFare ? $trip->route->activeFare->base_fare : 1000.00;

                    return [
                        'id' => $trip->id,
                        'route' => [
                            'origin' => $trip->route->start_point,
                            'destination' => $trip->route->end_point,
                            'price' => $fare
                        ],
                        'schedule' => [
                            'date' => Carbon::parse($trip->schedule->trip_date)->format('m/d/Y'),
                            'departure_time' => $trip->schedule->formatted_start_time
                        ],
                        'bus' => [
                            'seat' => $trip->bus->seat,
                            'class' => $trip->bus->bus_code ?? 'Regular'
                        ],
                        'available_seats' => $trip->available_seats
                    ];
                });

            return response()->json([
                'success' => true,
                'trips' => $trips
            ]);

        } catch (\Exception $e) {
            Log::error('Error searching trips: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error searching for trips'
            ], 500);
        }
    }

    /**
     * Get trip details including booked seats
     */
    public function getTripDetails($tripId)
    {
        try {
            $trip = Trip::with(['bus', 'route', 'schedule'])->findOrFail($tripId);
            
            // Get booked seats for this trip
            $bookedSeats = Ticket::where('trip_id', $tripId)
                ->whereHas('payments', function($q) {
                    $q->where('status', '!=', 'cancelled');
                })
                ->pluck('seat_number')
                ->toArray();

            return response()->json([
                'success' => true,
                'trip' => [
                    'id' => $trip->id,
                    'route' => [
                        'origin' => $trip->route->start_point,
                        'destination' => $trip->route->end_point,
                        'price' => $trip->route->activeFare ? $trip->route->activeFare->base_fare : 1000.00
                    ],
                    'schedule' => [
                        'date' => Carbon::parse($trip->schedule->trip_date)->format('m/d/Y'),
                        'departure_time' => $trip->schedule->formatted_start_time
                    ],
                    'bus' => [
                        'seat' => $trip->bus->seat,
                        'class' => $trip->bus->bus_code ?? 'Regular'
                    ]
                ],
                'bookedSeats' => array_map('intval', $bookedSeats)
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting trip details: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error loading trip details'
            ], 500);
        }
    }

    /**
     * Process the complete booking
     */
    public function processBooking(Request $request)
    {
        try {
            // Check booking type
            $bookingType = $request->get('booking_type', 'one_way');

            if ($bookingType === 'round_trip') {
                return $this->processRoundTripBooking($request);
            }

            // One-way booking validation
            $request->validate([
                'trip_id' => 'required|exists:trip,id',
                'passenger_data' => 'required|array',
                'passenger_data.first_name' => 'required|string|max:255',
                'passenger_data.last_name' => 'required|string|max:255',
                'passenger_data.email' => 'required|email|max:255',
                'passenger_data.mobile' => 'required|string|max:20',
                'passenger_data.city' => 'required|string|max:255',
                'passenger_data.address' => 'required|string|max:500',
                'seat_numbers' => 'required|string',
                'payment_method' => 'required|in:visa,master,gpay,gcash,cash,maya,qrph',
                'payment_reference' => 'nullable|string|max:255'
            ]);

            DB::beginTransaction();

            $trip = Trip::findOrFail($request->trip_id);
            $seatNumbers = explode(',', $request->seat_numbers);
            $passengerData = $request->passenger_data;
            
            // Check if seats are still available
            $bookedSeats = Ticket::where('trip_id', $request->trip_id)
                ->whereHas('payments', function($q) {
                    $q->where('status', '!=', 'cancelled');
                })
                ->pluck('seat_number')
                ->toArray();

            foreach ($seatNumbers as $seatNumber) {
                if (in_array($seatNumber, $bookedSeats)) {
                    throw new \Exception("Seat {$seatNumber} is no longer available");
                }
            }

            $tickets = [];
            $totalAmount = 0;

            // Get the fare for this route
            $fare = $trip->route->activeFare ? $trip->route->activeFare->base_fare : 1000.00;

            // Create tickets for each seat
            foreach ($seatNumbers as $seatNumber) {
                $ticket = Ticket::create([
                    'user_id' => Auth::id(),
                    'trip_id' => $request->trip_id,
                    'seat_number' => trim($seatNumber),
                    'price' => $fare,
                    'booking_type' => 'online',  // Set as online booking
                    'booked_by_agent' => null    // No agent for online bookings
                ]);

                $tickets[] = $ticket;
                $totalAmount += $fare;
            }

            // Add reservation fee per passenger
            $reservationFee = 50.00 * count($seatNumbers);
            $totalAmount += $reservationFee;

            // Create payment record
            $payment = Payment::create([
                'ticket_id' => $tickets[0]->id, // Link to first ticket
                'amount' => $totalAmount,
                'payment_method' => $request->payment_method,
                'reference_number' => $request->payment_reference,
                'status' => 'pending', // All payments start as pending
                'passenger_name' => $passengerData['first_name'] . ' ' . $passengerData['last_name'],
                'passenger_email' => $passengerData['email'],
                'passenger_mobile' => $passengerData['mobile'],
                'passenger_address' => $passengerData['address'] . ', ' . $passengerData['city']
            ]);

            // Link all tickets to this payment
            foreach ($tickets as $ticket) {
                if ($ticket->id !== $tickets[0]->id) {
                    Payment::create([
                        'ticket_id' => $ticket->id,
                        'amount' => $fare,
                        'payment_method' => $request->payment_method,
                        'reference_number' => $request->payment_reference,
                        'status' => $request->payment_method === 'cash' ? 'pending' : 'paid',
                        'passenger_name' => $passengerData['first_name'] . ' ' . $passengerData['last_name'],
                        'passenger_email' => $passengerData['email'],
                        'passenger_mobile' => $passengerData['mobile'],
                        'passenger_address' => $passengerData['address'] . ', ' . $passengerData['city']
                    ]);
                }
            }

            // Send email and SMS notifications
            try {
                // Send email confirmation
                Mail::to($passengerData['email'])->send(new BookingConfirmation($payment, $tickets, $passengerData));

                // Send SMS confirmation
                $smsService = new SmsService();
                $smsData = [
                    'booking_id' => str_pad($payment->id, 6, '0', STR_PAD_LEFT),
                    'route' => $trip->route->start_point . ' → ' . $trip->route->end_point,
                    'date' => $trip->schedule->trip_date ? Carbon::parse($trip->schedule->trip_date)->format('M d, Y') : 'TBD',
                    'time' => Carbon::parse($trip->schedule->start_time)->format('g:i A'),
                    'seats' => implode(', ', $seatNumbers),
                    'amount' => number_format($totalAmount, 2),
                    'payment_method' => strtoupper($request->payment_method),
                    'reference_number' => $request->payment_reference
                ];

                $smsService->sendBookingConfirmation($passengerData['mobile'], $smsData);

                Log::info('Notifications sent', [
                    'email' => $passengerData['email'],
                    'mobile' => $passengerData['mobile'],
                    'booking_id' => $payment->id
                ]);

            } catch (\Exception $e) {
                Log::error('Error sending notifications: ' . $e->getMessage(), [
                    'booking_id' => $payment->id,
                    'email' => $passengerData['email'],
                    'mobile' => $passengerData['mobile']
                ]);
                // Don't fail the booking if notifications fail
            }

            DB::commit();

            Log::info('Booking processed successfully', [
                'user_id' => Auth::id(),
                'trip_id' => $request->trip_id,
                'seats' => $seatNumbers,
                'total_amount' => $totalAmount
            ]);

            // Process payment through gateway
            $paymentGateway = new PaymentGatewayService();
            $paymentResult = $paymentGateway->processPayment(
                $request->payment_method,
                $totalAmount,
                $passengerData,
                $payment
            );

            if ($paymentResult['redirect_required']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Booking created successfully! Redirecting to payment gateway...',
                    'booking_id' => $payment->id,
                    'total_amount' => $totalAmount,
                    'payment_gateway' => $paymentResult,
                    'redirect_url' => $paymentResult['payment_url'] ?? null,
                    'notifications' => [
                        'email_sent' => $passengerData['email'],
                        'sms_sent' => $passengerData['mobile']
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => true,
                    'message' => 'Booking processed successfully! Check your email and SMS for confirmation.',
                    'booking_id' => $payment->id,
                    'total_amount' => $totalAmount,
                    'payment_gateway' => $paymentResult,
                    'notifications' => [
                        'email_sent' => $passengerData['email'],
                        'sms_sent' => $passengerData['mobile']
                    ]
                ]);
            }

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error processing booking: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process round trip booking
     */
    private function processRoundTripBooking(Request $request)
    {
        // Round trip validation
        $request->validate([
            'outbound_trip_id' => 'required|exists:trip,id',
            'return_trip_id' => 'required|exists:trip,id',
            'passenger_data' => 'required|array',
            'passenger_data.first_name' => 'required|string|max:255',
            'passenger_data.last_name' => 'required|string|max:255',
            'passenger_data.email' => 'required|email|max:255',
            'passenger_data.mobile' => 'required|string|max:20',
            'passenger_data.city' => 'required|string|max:255',
            'passenger_data.address' => 'required|string|max:500',
            'outbound_seats' => 'required|string',
            'return_seats' => 'required|string',
            'payment_method' => 'required|in:visa,master,gpay,gcash,cash,maya,qrph',
            'payment_reference' => 'nullable|string|max:255',
            'total_amount' => 'required|numeric|min:0'
        ]);

        DB::beginTransaction();

        try {
            $outboundTrip = Trip::findOrFail($request->outbound_trip_id);
            $returnTrip = Trip::findOrFail($request->return_trip_id);
            $outboundSeats = explode(',', $request->outbound_seats);
            $returnSeats = explode(',', $request->return_seats);
            $passengerData = $request->passenger_data;

            // Check seat availability for both trips
            $this->checkSeatAvailability($request->outbound_trip_id, $outboundSeats);
            $this->checkSeatAvailability($request->return_trip_id, $returnSeats);

            // Create outbound tickets for all selected seats
            $outboundTickets = [];
            foreach ($outboundSeats as $seatNumber) {
                $outboundTickets[] = Ticket::create([
                    'user_id' => Auth::id() ?? 1,
                    'trip_id' => $request->outbound_trip_id,
                    'seat_number' => $seatNumber,
                    'price' => $outboundTrip->route->activeFare ? $outboundTrip->route->activeFare->base_fare : 1000.00,
                    'booking_type' => 'online'
                ]);
            }

            // Create return tickets for all selected seats
            $returnTickets = [];
            foreach ($returnSeats as $seatNumber) {
                $returnTickets[] = Ticket::create([
                    'user_id' => Auth::id() ?? 1,
                    'trip_id' => $request->return_trip_id,
                    'seat_number' => $seatNumber,
                    'price' => $returnTrip->route->activeFare ? $returnTrip->route->activeFare->base_fare : 1000.00,
                    'booking_type' => 'online'
                ]);
            }

            // Use the first outbound ticket as the primary ticket for payment
            $primaryTicket = $outboundTickets[0];

            // Create combined payment record
            $payment = Payment::create([
                'ticket_id' => $primaryTicket->id, // Primary ticket
                'amount' => $request->total_amount,
                'payment_method' => $request->payment_method,
                'status' => 'pending',
                'passenger_name' => $passengerData['first_name'] . ' ' . $passengerData['last_name'],
                'passenger_email' => $passengerData['email'],
                'passenger_mobile' => $passengerData['mobile'],
                'passenger_address' => $passengerData['address'] . ', ' . $passengerData['city'],
                'booking_type' => 'round_trip',
                'booking_details' => json_encode([
                    'outbound_trip_id' => $request->outbound_trip_id,
                    'return_trip_id' => $request->return_trip_id,
                    'outbound_seats' => $outboundSeats,
                    'return_seats' => $returnSeats,
                    'outbound_ticket_ids' => collect($outboundTickets)->pluck('id')->toArray(),
                    'return_ticket_ids' => collect($returnTickets)->pluck('id')->toArray(),
                    'total_seats' => count($outboundSeats) + count($returnSeats)
                ])
            ]);

            // Process payment through gateway
            $paymentGateway = new PaymentGatewayService();
            $paymentResult = $paymentGateway->processPayment(
                $request->payment_method,
                $request->total_amount,
                $passengerData,
                $payment
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Round trip booking created successfully!',
                'booking_id' => $payment->id,
                'total_amount' => $request->total_amount,
                'payment_gateway' => $paymentResult,
                'redirect_url' => $paymentResult['payment_url'] ?? null,
                'booking_type' => 'round_trip'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Round trip booking error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Check seat availability for a trip
     */
    private function checkSeatAvailability($tripId, $seatNumbers)
    {
        // Get booked seats for this trip from payment records
        $bookedSeats = Payment::where('status', '!=', 'cancelled')
            ->whereHas('ticket', function($q) use ($tripId) {
                $q->where('trip_id', $tripId);
            })
            ->with('ticket')
            ->get()
            ->pluck('ticket.seat_number')
            ->filter()
            ->toArray();

        $unavailableSeats = array_intersect($seatNumbers, $bookedSeats);

        if (!empty($unavailableSeats)) {
            throw new \Exception('Seats ' . implode(', ', $unavailableSeats) . ' are no longer available');
        }
    }

    /**
     * Show booking confirmation page
     */
    public function showConfirmation($bookingId)
    {
        try {
            $payment = Payment::with(['ticket.trip.route', 'ticket.trip.schedule', 'ticket.trip.bus'])
                ->where('id', $bookingId)
                ->whereHas('ticket', function($q) {
                    $q->where('user_id', Auth::id());
                })
                ->firstOrFail();

            $pageTitle = 'Booking Confirmation';

            return view('booking.confirmation', compact('payment', 'pageTitle'));

        } catch (\Exception $e) {
            Log::error('Error loading booking confirmation: ' . $e->getMessage());
            return redirect()->route('home')->with('error', 'Booking not found');
        }
    }
}
