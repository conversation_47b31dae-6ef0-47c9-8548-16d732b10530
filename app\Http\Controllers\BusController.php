<?php

namespace App\Http\Controllers;

use App\Http\Requests\BusRequest;
use App\Http\Requests\Route as RequestR;
use App\Http\Requests\ScheduleRequest;
use App\Models\Route;
use App\Models\Bus;
use App\Models\Schedule;
use App\Traits\ApiResponse;
use App\Http\Resources\BusResource;
use App\Http\Resources\RouteResource;
use App\Http\Resources\ScheduleResource;
use Illuminate\Support\Facades\Log;


class BusController extends Controller
{
    
use ApiResponse;
    public function store(BusRequest $request){
        try {
            $bus = Bus::create($request->all());

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Bus has been created successfully',
                    'data' => new BusResource($bus)
                ], 201);
            }

            return redirect()->back()->with('success', 'Bus has been created successfully');
        }
        catch (\Exception $e) {
            Log::error($e);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Something went wrong'
                ], 500);
            }

            return redirect()->back()->with('error', 'Something went wrong');
        }
    }

    public function store_route(RequestR $request){
        try {
            $route = Route::create($request->all());

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Route has been created successfully',
                    'data' => new RouteResource($route)
                ], 201);
            }

            return redirect()->back()->with('success', 'Route has been created successfully');
        }
        catch (\Exception $e) {
            Log::error($e);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Something went wrong'
                ], 500);
            }

            return redirect()->back()->with('error', 'Something went wrong');
        }
    }

    public function store_schedule(ScheduleRequest $request){
        try {
            $schedule = Schedule::create($request->all());

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Schedule has been created successfully',
                    'data' => new ScheduleResource($schedule)
                ], 201);
            }

            return redirect()->back()->with('success', 'Schedule has been created successfully');
        }
        catch (\Exception $e) {
            Log::error($e);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Something went wrong'
                ], 500);
            }

            return redirect()->back()->with('error', 'Something went wrong');
        }
    }
}
