<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Fare;
use App\Models\Route;

class FareController extends Controller
{
    public function index()
    {
        $fares = Fare::with('route')->orderBy('created_at', 'desc')->get();
        $routes = Route::all();

        return response()->json([
            'fares' => $fares,
            'routes' => $routes
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'route_id' => 'required|exists:routes,id',
            'base_fare' => 'required|numeric|min:0',
            'per_km_rate' => 'nullable|numeric|min:0',
            'minimum_fare' => 'nullable|numeric|min:0',
            'maximum_fare' => 'nullable|numeric|min:0',
            'description' => 'nullable|string|max:255'
        ]);

        // Deactivate existing active fare for this route
        Fare::where('route_id', $request->route_id)
            ->where('is_active', true)
            ->update(['is_active' => false]);

        $fare = Fare::create([
            'route_id' => $request->route_id,
            'base_fare' => $request->base_fare,
            'per_km_rate' => $request->per_km_rate ?? 0,
            'minimum_fare' => $request->minimum_fare,
            'maximum_fare' => $request->maximum_fare,
            'description' => $request->description,
            'is_active' => true
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Fare created successfully',
            'fare' => $fare->load('route')
        ]);
    }

    public function update(Request $request, Fare $fare)
    {
        $request->validate([
            'base_fare' => 'required|numeric|min:0',
            'per_km_rate' => 'nullable|numeric|min:0',
            'minimum_fare' => 'nullable|numeric|min:0',
            'maximum_fare' => 'nullable|numeric|min:0',
            'description' => 'nullable|string|max:255'
        ]);

        $fare->update([
            'base_fare' => $request->base_fare,
            'per_km_rate' => $request->per_km_rate ?? 0,
            'minimum_fare' => $request->minimum_fare,
            'maximum_fare' => $request->maximum_fare,
            'description' => $request->description
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Fare updated successfully',
            'fare' => $fare->load('route')
        ]);
    }

    public function destroy(Fare $fare)
    {
        $fare->delete();

        return response()->json([
            'success' => true,
            'message' => 'Fare deleted successfully'
        ]);
    }

    public function activate(Fare $fare)
    {
        // Deactivate other fares for this route
        Fare::where('route_id', $fare->route_id)
            ->where('id', '!=', $fare->id)
            ->update(['is_active' => false]);

        $fare->update(['is_active' => true]);

        return response()->json([
            'success' => true,
            'message' => 'Fare activated successfully'
        ]);
    }

    public function show(Fare $fare)
    {
        return response()->json([
            'success' => true,
            'fare' => $fare->load('route')
        ]);
    }

    public function getFareForRoute($routeId)
    {
        $fare = Fare::where('route_id', $routeId)
                   ->where('is_active', true)
                   ->first();

        if (!$fare) {
            return response()->json([
                'success' => false,
                'message' => 'No active fare found for this route'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'fare' => $fare
        ]);
    }
}
