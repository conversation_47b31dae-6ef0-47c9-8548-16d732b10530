<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Trip;
use App\Models\Route;
use App\Models\Schedule;
use App\Models\Bus;
use App\Models\Ticket;
use Illuminate\Support\Facades\Auth;
class HomeController extends Controller
{


    public function index(){
        // Only show available (non-expired) trips
        $trips = Trip::with('bus','route','schedule')
            ->available()
            ->get();

        $tickets = Ticket::with('trip','payments')->getUserPendingTicket(Auth::user()->id);
        $pageTitle = 'Book Your Trip';
        return view('home',compact('trips','tickets','pageTitle'));
    }

    public function admin()
    {
        $data=[
            'users' => \App\Models\User::all(),
            'routes' => Route::all(),
            'schedules' => Schedule::all(),
            'buses' => Bus::all(),
            'trips' => Trip::with('bus','route','schedule')->get(), // Show all trips for admin
            'available_trips' => Trip::with('bus','route','schedule')->available()->get(),
            'expired_trips' => Trip::with('bus','route','schedule')->expired()->get(),
            'tickets' => Ticket::with('trip')->get()
        ];

        return view('admin',compact('data'));
    }
    public function search(Request $request)
    {
        $validated = $request->validate([
            'search' => [
                'required',
                function ($attribute, $value, $fail) use ($request) {
                    if ($request->input('filter') === 'schedule' && !strtotime($value)) {
                        $fail('The search value must be a valid time for schedule filtering.');
                    }
                }
            ],
            'filter' => 'required|string|in:route,bus,schedule'
        ]);
    
        $search = $validated['search'];
        $filter = $validated['filter'];
    
        $trips = Trip::search($search, $filter)
            ->with('bus', 'route', 'schedule')
            ->get();
    
        return response()->json(['status' => 'success', 'data' => $trips ?? []], 200);
    }
    

    protected function requestFilter($filter, $search)
    {
        switch ($filter) {
            case 'route':
            case 'bus':
                if (!is_string($search)) {
                    return false;
                }
                return true;
    
            case 'schedule':
                if (strtotime($search) === false) {
                    return false;
                }
                return true;
    
            default:
                return false;
        }
    }

    public function searchResult(Trip $trip){
        trip::with('bus','route','schedule')->get();
        return view('SearchResult',compact('trip'));
    }
    
}
