<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentGatewayController extends Controller
{
    /**
     * Show payment gateway page
     */
    public function showGateway(Request $request)
    {
        $method = $request->get('method');
        $amount = $request->get('amount');
        $ref = $request->get('ref');

        Log::info('Payment gateway accessed', [
            'method' => $method,
            'amount' => $amount,
            'ref' => $ref
        ]);

        // Find payment by reference
        $payment = Payment::where('reference_number', $ref)->first();

        if (!$payment) {
            Log::warning('Payment not found', ['ref' => $ref]);
            return redirect()->route('home')->with('error', 'Payment not found');
        }

        Log::info('Payment found', [
            'payment_id' => $payment->id,
            'status' => $payment->status,
            'amount' => $payment->amount
        ]);

        $pageTitle = 'Payment Gateway - ' . strtoupper($method);

        return view('payment.gateway-simple', compact('method', 'amount', 'ref', 'payment', 'pageTitle'));
    }

    /**
     * Process payment callback
     */
    public function processCallback(Request $request)
    {
        try {
            $transactionRef = $request->get('ref');
            $status = $request->get('status', 'success'); // success, failed, cancelled
            $gatewayRef = $request->get('gateway_ref');

            $payment = Payment::where('reference_number', $transactionRef)->first();
            
            if (!$payment) {
                return response()->json(['error' => 'Payment not found'], 404);
            }

            if ($status === 'success') {
                $payment->update([
                    'status' => 'paid',
                    'payment_date' => now(),
                    'payment_details' => json_encode(array_merge(
                        json_decode($payment->payment_details, true) ?? [],
                        [
                            'gateway_reference' => $gatewayRef,
                            'completed_at' => now()->toDateTimeString(),
                            'callback_data' => $request->all()
                        ]
                    ))
                ]);

                Log::info('Payment completed successfully', [
                    'payment_id' => $payment->id,
                    'transaction_ref' => $transactionRef,
                    'gateway_ref' => $gatewayRef
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Payment completed successfully',
                    'redirect_url' => route('booking.confirmation', $payment->id)
                ]);
            } else {
                $payment->update([
                    'status' => 'cancelled',
                    'payment_details' => json_encode(array_merge(
                        json_decode($payment->payment_details, true) ?? [],
                        [
                            'cancelled_at' => now()->toDateTimeString(),
                            'cancellation_reason' => $status,
                            'callback_data' => $request->all()
                        ]
                    ))
                ]);

                Log::warning('Payment cancelled or failed', [
                    'payment_id' => $payment->id,
                    'transaction_ref' => $transactionRef,
                    'status' => $status
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Payment was cancelled or failed',
                    'redirect_url' => route('home')
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Payment callback error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing payment'
            ], 500);
        }
    }

    /**
     * Handle payment success
     */
    public function paymentSuccess(Request $request)
    {
        $ref = $request->get('ref');
        $payment = Payment::where('reference_number', $ref)->first();
        
        if (!$payment) {
            return redirect()->route('home')->with('error', 'Payment not found');
        }

        // Mark as paid
        $payment->update([
            'status' => 'paid',
            'payment_date' => now()
        ]);

        return redirect()->route('booking.confirmation', $payment->id)
            ->with('success', 'Payment completed successfully!');
    }

    /**
     * Handle payment cancellation
     */
    public function paymentCancel(Request $request)
    {
        $ref = $request->get('ref');
        $payment = Payment::where('reference_number', $ref)->first();
        
        if ($payment) {
            $payment->update(['status' => 'cancelled']);
        }

        return redirect()->route('home')
            ->with('error', 'Payment was cancelled. Please try again.');
    }

    /**
     * Generate QR code for QRPh payments
     */
    public function generateQRCode(Request $request)
    {
        $ref = $request->get('ref');
        $payment = Payment::where('reference_number', $ref)->first();
        
        if (!$payment) {
            return response()->json(['error' => 'Payment not found'], 404);
        }

        $paymentDetails = json_decode($payment->payment_details, true);
        
        // Generate QR code data
        $qrData = [
            'merchant' => 'GL Bus Lines',
            'amount' => $payment->amount,
            'reference' => $payment->reference_number,
            'description' => 'Bus Ticket Payment'
        ];

        // In production, you would generate actual QR code image
        $qrString = base64_encode(json_encode($qrData));

        return response()->json([
            'success' => true,
            'qr_data' => $qrString,
            'amount' => $payment->amount,
            'reference' => $payment->reference_number
        ]);
    }

    /**
     * Check payment status
     */
    public function checkPaymentStatus(Request $request)
    {
        $ref = $request->get('ref');
        $payment = Payment::where('reference_number', $ref)->first();
        
        if (!$payment) {
            return response()->json(['error' => 'Payment not found'], 404);
        }

        return response()->json([
            'success' => true,
            'status' => $payment->status,
            'amount' => $payment->amount,
            'payment_date' => $payment->payment_date,
            'reference' => $payment->reference_number
        ]);
    }
}
