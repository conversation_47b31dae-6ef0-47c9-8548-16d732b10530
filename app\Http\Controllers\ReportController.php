<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Ticket;
use App\Models\payment;
use App\Models\Trip;
use App\Models\Bus;
use App\Models\Route;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
// PDF functionality will be handled by browser print


class ReportController extends Controller
{
    public function index()
    {
        // Check if user has admin or agent role
        $user = Auth::user();
        $role = $user ? $user->roles->first() : null;

        if (!$role || !in_array($role->type, ['admin', 'agent'])) {
            abort(403, 'Access denied. Admin or Agent role required to view reports.');
        }

        // Get summary statistics
        $totalBookings = Ticket::count();
        $totalRevenue = payment::where('status', 'paid')->sum('amount');
        $pendingPayments = payment::where('status', 'pending')->count();
        $cancelledBookings = payment::where('status', 'cancelled')->count();
        $totalTrips = Trip::count();
        $totalBuses = Bus::count();
        $totalUsers = User::whereHas('roles', function($query) {
            $query->where('type', 'user');
        })->count(); // Count only regular users

        // Get monthly revenue data for chart (last 12 months)
        $monthlyRevenue = payment::where('status', 'paid')
            ->where('payment_date', '>=', Carbon::now()->subMonths(12))
            ->selectRaw('MONTH(payment_date) as month, YEAR(payment_date) as year, SUM(amount) as revenue')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        // Get popular routes (top 5)
        $popularRoutes = Route::withCount(['trips' => function($query) {
                $query->whereHas('tickets.payments', function($q) {
                    $q->where('status', '!=', 'cancelled');
                });
            }])
            ->orderBy('trips_count', 'desc')
            ->take(5)
            ->get();

        // Get recent bookings
        $recentBookings = Ticket::with(['user', 'trip.bus', 'trip.route', 'payments'])
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        // Get payment method distribution
        $paymentMethods = payment::where('status', 'paid')
            ->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as total')
            ->groupBy('payment_method')
            ->get();

        $data = compact(
            'totalBookings',
            'totalRevenue',
            'pendingPayments',
            'cancelledBookings',
            'totalTrips',
            'totalBuses',
            'totalUsers',
            'monthlyRevenue',
            'popularRoutes',
            'recentBookings',
            'paymentMethods'
        );

        return view('reports.dashboard', compact('data'));
    }

    /**
     * Generate Monthly Revenue Report PDF
     */
    public function monthlyRevenueReport($year = null, $month = null)
    {
        $this->checkAccess();

        $year = $year ?? date('Y');
        $month = $month ?? date('m');

        $startDate = Carbon::create($year, $month, 1)->startOfMonth();
        $endDate = Carbon::create($year, $month, 1)->endOfMonth();

        $data = [
            'title' => 'Monthly Revenue Report',
            'period' => $startDate->format('F Y'),
            'generated_at' => now()->format('F d, Y h:i A'),
            'generated_by' => Auth::user()->name,

            // Revenue Summary
            'total_revenue' => payment::where('status', 'paid')
                ->whereBetween('payment_date', [$startDate, $endDate])
                ->sum('amount'),

            'total_bookings' => Ticket::whereHas('payments', function($q) use ($startDate, $endDate) {
                $q->where('status', 'paid')
                  ->whereBetween('payment_date', [$startDate, $endDate]);
            })->count(),

            'pending_payments' => payment::where('status', 'pending')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('amount'),

            // Daily breakdown
            'daily_revenue' => payment::where('status', 'paid')
                ->whereBetween('payment_date', [$startDate, $endDate])
                ->selectRaw('DATE(payment_date) as date, SUM(amount) as revenue, COUNT(*) as bookings')
                ->groupBy('date')
                ->orderBy('date')
                ->get(),

            // Top routes
            'top_routes' => Route::withCount(['trips' => function($query) use ($startDate, $endDate) {
                $query->whereHas('tickets.payments', function($q) use ($startDate, $endDate) {
                    $q->where('status', 'paid')
                      ->whereBetween('payment_date', [$startDate, $endDate]);
                });
            }])
            ->with(['trips' => function($query) use ($startDate, $endDate) {
                $query->whereHas('tickets.payments', function($q) use ($startDate, $endDate) {
                    $q->where('status', 'paid')
                      ->whereBetween('payment_date', [$startDate, $endDate]);
                })->withSum(['tickets as revenue' => function($q) use ($startDate, $endDate) {
                    $q->join('payment', 'tickets.id', '=', 'payment.ticket_id')
                      ->where('payment.status', 'paid')
                      ->whereBetween('payment.payment_date', [$startDate, $endDate]);
                }], 'payment.amount');
            }])
            ->orderBy('trips_count', 'desc')
            ->take(10)
            ->get(),
        ];

        // Return printable HTML view
        return view('reports.pdf.monthly-revenue', $data)
            ->with('print_mode', true);
    }

    /**
     * Generate Daily Sales Report PDF
     */
    public function dailySalesReport($date = null)
    {
        $this->checkAccess();

        $date = $date ? Carbon::parse($date) : now();
        $startDate = $date->copy()->startOfDay();
        $endDate = $date->copy()->endOfDay();

        $data = [
            'title' => 'Daily Sales Report',
            'date' => $date->format('F d, Y'),
            'generated_at' => now()->format('F d, Y h:i A'),
            'generated_by' => Auth::user()->name,

            // Sales Summary
            'total_sales' => payment::where('status', 'paid')
                ->whereBetween('payment_date', [$startDate, $endDate])
                ->sum('amount'),

            'total_bookings' => Ticket::whereHas('payments', function($q) use ($startDate, $endDate) {
                $q->where('status', 'paid')
                  ->whereBetween('payment_date', [$startDate, $endDate]);
            })->count(),

            'pending_amount' => payment::where('status', 'pending')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('amount'),

            'cancelled_amount' => payment::where('status', 'cancelled')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('amount'),

            // Detailed transactions
            'transactions' => payment::with(['ticket.trip.route', 'ticket.user'])
                ->whereBetween('payment_date', [$startDate, $endDate])
                ->orderBy('payment_date', 'desc')
                ->get(),

            // Payment methods breakdown
            'payment_methods' => payment::where('status', 'paid')
                ->whereBetween('payment_date', [$startDate, $endDate])
                ->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as total')
                ->groupBy('payment_method')
                ->get(),
        ];

        // Return printable HTML view
        return view('reports.pdf.daily-sales', $data)
            ->with('print_mode', true);
    }

    /**
     * Generate Bus Utilization Report PDF
     */
    public function busUtilizationReportPdf($month = null, $year = null)
    {
        $this->checkAccess();

        $year = $year ?? date('Y');
        $month = $month ?? date('m');

        $startDate = Carbon::create($year, $month, 1)->startOfMonth();
        $endDate = Carbon::create($year, $month, 1)->endOfMonth();

        $data = [
            'title' => 'Bus Utilization Report',
            'period' => $startDate->format('F Y'),
            'generated_at' => now()->format('F d, Y h:i A'),
            'generated_by' => Auth::user()->name,

            // Bus utilization data
            'buses' => Bus::with(['trips' => function($query) use ($startDate, $endDate) {
                $query->whereHas('schedule', function($q) use ($startDate, $endDate) {
                    $q->whereBetween('trip_date', [$startDate, $endDate]);
                })->withCount('tickets');
            }])
            ->get()
            ->map(function($bus) use ($startDate, $endDate) {
                $totalTrips = $bus->trips->count();
                $totalSeats = $totalTrips * $bus->capacity;
                $bookedSeats = $bus->trips->sum('tickets_count');

                return [
                    'bus' => $bus,
                    'total_trips' => $totalTrips,
                    'total_capacity' => $totalSeats,
                    'booked_seats' => $bookedSeats,
                    'utilization_rate' => $totalSeats > 0 ? round(($bookedSeats / $totalSeats) * 100, 2) : 0,
                    'revenue' => $bus->trips->sum(function($trip) {
                        return $trip->tickets->sum(function($ticket) {
                            return $ticket->payments->where('status', 'paid')->sum('amount');
                        });
                    })
                ];
            }),

            // Summary statistics
            'total_buses' => Bus::count(),
            'active_buses' => Bus::whereHas('trips.schedule', function($query) use ($startDate, $endDate) {
                $query->whereBetween('trip_date', [$startDate, $endDate]);
            })->count(),
        ];

        // Return printable HTML view
        return view('reports.pdf.bus-utilization', $data)
            ->with('print_mode', true);
    }

    /**
     * Check if user has access to reports
     */
    private function checkAccess()
    {
        $user = Auth::user();
        $role = $user ? $user->roles->first() : null;

        if (!$role || !in_array($role->type, ['admin', 'agent'])) {
            abort(403, 'Access denied. Admin or Agent role required to generate reports.');
        }
    }

    public function bookingReport(Request $request)
    {
        $query = Ticket::with(['user', 'trip.bus', 'trip.route', 'trip.schedule', 'payments']);

        // Date filtering
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        // Status filtering
        if ($request->filled('status')) {
            $query->whereHas('payments', function($q) use ($request) {
                $q->where('status', $request->status);
            });
        }

        $bookings = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.reports.bookings', compact('bookings'));
    }

    public function revenueReport(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());

        // Daily revenue data
        $dailyRevenue = payment::where('status', 'paid')
            ->whereBetween('payment_date', [$startDate, $endDate])
            ->selectRaw('DATE(payment_date) as date, SUM(amount) as revenue, COUNT(*) as transactions')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Revenue by payment method
        $revenueByMethod = payment::where('status', 'paid')
            ->whereBetween('payment_date', [$startDate, $endDate])
            ->selectRaw('payment_method, SUM(amount) as revenue, COUNT(*) as transactions')
            ->groupBy('payment_method')
            ->get();

        // Revenue by route
        $revenueByRoute = payment::where('status', 'paid')
            ->whereBetween('payment_date', [$startDate, $endDate])
            ->join('tickets', 'payment.ticket_id', '=', 'tickets.id')
            ->join('trip', 'tickets.trip_id', '=', 'trip.id')
            ->join('routes', 'trip.route_id', '=', 'routes.id')
            ->selectRaw('routes.start_point, routes.end_point, SUM(payment.amount) as revenue, COUNT(*) as bookings')
            ->groupBy('routes.id', 'routes.start_point', 'routes.end_point')
            ->orderBy('revenue', 'desc')
            ->get();

        // Revenue by bus
        $revenueByBus = payment::where('status', 'paid')
            ->whereBetween('payment_date', [$startDate, $endDate])
            ->join('tickets', 'payment.ticket_id', '=', 'tickets.id')
            ->join('trip', 'tickets.trip_id', '=', 'trip.id')
            ->join('buses', 'trip.bus_id', '=', 'buses.id')
            ->selectRaw('buses.name as bus_name, SUM(payment.amount) as revenue, COUNT(*) as bookings')
            ->groupBy('buses.id', 'buses.name')
            ->orderBy('revenue', 'desc')
            ->get();

        $totalRevenue = $dailyRevenue->sum('revenue');
        $totalTransactions = $dailyRevenue->sum('transactions');
        $averageTransaction = $totalTransactions > 0 ? $totalRevenue / $totalTransactions : 0;

        return view('reports.revenue', compact(
            'dailyRevenue',
            'revenueByMethod',
            'revenueByRoute',
            'revenueByBus',
            'totalRevenue',
            'totalTransactions',
            'averageTransaction',
            'startDate',
            'endDate'
        ));
    }

    public function busUtilizationReport()
    {
        $busUtilization = Bus::withCount(['trips as total_trips'])
            ->with(['trips' => function($query) {
                $query->withCount(['tickets as booked_seats' => function($q) {
                    $q->whereHas('payments', function($p) {
                        $p->where('status', 'paid');
                    });
                }]);
            }])
            ->get()
            ->map(function($bus) {
                $totalCapacity = $bus->total_trips * $bus->seat;
                $totalBooked = $bus->trips->sum('booked_seats');
                $utilizationRate = $totalCapacity > 0 ? ($totalBooked / $totalCapacity) * 100 : 0;
                
                return [
                    'bus' => $bus,
                    'total_capacity' => $totalCapacity,
                    'total_booked' => $totalBooked,
                    'utilization_rate' => round($utilizationRate, 2)
                ];
            });

        return view('admin.reports.bus-utilization', compact('busUtilization'));
    }

    public function exportBookings(Request $request)
    {
        $query = Ticket::with(['user', 'trip.bus', 'trip.route', 'trip.schedule', 'payments']);

        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $bookings = $query->get();

        $filename = 'bookings_report_' . date('Y-m-d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($bookings) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['ID', 'User', 'Bus', 'Route', 'Seat Number', 'Price', 'Status', 'Booking Date']);

            foreach ($bookings as $booking) {
                fputcsv($file, [
                    $booking->id,
                    $booking->user->name,
                    $booking->trip->bus->name,
                    $booking->trip->route->start_point . ' → ' . $booking->trip->route->end_point,
                    $booking->seat_number,
                    $booking->price,
                    $booking->payments->first()->status ?? 'N/A',
                    $booking->created_at->format('Y-m-d H:i:s')
                ]);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}