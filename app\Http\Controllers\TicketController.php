<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\TicketRequest;
use App\Models\Ticket;
use App\Models\Trip;
use App\Models\payment;
use App\Http\Resources\TicketResource;
use App\Http\Resources\PaymentResource;
use Illuminate\Support\Facades\Auth;
use App\Models\canceled_ticket;
use App\Http\Requests\TicketActionRequest;
use Illuminate\Support\Facades\Gate;
use App\Traits\ApiResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

use App\Events\UserBookedTicket;
use App\Events\TicketCanceled;
use App\Events\TicketPurchased;
use App\Services\PaymentGatewayService;


class TicketController extends Controller
{
  use ApiResponse;
 public function store_ticket(TicketRequest $ticketRequest){

    try{
      $ticket=Ticket::create($ticketRequest->all());
      payment::create(['ticket_id'=>$ticket->id]);
      event(new UserBookedTicket(auth()->user(), $ticket));

    } catch (\Exception $e) {
      Log::error($e);
      return $this->error(null, 'Something went wrong', 500);
    }
    return $this->success(new TicketResource($ticket->load( 'trip.bus', 'trip.route', 'trip.schedule')), 'Ticket has been created successfully', 201);
      
 }

 public function cancel_ticket(Ticket $ticket,TicketActionRequest $request)
{
  try{
    $payment = Payment::where('ticket_id', $ticket->id)->first(); 

    $payment->status = 'cancelled';

    $payment->save();
    $ticket->delete();
    canceled_ticket::create([
            'ticket_id' => $ticket->id,
            'user_id' => $ticket->user_id,
      ]);
      event(new TicketCanceled(auth()->user(), $ticket));
    }  
    catch (\Exception $e) {
      Log::error($e);
      return $this->error(null, 'Something went wrong', 500);
    }

    

    return $this->success(new TicketResource($ticket,false), 'Ticket has been cancelled successfully', 201);
}

    public function complete_payment(Ticket $ticket,TicketActionRequest $request){
           
       try{
        $payment = payment::where('ticket_id', $ticket->id)->first();

        $payment->status = 'paid';
        $payment->payment_date = now();
        if (!$payment->amount) {
            $payment->amount = $ticket->price;
        }

        $payment->save();
        event(new TicketPurchased(auth()->user(), $ticket));
       } 
       catch (\Exception $e) {
        Log::error($e);
        return $this->error(null, 'Something went wrong', 500);
       }
        return $this->success(new PaymentResource($payment), 'Payment has been completed successfully', 201);
    }
    // Book a seat for a trip
    public function bookSeat(Request $request, $tripId)
    {
        Log::info('BookSeat function called', [
            'trip_id' => $tripId,
            'user_id' => Auth::id(),
            'request_data' => $request->all()
        ]);

        try {
            $request->validate([
                'seat_numbers' => 'required|string',
                'payment_method' => 'required|in:visa,master,gpay,gcash,cash,maya,qrph',
                'payment_reference' => 'nullable|string|max:255'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Validation failed', ['errors' => $e->errors()]);
            throw $e;
        }

        // Parse seat numbers
        $seatNumbers = array_map('intval', explode(',', $request->seat_numbers));
        $seatNumbers = array_filter($seatNumbers, function($seat) { return $seat > 0; });

        if (empty($seatNumbers)) {
            return response()->json(['error' => 'Please select at least one seat.'], 400);
        }

        if (count($seatNumbers) > 5) {
            return response()->json(['error' => 'You can only book up to 5 seats at once.'], 400);
        }

        $user = Auth::user();
        $trip = \App\Models\Trip::find($tripId);

        if (!$trip) {
            return response()->json(['error' => 'Trip not found.'], 404);
        }

        // Validate seat numbers are within bus capacity
        foreach ($seatNumbers as $seatNumber) {
            if ($seatNumber > $trip->bus->seat) {
                return response()->json(['error' => "Invalid seat number {$seatNumber}. This bus only has {$trip->bus->seat} seats."], 400);
            }
        }

        try {
            DB::beginTransaction();

            // Check if bus has enough available seats
            $bookedSeats = $trip->tickets()
                ->whereHas('payments', function($q) {
                    $q->where('status', '!=', 'cancelled');
                })
                ->lockForUpdate()
                ->count();

            if (($bookedSeats + count($seatNumbers)) > $trip->bus->seat) {
                DB::rollback();
                return response()->json(['error' => 'Not enough seats available. Only ' . ($trip->bus->seat - $bookedSeats) . ' seats left.'], 409);
            }

            // Check for double booking of any selected seats
            $conflictingSeats = \App\Models\Ticket::where('trip_id', $tripId)
                ->whereIn('seat_number', $seatNumbers)
                ->whereHas('payments', function($q) {
                    $q->where('status', '!=', 'cancelled');
                })
                ->lockForUpdate()
                ->pluck('seat_number')
                ->toArray();

            if (!empty($conflictingSeats)) {
                DB::rollback();
                return response()->json(['error' => 'Seats ' . implode(', ', $conflictingSeats) . ' are already booked. Please select different seats.'], 409);
            }

            // Check if user already has a pending booking for this trip
            $userHasPendingBooking = \App\Models\Ticket::where('trip_id', $tripId)
                ->where('user_id', $user->id)
                ->whereHas('payments', function($q) {
                    $q->where('status', 'pending');
                })
                ->exists();

            if ($userHasPendingBooking) {
                DB::rollback();
                return response()->json(['error' => 'You already have a pending booking for this trip. Please complete or cancel your existing booking first.'], 409);
            }

            // Calculate price based on route fare
            $fare = $trip->route->activeFare;
            if ($fare) {
                $pricePerSeat = $fare->calculateFare(); // You can pass distance if available
            } else {
                // Fallback to default price if no fare is set
                $pricePerSeat = 100;
            }

            $tickets = [];
            $payments = [];
            $totalAmount = 0;

            // Create tickets for each seat
            foreach ($seatNumbers as $seatNumber) {
                $ticket = \App\Models\Ticket::create([
                    'user_id' => $user->id,
                    'trip_id' => $tripId,
                    'seat_number' => $seatNumber,
                    'price' => $pricePerSeat,
                ]);

                // Generate invoice number
                $invoiceNumber = 'INV-' . date('Ymd') . '-' . str_pad($ticket->id, 6, '0', STR_PAD_LEFT);

                $payment = payment::create([
                    'ticket_id' => $ticket->id,
                    'payment_method' => $request->payment_method,
                    'invoice_number' => $invoiceNumber,
                    'amount' => $pricePerSeat,
                    'payment_details' => json_encode([
                        'method' => $request->payment_method,
                        'reference' => $request->payment_reference ?? null,
                        'booking_date' => now()->toDateTimeString(),
                        'seats_booked' => count($seatNumbers),
                        'all_seats' => $seatNumbers
                    ])
                ]);

                $tickets[] = $ticket;
                $payments[] = $payment;
                $totalAmount += $pricePerSeat;

                event(new UserBookedTicket($user, $ticket));
            }

            // Process payment through gateway (use first payment for gateway processing)
            $paymentGateway = new PaymentGatewayService();
            $paymentResult = $paymentGateway->processPayment(
                $request->payment_method,
                $totalAmount,
                ['name' => $user->name, 'email' => $user->email],
                $payments[0] // Use first payment for gateway processing
            );

            DB::commit();

            // Calculate seats left
            $seats_left = $trip->bus->seat - $trip->tickets()->whereHas('payments', function($q) {
                $q->where('status', '!=', 'cancelled');
            })->count();

            $response = [
                'success' => true,
                'seats_left' => $seats_left,
                'tickets_count' => count($tickets),
                'seats_booked' => $seatNumbers,
                'total_amount' => $totalAmount,
                'invoice_numbers' => array_column($payments, 'invoice_number'),
                'payment_gateway' => $paymentResult,
                'booking_id' => $payments[0]->id,
                'message' => count($seatNumbers) > 1
                    ? 'Seats ' . implode(', ', $seatNumbers) . ' booked successfully! Total: ₱' . number_format($totalAmount, 2)
                    : 'Seat ' . $seatNumbers[0] . ' booked successfully! Amount: ₱' . number_format($totalAmount, 2)
            ];

            // Add redirect URL if payment gateway requires it
            if ($paymentResult['redirect_required']) {
                $response['redirect_url'] = $paymentResult['payment_url'];
            }

            return response()->json($response);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Seat booking error: ' . $e->getMessage());
            return response()->json(['error' => 'An error occurred while booking the seat.'], 500);
        }
    }

    // Get available seats for a trip
    public function getAvailableSeats($tripId)
    {
        $trip = \App\Models\Trip::find($tripId);

        if (!$trip) {
            return response()->json(['error' => 'Trip not found.'], 404);
        }

        // Get all booked seat numbers for this trip
        $bookedSeats = \App\Models\Ticket::where('trip_id', $tripId)
            ->whereHas('payments', function($q) {
                $q->where('status', '!=', 'cancelled');
            })
            ->pluck('seat_number')
            ->toArray();

        // Generate array of all seat numbers
        $allSeats = range(1, $trip->bus->seat);

        // Get available seats
        $availableSeats = array_diff($allSeats, $bookedSeats);

        return response()->json([
            'total_seats' => $trip->bus->seat,
            'booked_seats' => $bookedSeats,
            'available_seats' => array_values($availableSeats),
            'seats_left' => count($availableSeats)
        ]);
    }

    // Generate and download invoice PDF
    public function generateInvoice($ticketId)
    {
        $ticket = Ticket::with(['user', 'trip.bus', 'trip.route', 'trip.schedule', 'payments'])
                       ->findOrFail($ticketId);

        $payment = $ticket->payments->first();

        if (!$payment) {
            return response()->json(['error' => 'Payment not found for this ticket.'], 404);
        }

        // Generate invoice HTML
        $invoiceHtml = view('invoices.ticket-invoice', compact('ticket', 'payment'))->render();

        return response()->json([
            'success' => true,
            'invoice_html' => $invoiceHtml,
            'ticket' => $ticket,
            'payment' => $payment
        ]);
    }

    // Get payment receipt
    public function getReceipt($ticketId)
    {
        $ticket = Ticket::with(['user', 'trip.bus', 'trip.route', 'trip.schedule', 'payments'])
                       ->findOrFail($ticketId);

        $payment = $ticket->payments->first();

        if (!$payment || $payment->status !== 'paid') {
            return response()->json(['error' => 'Payment not completed or not found.'], 404);
        }

        return response()->json([
            'success' => true,
            'receipt' => [
                'invoice_number' => $payment->invoice_number,
                'ticket_id' => $ticket->id,
                'passenger_name' => $ticket->user->name,
                'bus_name' => $ticket->trip->bus->name,
                'route' => $ticket->trip->route->start_point . ' → ' . $ticket->trip->route->end_point,
                'schedule' => $ticket->trip->schedule->start_time . ' - ' . $ticket->trip->schedule->end_time,
                'seat_number' => $ticket->seat_number,
                'amount' => $payment->amount,
                'payment_method' => $payment->payment_method,
                'payment_date' => $payment->payment_date,
                'booking_date' => $ticket->created_at
            ]
        ]);
    }

    // Update payment status
    public function updatePaymentStatus(Request $request, $ticketId)
    {
        $request->validate([
            'status' => 'required|in:pending,paid,cancelled',
            'payment_reference' => 'nullable|string|max:255'
        ]);

        $ticket = Ticket::findOrFail($ticketId);
        $payment = $ticket->payments->first();

        if (!$payment) {
            return response()->json(['error' => 'Payment not found.'], 404);
        }

        $payment->status = $request->status;
        if ($request->payment_reference) {
            $paymentDetails = json_decode($payment->payment_details, true) ?? [];
            $paymentDetails['reference'] = $request->payment_reference;
            $payment->payment_details = json_encode($paymentDetails);
        }

        if ($request->status === 'paid') {
            $payment->payment_date = now();
        }

        $payment->save();

        return response()->json([
            'success' => true,
            'message' => 'Payment status updated successfully.',
            'payment' => $payment
        ]);
    }

    // View invoice directly
    public function viewInvoice($ticketId)
    {
        $ticket = Ticket::with(['user', 'trip.bus', 'trip.route', 'trip.schedule', 'payments'])
                       ->findOrFail($ticketId);

        $payment = $ticket->payments->first();

        if (!$payment) {
            return redirect()->back()->with('error', 'Payment not found for this ticket.');
        }

        return view('invoices.ticket-invoice', compact('ticket', 'payment'));
    }
}
