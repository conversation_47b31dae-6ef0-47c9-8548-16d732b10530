<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\TripRequest;
use App\Models\Ticket;
use App\Models\Trip;
use App\Http\Resources\TripResource;
use Illuminate\Support\Facades\Auth;
use App\Traits\ApiResponse;
use Illuminate\Support\Facades\Log;


class TripController extends Controller

{
    use ApiResponse;
    public function store_trip(TripRequest $request){
        try {
              $trip=Trip::create($request->all());
        }
        catch (\Exception $e) {
            Log::error($e);
            return $this->error(null, 'Something went wrong', 500);
        }

      
        return $this->success(new TripResource($trip->load( 'bus', 'route', 'schedule')), 'Trip has been created successfully', 201);
    }

    public function edit_trip(TripRequest $request, Trip $trip){
        try{
        $trip->update($request->all());
    }
    catch (\Exception $e) {
        Log::error($e);
        return $this->error(null, 'Something went wrong', 500);
    }
        return $this->success(new TripResource($trip->load( 'bus', 'route', 'schedule')), 'Trip has been updated successfully', 201);
    }

    public function delete_trip(Trip $trip){
      try{  $ticket=Ticket::GetUserPendingTicket(Auth::user()->id)->where('trip_id', $trip->id)->first();
        $ticket->delete();
        $trip->delete();
        }
        catch (\Exception $e) {
            Log::error($e);
            return $this->error(null, 'Something went wrong', 500);
        }

        return $this->success(new TripResource($trip->load( 'bus', 'route', 'schedule')), 'Trip has been deleted successfully', 201);
    }
}
