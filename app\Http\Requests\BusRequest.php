<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class BusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'bus_code' => ['required', 'string', 'max:20', 'unique:buses,bus_code,' . $this->route('bus')],
            'seat' => ['required', 'numeric', 'min:1', 'max:100'],
            'driver_name' => ['required', 'string', 'max:255'],
            'conductor_name' => ['required', 'string', 'max:255'],
        ];
    }

    public function messages(): array
    {
        return [
            'bus_code.unique' => 'This bus code is already taken.',
            'bus_code.required' => 'Bus code is required.',
            'driver_name.required' => 'Driver name is required.',
            'conductor_name.required' => 'Conductor name is required.',
            'seat.min' => 'Bus must have at least 1 seat.',
            'seat.max' => 'Bus cannot have more than 100 seats.',
        ];
    }
}
