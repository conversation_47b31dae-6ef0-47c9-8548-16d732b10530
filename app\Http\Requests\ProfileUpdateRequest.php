<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProfileUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email',
                'max:255',
                Rule::unique(User::class)->ignore($this->user()->id),
            ],
            'phone' => ['nullable', 'string', 'max:20'],
            'address' => ['nullable', 'string', 'max:500'],
            'preferred_pickup' => ['nullable', 'string', 'max:100'],
            'custom_pickup' => ['nullable', 'required_if:preferred_pickup,other', 'string', 'max:100'],
            'emergency_contact' => ['nullable', 'string', 'max:20'],
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // If "other" is selected and custom_pickup is provided, use custom_pickup as preferred_pickup
        if ($this->preferred_pickup === 'other' && $this->custom_pickup) {
            $this->merge([
                'preferred_pickup' => $this->custom_pickup,
            ]);
        }
    }
}
