<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ScheduleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "trip_date" => ["required", "date", "after_or_equal:today"],
            "start_time" => ["required", "date_format:H:i"],
            "end_time" => ["required", "date_format:H:i", "after:start_time"],
        ];
    }

    public function messages(): array
    {
        return [
            'trip_date.required' => 'Trip date is required.',
            'trip_date.after_or_equal' => 'Trip date cannot be in the past.',
            'end_time.after' => 'End time must be after start time.',
        ];
    }
}
