<?php 
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\payment;
use Illuminate\Validation\Validator;
use Illuminate\Support\Facades\Log;

class TicketActionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Make sure you add proper authorization logic if needed
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
           
        ];
    }

    /**
     * Add custom validation logic.
     */
    public function withValidator(Validator $validator)
    {
      
        try{
        $ticket = $this->route('ticket');
        $payment = Payment::where('ticket_id', $ticket->id)->first();

        if (!$payment) {
            $validator->errors()->add('id', 'Payment not found');
            return;
        }

        if ($payment->status === 'paid') {
            $validator->errors()->add('id', 'Ticket is already paid');
        } elseif ($payment->status === 'cancelled') {
            $validator->errors()->add('id', 'Ticket is already cancelled');
        }
        }catch(\Exception $e){  
            Log::error($e);
        }
    }
}
