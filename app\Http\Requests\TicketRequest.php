<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Ticket;
use Illuminate\Support\Facades\Auth;

class TicketRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'trip_id' => 'required|exists:trip,id',
            'user_id' => 'required|exists:users,id',
            'price' => 'required|numeric',
        ];
    }

    public function withValidator($validator)
    {
        try{
        $validator->after(function ($validator) {
            if (Ticket::ticketExist($this->trip_id, Auth::user()->id)) {
                $validator->errors()->add('trip_id', 'Ticket already exists for this trip and user.');
            }
        });
        }catch(\Exception $e){
            Log::error($e);
        }
    }
}
