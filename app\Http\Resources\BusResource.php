<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BusResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'bus_code' => $this->bus_code,
            'seat' => $this->seat,
            'driver_name' => $this->driver_name,
            'conductor_name' => $this->conductor_name,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
