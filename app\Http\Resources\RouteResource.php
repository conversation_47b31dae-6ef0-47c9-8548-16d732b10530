<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RouteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'start_point' => $this->start_point,
            'end_point' => $this->end_point,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
