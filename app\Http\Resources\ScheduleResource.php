<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ScheduleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'trip_date' => $this->trip_date,
            'start_time' => $this->start_time,
            'end_time' => $this->end_time,
            'formatted_schedule' => $this->formatted_schedule,
            'formatted_trip_date' => $this->formatted_trip_date,
            'full_schedule' => $this->full_schedule,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
