<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TicketResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     * 
     */
    protected $includeTrip ;

     public function __construct($resource, $includeTrip = true){
        parent::__construct($resource);
        $this->includeTrip = $includeTrip;
     }
    public function toArray(Request $request): array
    {
        $data= [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'trip_id' => $this->trip_id,
            'price' => $this->price,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at

        ];
            if($this->includeTrip){
                $data['trip'] = new TripResource($this->whenLoaded('trip'));
            }
        return $data;

    }
}
