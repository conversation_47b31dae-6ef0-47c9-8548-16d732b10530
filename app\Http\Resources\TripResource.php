<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TripResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'bus'=> new BusResource($this->bus),
            'route'=> new RouteResource($this->route),
            'schedule'=> new ScheduleResource($this->schedule),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
