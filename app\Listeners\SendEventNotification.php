<?php

namespace App\Listeners;

use App\Events\UserBookedTicket;
use App\Mail\EventNotification;
use Illuminate\Support\Facades\Mail;

class SendEventNotification
{
    /**
     * Handle the event.
     */
    public function handle(UserBookedTicket $event)
    {
        $subject = "Ticket Booking Confirmation";
        $messageBody = "Dear {$event->user->name}, your ticket Id {$event->ticket->id} has been booked successfully.";

        Mail::to($event->user->email)->send(new EventNotification($subject, $messageBody));
    }
}
