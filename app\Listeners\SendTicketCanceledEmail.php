<?php 
namespace App\Listeners;

use App\Events\TicketCanceled;
use App\Mail\EventNotification;
use Illuminate\Support\Facades\Mail;

class SendTicketCanceledEmail
{
    public function handle(TicketCanceled $event)
    {
        $subject = "Your Ticket Has Been Canceled";
        $messageBody = "Dear {$event->user->name}, your ticket  id {$event->ticket->id} has been canceled.";

        Mail::to($event->user->email)->send(new EventNotification($subject, $messageBody));
    }
}