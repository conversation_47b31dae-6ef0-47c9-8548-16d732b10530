<?php

namespace App\Listeners;

use App\Events\TicketPurchased;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;
use App\Mail\EventNotification;

class SendTicketPurchasedEmail
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(TicketPurchased $event): void
    {
        $subject = "Your Ticket Has Been Purchased";
        $messageBody = "Dear {$event->user->name}, your ticket  id {$event->ticket->id} has been purchased.";

        Mail::to($event->user->email)->send(new EventNotification($subject, $messageBody));
    }
}
