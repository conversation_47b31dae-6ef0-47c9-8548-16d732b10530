<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\payment;
use App\Models\Ticket;

class BookingConfirmation extends Mailable
{
    use Queueable, SerializesModels;

    public $payment;
    public $tickets;
    public $passengerData;

    /**
     * Create a new message instance.
     */
    public function __construct(payment $payment, $tickets, $passengerData = null)
    {
        $this->payment = $payment;
        $this->tickets = $tickets;
        $this->passengerData = $passengerData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'GL Bus - Booking Confirmation #' . str_pad($this->payment->id, 6, '0', STR_PAD_LEFT),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.booking-confirmation',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
