<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Fare extends Model
{
    protected $fillable = [
        'route_id',
        'base_fare',
        'per_km_rate',
        'minimum_fare',
        'maximum_fare',
        'is_active',
        'description'
    ];

    protected $casts = [
        'base_fare' => 'decimal:2',
        'per_km_rate' => 'decimal:2',
        'minimum_fare' => 'decimal:2',
        'maximum_fare' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    public function route(): BelongsTo
    {
        return $this->belongsTo(Route::class);
    }

    // Calculate fare based on distance
    public function calculateFare($distance = null): float
    {
        $fare = $this->base_fare;

        if ($distance && $this->per_km_rate > 0) {
            $fare += ($distance * $this->per_km_rate);
        }

        // Apply minimum and maximum fare limits
        if ($this->minimum_fare && $fare < $this->minimum_fare) {
            $fare = $this->minimum_fare;
        }

        if ($this->maximum_fare && $fare > $this->maximum_fare) {
            $fare = $this->maximum_fare;
        }

        return round($fare, 2);
    }
}
