<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class Route extends Model
{ 
    use SoftDeletes;
    protected $fillable = [
        'start_point',
        'end_point',
    ];


    public function trips(){
        return $this->hasMany(Trip::class);
    }

    public function fares()
    {
        return $this->hasMany(Fare::class);
    }

    public function activeFare()
    {
        return $this->hasOne(Fare::class)->where('is_active', true);
    }
}
