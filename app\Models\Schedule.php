<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Schedule extends Model
{
    use SoftDeletes;
    protected $table = 'schedule';
    protected $fillable = [
        'start_time',
        'end_time',
        'trip_date',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'trip_date' => 'date',
    ];

    public function trips(){
        return $this->hasMany(Trip::class);
    }

    // Helper method to format time to 12-hour format with AM/PM
    public function getFormattedStartTimeAttribute()
    {
        if ($this->start_time instanceof Carbon) {
            return $this->start_time->format('g:i A');
        }

        // If it's a string, try to parse it
        try {
            return Carbon::parse($this->start_time)->format('g:i A');
        } catch (\Exception $e) {
            return $this->start_time; // Return original if parsing fails
        }
    }

    public function getFormattedEndTimeAttribute()
    {
        if ($this->end_time instanceof Carbon) {
            return $this->end_time->format('g:i A');
        }

        // If it's a string, try to parse it
        try {
            return Carbon::parse($this->end_time)->format('g:i A');
        } catch (\Exception $e) {
            return $this->end_time; // Return original if parsing fails
        }
    }

    public function getFormattedScheduleAttribute()
    {
        return $this->formatted_start_time . ' - ' . $this->formatted_end_time;
    }

    public function getFormattedTripDateAttribute()
    {
        if ($this->trip_date instanceof Carbon) {
            return $this->trip_date->format('M d, Y');
        }

        try {
            return Carbon::parse($this->trip_date)->format('M d, Y');
        } catch (\Exception $e) {
            return $this->trip_date;
        }
    }

    public function getFullScheduleAttribute()
    {
        return $this->formatted_trip_date . ' (' . $this->formatted_schedule . ')';
    }

    // Get the full datetime for this schedule in Manila timezone
    public function getFullDateTimeAttribute()
    {
        $date = Carbon::parse($this->trip_date)->format('Y-m-d');
        $time = Carbon::parse($this->start_time)->format('H:i:s');
        return Carbon::parse($date . ' ' . $time, 'Asia/Manila');
    }

    // Check if this schedule is expired
    public function getIsExpiredAttribute()
    {
        $now = Carbon::now('Asia/Manila');
        return $this->full_date_time->isPast();
    }

    // Check if this schedule is about to expire (within 30 minutes)
    public function getIsAboutToExpireAttribute()
    {
        $now = Carbon::now('Asia/Manila');
        return $this->full_date_time->diffInMinutes($now) <= 30 && $this->full_date_time->isFuture();
    }

    // Scope to filter available schedules (not expired) - Manila timezone
    public function scopeAvailable($query)
    {
        $now = Carbon::now('Asia/Manila');
        return $query->where(function($q) use ($now) {
            // Future dates are always available
            $q->where('trip_date', '>', $now->toDateString())
              ->orWhere(function($subQ) use ($now) {
                  // For today, check if start time hasn't passed yet
                  $subQ->where('trip_date', '=', $now->toDateString())
                       ->whereTime('start_time', '>', $now->toTimeString());
              });
        });
    }

    // Scope to filter expired schedules - Manila timezone
    public function scopeExpired($query)
    {
        $now = Carbon::now('Asia/Manila');
        return $query->where(function($q) use ($now) {
            // Past dates are expired
            $q->where('trip_date', '<', $now->toDateString())
              ->orWhere(function($subQ) use ($now) {
                  // For today, check if start time has passed
                  $subQ->where('trip_date', '=', $now->toDateString())
                       ->whereTime('start_time', '<=', $now->toTimeString());
              });
        });
    }
}
