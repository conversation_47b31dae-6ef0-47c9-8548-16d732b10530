<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class Ticket extends Model
{
   use SoftDeletes;
   protected $table = 'tickets';
    public $fillable = [
        'user_id',
        'trip_id',
        'seat_number',
        'price',
        'booking_type',
        'booked_by_agent'
    ];


    public function user(){
        return $this->belongsTo(User::class);
    }

    public function trip(){
        return $this->belongsTo(Trip::class);
    }
    
    public function scopeCountTicket($query,$trip_id){
        return $query->TicketStatus($trip_id,"paid")->count();
        
    }

    public function payments(){
        return $this->hasMany(payment::class);
    }
   

    public function scopeGetAllTicket($query,$trip_id){
        return $query->where('trip_id', $trip_id)->get();
    }

    public function scopeGetUserPendingTicket($query,$user_id){
        
            return $query->where('user_id', $user_id)->whereHas('payments', function ($query) {
                $query->where('status', 'pending');
            })->get();
            
        }
    public function scopeTicketStatus($query,$trip_id,$status="pending"){

        return $query->with('payments')
                ->whereHas('payments', function ($query) use ($status)
                {
                $query->where('status', $status);
        
                })
                ->where('trip_id', $trip_id);

    }

    public function scopeTicketExist($query, $trip_id, $user_id)
    {
        return $query->where('trip_id', $trip_id)
                     ->where('user_id', $user_id)
                     ->whereHas('payments', function ($query) {
                         $query->where('status', 'pending');
                     })
                     ->exists();
    }



 


   
}
