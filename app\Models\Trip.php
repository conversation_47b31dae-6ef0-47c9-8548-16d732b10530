<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Trip extends Model
{
    //
    use SoftDeletes;
    protected $fillable = [
        'route_id',
        'schedule_id',
        'bus_id',

    ];

    protected $table = 'trip';
    public function tickets()
    {
        return $this->hasMany(Ticket::class);
    }


    public function route(){
        return $this->belongsTo(Route::class);
    }

    public function schedule(){
        return $this->belongsTo(Schedule::class);
    }

    public function bus(){
        return $this->belongsTo(Bus::class);
    }

    public function scopeSearch($query, $search, $filter)
{
    if (empty($search)) {
        return ""; 
    }

    switch ($filter) {
        case 'route':
            return $query->whereHas('route', function ($query) use ($search) {
                $query->where('start_point', 'like', "%$search%")
                    ->orWhere('end_point', 'like', "%$search%");
            });

        case 'bus':
            return $query->whereHas('bus', function ($query) use ($search) {
                $query->where('name', 'like', "%$search%");
            });

        case 'schedule':
            return $query->whereHas('schedule', function ($query) use ($search) {
                $query->where('start_time', 'like', "%$search%")
                    ->orWhere('end_time', 'like', "%$search%");
            });

        default:
            return is_numeric($search) ? $query->where('id', $search) : $query;
    }
}

    // Scope to filter available trips (not expired)
    public function scopeAvailable($query)
    {
        return $query->whereHas('schedule', function($q) {
            $q->available();
        });
    }

    // Scope to filter expired trips
    public function scopeExpired($query)
    {
        return $query->whereHas('schedule', function($q) {
            $q->expired();
        });
    }

    // Get available seats count for this trip
    public function getAvailableSeatsAttribute()
    {
        $bookedSeats = $this->tickets()
            ->whereHas('payments', function($q) {
                $q->where('status', '!=', 'cancelled');
            })
            ->count();

        return $this->bus->seat - $bookedSeats;
    }

    // Check if trip is bookable (has available seats and not expired)
    public function getIsBookableAttribute()
    {
        return $this->available_seats > 0 && $this->schedule->available()->exists();
    }

}
