<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class payment extends Model
{
    use SoftDeletes;
    protected $table = 'payment';
    protected $fillable = [
         'user_id',
         'ticket_id',
         'status',
         'payment_method',
         'booking_type',
         'booking_details',
         'invoice_number',
         'amount',
         'payment_details',
         'payment_date',
         'reference_number',
         'passenger_name',
         'passenger_email',
         'passenger_mobile',
         'passenger_address'
        ];

    protected $casts = [
        'payment_date' => 'datetime',
        'booking_details' => 'array',
    ];


        public function ticket(){
            return $this->belongsTo(Ticket::class);
        }
       
}
