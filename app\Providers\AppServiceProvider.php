<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate ;
use Illuminate\Support\Facades\Auth as Aut ;
use App\Models\Ticket;
use Illuminate\Support\Facades\View;
use App\Models\Bus;
use App\Models\Route;
use App\Models\Schedule;
use Illuminate\Support\Facades\Blade;
use Carbon\Carbon;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Gate::define('admin', function ($user) {
            $role = $user->roles->first();
            return $role && $role->type == 'admin';
        });

        Gate::define('cancel_or_complete', function ($user, $ticket) {
            $role = $user->roles->first();
            return $ticket->user_id === $user->id || ($role && $role->type == 'admin');
        });

        // Only share options if tables exist
        try {
            if (\Illuminate\Support\Facades\Schema::hasTable('buses')) {
                View::share('busOptions', Bus::all());
            }
            if (\Illuminate\Support\Facades\Schema::hasTable('routes')) {
                View::share('routeOptions', Route::all());
            }
            if (\Illuminate\Support\Facades\Schema::hasTable('schedule')) {
                View::share('scheduleOptions', Schedule::all());
            }
        } catch (\Exception $e) {
            // Ignore database errors during migration
        }

        // Add global Blade directive for time formatting
        Blade::directive('time12', function ($expression) {
            return "<?php
                try {
                    echo \Carbon\Carbon::parse($expression)->format('g:i A');
                } catch (\Exception \$e) {
                    echo $expression;
                }
            ?>";
        });

        // Add global helper function
        if (!function_exists('format_time_12h')) {
            function format_time_12h($time) {
                try {
                    return Carbon::parse($time)->format('g:i A');
                } catch (\Exception $e) {
                    return $time;
                }
            }
        }
    }
}
