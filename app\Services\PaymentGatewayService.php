<?php

namespace App\Services;

use App\Models\Payment;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PaymentGatewayService
{
    /**
     * Process payment through appropriate gateway
     */
    public function processPayment($paymentMethod, $amount, $bookingData, $payment)
    {
        switch ($paymentMethod) {
            case 'gcash':
                return $this->processGCashPayment($amount, $bookingData, $payment);
            case 'maya':
                return $this->processMayaPayment($amount, $bookingData, $payment);
            case 'qrph':
                return $this->processQRPhPayment($amount, $bookingData, $payment);
            case 'visa':
            case 'master':
                return $this->processCreditCardPayment($paymentMethod, $amount, $bookingData, $payment);
            case 'gpay':
                return $this->processGooglePayPayment($amount, $bookingData, $payment);
            case 'cash':
                return $this->processCashPayment($amount, $bookingData, $payment);
            default:
                throw new \Exception('Unsupported payment method');
        }
    }

    /**
     * Process GCash payment
     */
    private function processGCashPayment($amount, $bookingData, $payment)
    {
        // Generate unique transaction reference
        $transactionRef = 'GLBUS-GCASH-' . time() . '-' . Str::random(6);
        
        // Update payment with transaction reference
        $payment->update([
            'reference_number' => $transactionRef,
            'status' => 'pending',
            'payment_details' => json_encode([
                'gateway' => 'gcash',
                'transaction_ref' => $transactionRef,
                'amount' => $amount,
                'currency' => 'PHP'
            ])
        ]);

        // In production, you would integrate with actual GCash API
        // For now, we'll create a mock payment URL
        $paymentUrl = $this->generateGCashPaymentUrl($amount, $transactionRef, $bookingData);

        return [
            'success' => true,
            'payment_method' => 'gcash',
            'payment_url' => $paymentUrl,
            'transaction_ref' => $transactionRef,
            'amount' => $amount,
            'redirect_required' => true,
            'message' => 'Redirecting to GCash payment...'
        ];
    }

    /**
     * Process Maya payment
     */
    private function processMayaPayment($amount, $bookingData, $payment)
    {
        $transactionRef = 'GLBUS-MAYA-' . time() . '-' . Str::random(6);
        
        $payment->update([
            'reference_number' => $transactionRef,
            'status' => 'pending',
            'payment_details' => json_encode([
                'gateway' => 'maya',
                'transaction_ref' => $transactionRef,
                'amount' => $amount,
                'currency' => 'PHP'
            ])
        ]);

        $paymentUrl = $this->generateMayaPaymentUrl($amount, $transactionRef, $bookingData);

        return [
            'success' => true,
            'payment_method' => 'maya',
            'payment_url' => $paymentUrl,
            'transaction_ref' => $transactionRef,
            'amount' => $amount,
            'redirect_required' => true,
            'message' => 'Redirecting to Maya payment...'
        ];
    }

    /**
     * Process QRPh payment
     */
    private function processQRPhPayment($amount, $bookingData, $payment)
    {
        $transactionRef = 'GLBUS-QRPH-' . time() . '-' . Str::random(6);
        
        $payment->update([
            'reference_number' => $transactionRef,
            'status' => 'pending',
            'payment_details' => json_encode([
                'gateway' => 'qrph',
                'transaction_ref' => $transactionRef,
                'amount' => $amount,
                'currency' => 'PHP'
            ])
        ]);

        // Generate QR code for InstaPay/PESONet
        $qrData = $this->generateQRPhData($amount, $transactionRef, $bookingData);

        return [
            'success' => true,
            'payment_method' => 'qrph',
            'qr_code' => $qrData,
            'transaction_ref' => $transactionRef,
            'amount' => $amount,
            'redirect_required' => false,
            'show_qr' => true,
            'message' => 'Scan QR code to pay via QRPh'
        ];
    }

    /**
     * Process Credit Card payment
     */
    private function processCreditCardPayment($cardType, $amount, $bookingData, $payment)
    {
        $transactionRef = 'GLBUS-' . strtoupper($cardType) . '-' . time() . '-' . Str::random(6);
        
        $payment->update([
            'reference_number' => $transactionRef,
            'status' => 'pending',
            'payment_details' => json_encode([
                'gateway' => $cardType,
                'transaction_ref' => $transactionRef,
                'amount' => $amount,
                'currency' => 'PHP'
            ])
        ]);

        // In production, integrate with payment processors like PayMongo, Stripe, etc.
        $paymentUrl = $this->generateCreditCardPaymentUrl($cardType, $amount, $transactionRef, $bookingData);

        return [
            'success' => true,
            'payment_method' => $cardType,
            'payment_url' => $paymentUrl,
            'transaction_ref' => $transactionRef,
            'amount' => $amount,
            'redirect_required' => true,
            'message' => 'Redirecting to secure payment gateway...'
        ];
    }

    /**
     * Process Google Pay payment
     */
    private function processGooglePayPayment($amount, $bookingData, $payment)
    {
        $transactionRef = 'GLBUS-GPAY-' . time() . '-' . Str::random(6);
        
        $payment->update([
            'reference_number' => $transactionRef,
            'status' => 'pending',
            'payment_details' => json_encode([
                'gateway' => 'gpay',
                'transaction_ref' => $transactionRef,
                'amount' => $amount,
                'currency' => 'PHP'
            ])
        ]);

        $paymentUrl = $this->generateGooglePayUrl($amount, $transactionRef, $bookingData);

        return [
            'success' => true,
            'payment_method' => 'gpay',
            'payment_url' => $paymentUrl,
            'transaction_ref' => $transactionRef,
            'amount' => $amount,
            'redirect_required' => true,
            'message' => 'Redirecting to Google Pay...'
        ];
    }

    /**
     * Process Cash payment
     */
    private function processCashPayment($amount, $bookingData, $payment)
    {
        $transactionRef = 'GLBUS-CASH-' . time() . '-' . Str::random(6);
        
        $payment->update([
            'reference_number' => $transactionRef,
            'status' => 'pending', // Cash payments remain pending until agent confirms
            'payment_details' => json_encode([
                'gateway' => 'cash',
                'transaction_ref' => $transactionRef,
                'amount' => $amount,
                'currency' => 'PHP'
            ])
        ]);

        return [
            'success' => true,
            'payment_method' => 'cash',
            'transaction_ref' => $transactionRef,
            'amount' => $amount,
            'redirect_required' => false,
            'message' => 'Cash payment - Please pay at the terminal'
        ];
    }

    /**
     * Generate GCash payment URL (Mock implementation)
     */
    private function generateGCashPaymentUrl($amount, $transactionRef, $bookingData)
    {
        // In production, this would be the actual GCash API endpoint
        // For demo purposes, we'll create a mock payment page
        return route('payment.gateway', [
            'method' => 'gcash',
            'amount' => $amount,
            'ref' => $transactionRef
        ]);
    }

    /**
     * Generate Maya payment URL (Mock implementation)
     */
    private function generateMayaPaymentUrl($amount, $transactionRef, $bookingData)
    {
        return route('payment.gateway', [
            'method' => 'maya',
            'amount' => $amount,
            'ref' => $transactionRef
        ]);
    }

    /**
     * Generate QRPh data
     */
    private function generateQRPhData($amount, $transactionRef, $bookingData)
    {
        // Generate QR code data for InstaPay/PESONet
        return [
            'qr_string' => "GLBUS|{$amount}|{$transactionRef}|" . date('Y-m-d H:i:s'),
            'merchant_name' => 'GL Bus Lines',
            'amount' => $amount,
            'reference' => $transactionRef
        ];
    }

    /**
     * Generate Credit Card payment URL
     */
    private function generateCreditCardPaymentUrl($cardType, $amount, $transactionRef, $bookingData)
    {
        return route('payment.gateway', [
            'method' => $cardType,
            'amount' => $amount,
            'ref' => $transactionRef
        ]);
    }

    /**
     * Generate Google Pay URL
     */
    private function generateGooglePayUrl($amount, $transactionRef, $bookingData)
    {
        return route('payment.gateway', [
            'method' => 'gpay',
            'amount' => $amount,
            'ref' => $transactionRef
        ]);
    }
}
