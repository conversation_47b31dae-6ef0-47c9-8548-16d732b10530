<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SmsService
{
    protected $apiKey;
    protected $senderName;
    protected $baseUrl;

    public function __construct()
    {
        $this->apiKey = env('SMS_API_KEY');
        $this->senderName = env('SMS_SENDER_NAME', 'GL_BUS');
        $this->baseUrl = 'https://api.semaphore.co/api/v4/messages';
    }

    /**
     * Send SMS notification
     */
    public function sendBookingConfirmation($phoneNumber, $bookingData)
    {
        try {
            // Format phone number (ensure it starts with +63)
            $formattedNumber = $this->formatPhoneNumber($phoneNumber);
            
            // Create SMS message
            $message = $this->createBookingMessage($bookingData);
            
            // Send SMS using Semaphore API
            $response = Http::post($this->baseUrl, [
                'apikey' => $this->apiKey,
                'number' => $formattedNumber,
                'message' => $message,
                'sendername' => $this->senderName
            ]);

            if ($response->successful()) {
                Log::info('SMS sent successfully', [
                    'phone' => $formattedNumber,
                    'booking_id' => $bookingData['booking_id']
                ]);
                return true;
            } else {
                Log::error('SMS sending failed', [
                    'phone' => $formattedNumber,
                    'response' => $response->body(),
                    'booking_id' => $bookingData['booking_id']
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('SMS service error', [
                'error' => $e->getMessage(),
                'phone' => $phoneNumber,
                'booking_id' => $bookingData['booking_id'] ?? 'unknown'
            ]);
            return false;
        }
    }

    /**
     * Format phone number to international format
     */
    private function formatPhoneNumber($phoneNumber)
    {
        // Remove all non-numeric characters
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);
        
        // Handle different formats
        if (strlen($cleaned) === 11 && substr($cleaned, 0, 2) === '09') {
            // 09XXXXXXXXX format - convert to +639XXXXXXXXX
            return '+63' . substr($cleaned, 1);
        } elseif (strlen($cleaned) === 10 && substr($cleaned, 0, 1) === '9') {
            // 9XXXXXXXXX format - convert to +639XXXXXXXXX
            return '+63' . $cleaned;
        } elseif (strlen($cleaned) === 12 && substr($cleaned, 0, 2) === '63') {
            // 639XXXXXXXXX format - add +
            return '+' . $cleaned;
        } elseif (strlen($cleaned) === 13 && substr($cleaned, 0, 3) === '+63') {
            // Already in correct format
            return $cleaned;
        }
        
        // Default: assume it's a 9XXXXXXXXX format
        return '+63' . ltrim($cleaned, '0');
    }

    /**
     * Create booking confirmation SMS message
     */
    private function createBookingMessage($data)
    {
        $message = "🚌 GL BUS - Booking Confirmed!\n\n";
        $message .= "Ref: #{$data['booking_id']}\n";
        $message .= "Route: {$data['route']}\n";
        $message .= "Date: {$data['date']}\n";
        $message .= "Time: {$data['time']}\n";
        $message .= "Seat(s): {$data['seats']}\n";
        $message .= "Amount: ₱{$data['amount']}\n\n";
        
        if (isset($data['payment_method']) && $data['payment_method'] !== 'cash') {
            $message .= "💳 Payment: {$data['payment_method']}\n";
            
            if ($data['payment_method'] === 'gcash') {
                $message .= "Send to: " . env('GLBUS_GCASH_NUMBER', '***********') . "\n";
                $message .= "Name: " . env('GLBUS_GCASH_NAME', 'GL Bus Transport Corp') . "\n";
            } elseif ($data['payment_method'] === 'maya') {
                $message .= "Send to: " . env('GLBUS_MAYA_NUMBER', '***********') . "\n";
                $message .= "Name: " . env('GLBUS_MAYA_NAME', 'GL Bus Transport Corp') . "\n";
            } elseif ($data['payment_method'] === 'qrph') {
                $message .= "Account: " . env('GLBUS_QRPH_ACCOUNT', 'GL Bus Transport Corp') . "\n";
                $message .= "Bank: " . env('GLBUS_BANK_ACCOUNT', 'BPI - **********') . "\n";
            }
            
            if (isset($data['reference_number'])) {
                $message .= "Your Ref: {$data['reference_number']}\n";
            }
            $message .= "\n";
        }
        
        $message .= "📱 Show this SMS when boarding.\n";
        $message .= "Arrive 30 mins early.\n\n";
        $message .= "Questions? Call (*************\n";
        $message .= "GL Bus - Your trusted travel partner";
        
        return $message;
    }

    /**
     * Send payment reminder SMS
     */
    public function sendPaymentReminder($phoneNumber, $bookingData)
    {
        try {
            $formattedNumber = $this->formatPhoneNumber($phoneNumber);
            
            $message = "🚌 GL BUS - Payment Reminder\n\n";
            $message .= "Booking Ref: #{$bookingData['booking_id']}\n";
            $message .= "Amount: ₱{$bookingData['amount']}\n\n";
            
            if ($bookingData['payment_method'] === 'gcash') {
                $message .= "📱 GCash: " . env('GLBUS_GCASH_NUMBER', '***********') . "\n";
                $message .= "Name: " . env('GLBUS_GCASH_NAME', 'GL Bus Transport Corp') . "\n";
            } elseif ($bookingData['payment_method'] === 'maya') {
                $message .= "💳 Maya: " . env('GLBUS_MAYA_NUMBER', '***********') . "\n";
                $message .= "Name: " . env('GLBUS_MAYA_NAME', 'GL Bus Transport Corp') . "\n";
            } elseif ($bookingData['payment_method'] === 'qrph') {
                $message .= "🔲 QRPh: " . env('GLBUS_QRPH_ACCOUNT', 'GL Bus Transport Corp') . "\n";
                $message .= "Bank: " . env('GLBUS_BANK_ACCOUNT', 'BPI - **********') . "\n";
            }
            
            $message .= "\nSend payment and reply with reference number.\n";
            $message .= "Questions? Call (*************";
            
            $response = Http::post($this->baseUrl, [
                'apikey' => $this->apiKey,
                'number' => $formattedNumber,
                'message' => $message,
                'sendername' => $this->senderName
            ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Payment reminder SMS error', [
                'error' => $e->getMessage(),
                'phone' => $phoneNumber
            ]);
            return false;
        }
    }
}
