<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment', function (Blueprint $table) {
            $table->string('invoice_number')->unique()->nullable()->after('payment_method');
            $table->decimal('amount', 10, 2)->nullable()->after('invoice_number');
            $table->text('payment_details')->nullable()->after('amount');
            $table->timestamp('payment_date')->nullable()->after('payment_details');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payment', function (Blueprint $table) {
            $table->dropColumn(['invoice_number', 'amount', 'payment_details', 'payment_date']);
        });
    }
};