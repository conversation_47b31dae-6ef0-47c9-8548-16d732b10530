<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('buses', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('trip', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('routes', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('schedule', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('payment', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('roles', function (Blueprint $table) {
            $table->softDeletes();
        });
       
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
        Schema::table('buses', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
        Schema::table('trip', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
        Schema::table('routes', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
        Schema::table('schedule', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
        Schema::table('payment', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
        Schema::table('roles', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
    }
};
