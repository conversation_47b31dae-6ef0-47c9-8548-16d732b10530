<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->integer('seat_number')->nullable()->after('trip_id');

            // Add unique constraint to prevent double booking of same seat on same trip
            $table->unique(['trip_id', 'seat_number'], 'unique_seat_per_trip');

            // Add index for better performance
            $table->index(['user_id', 'trip_id'], 'idx_user_trip');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->dropUnique('unique_seat_per_trip');
            $table->dropIndex('idx_user_trip');
            $table->dropColumn('seat_number');
        });
    }
};
