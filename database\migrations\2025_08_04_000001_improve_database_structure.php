<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes for better performance - check if they don't exist first
        if (!Schema::hasIndex('payment', 'idx_payment_status_date')) {
            Schema::table('payment', function (Blueprint $table) {
                $table->index(['status', 'payment_date'], 'idx_payment_status_date');
            });
        }

        if (!Schema::hasIndex('payment', 'idx_payment_method')) {
            Schema::table('payment', function (Blueprint $table) {
                $table->index(['payment_method'], 'idx_payment_method');
            });
        }

        // Add indexes to trips table
        if (!Schema::hasIndex('trip', 'idx_trip_route_schedule')) {
            Schema::table('trip', function (Blueprint $table) {
                $table->index(['route_id', 'schedule_id'], 'idx_trip_route_schedule');
            });
        }

        if (!Schema::hasIndex('trip', 'idx_trip_bus')) {
            Schema::table('trip', function (Blueprint $table) {
                $table->index(['bus_id'], 'idx_trip_bus');
            });
        }

        // Add indexes to tickets table for better performance
        if (!Schema::hasIndex('tickets', 'idx_ticket_created')) {
            Schema::table('tickets', function (Blueprint $table) {
                $table->index(['created_at'], 'idx_ticket_created');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasIndex('payment', 'idx_payment_status_date')) {
            Schema::table('payment', function (Blueprint $table) {
                $table->dropIndex('idx_payment_status_date');
            });
        }

        if (Schema::hasIndex('payment', 'idx_payment_method')) {
            Schema::table('payment', function (Blueprint $table) {
                $table->dropIndex('idx_payment_method');
            });
        }

        if (Schema::hasIndex('trip', 'idx_trip_route_schedule')) {
            Schema::table('trip', function (Blueprint $table) {
                $table->dropIndex('idx_trip_route_schedule');
            });
        }

        if (Schema::hasIndex('trip', 'idx_trip_bus')) {
            Schema::table('trip', function (Blueprint $table) {
                $table->dropIndex('idx_trip_bus');
            });
        }

        if (Schema::hasIndex('tickets', 'idx_ticket_created')) {
            Schema::table('tickets', function (Blueprint $table) {
                $table->dropIndex('idx_ticket_created');
            });
        }
    }
};
