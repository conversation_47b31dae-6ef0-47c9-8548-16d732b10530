<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->enum('booking_type', ['online', 'walk_in'])->default('online')->after('seat_number');
            $table->unsignedBigInteger('booked_by_agent')->nullable()->after('booking_type');

            $table->foreign('booked_by_agent')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->dropForeign(['booked_by_agent']);
            $table->dropColumn(['booking_type', 'booked_by_agent']);
        });
    }
};
