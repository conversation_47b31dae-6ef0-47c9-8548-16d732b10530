<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Modify the enum to include 'agent'
        DB::statement("ALTER TABLE roles MODIFY COLUMN type ENUM('admin', 'user', 'agent') DEFAULT 'user'");

        // Create agent role if it doesn't exist
        DB::table('roles')->insertOrIgnore([
            'type' => 'agent',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove agent role
        DB::table('roles')->where('type', 'agent')->delete();

        // Revert enum to original values
        DB::statement("ALTER TABLE roles MODIFY COLUMN type ENUM('admin', 'user') DEFAULT 'user'");
    }
};
