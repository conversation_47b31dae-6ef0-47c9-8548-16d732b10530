<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('buses', function (Blueprint $table) {
            if (!Schema::hasColumn('buses', 'bus_code')) {
                $table->string('bus_code')->nullable()->after('name');
            }
            if (!Schema::hasColumn('buses', 'driver_name')) {
                $table->string('driver_name')->nullable()->after('bus_code');
            }
            if (!Schema::hasColumn('buses', 'conductor_name')) {
                $table->string('conductor_name')->nullable()->after('driver_name');
            }
        });

        // Update existing buses with default values
        DB::table('buses')->whereNull('bus_code')->update([
            'bus_code' => DB::raw("CONCAT('BUS', LPAD(id, 3, '0'))"),
            'driver_name' => 'TBD',
            'conductor_name' => 'TBD'
        ]);

        // Now add the unique constraint if it doesn't exist
        if (!$this->hasUniqueConstraint('buses', 'bus_code')) {
            Schema::table('buses', function (Blueprint $table) {
                $table->string('bus_code')->nullable(false)->change();
                $table->string('driver_name')->nullable(false)->change();
                $table->string('conductor_name')->nullable(false)->change();
                $table->unique('bus_code');
            });
        }
    }

    private function hasUniqueConstraint($table, $column)
    {
        $indexes = DB::select("SHOW INDEX FROM {$table} WHERE Column_name = '{$column}' AND Non_unique = 0");
        return !empty($indexes);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('buses', function (Blueprint $table) {
            $table->dropColumn(['bus_code', 'driver_name', 'conductor_name']);
        });
    }
};
