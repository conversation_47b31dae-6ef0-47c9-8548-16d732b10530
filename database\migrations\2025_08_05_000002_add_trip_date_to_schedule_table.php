<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('schedule', function (Blueprint $table) {
            $table->date('trip_date')->nullable()->after('end_time');
            $table->index(['trip_date', 'start_time'], 'idx_schedule_datetime');
        });

        // Update existing schedules with a default date (tomorrow)
        DB::table('schedule')->whereNull('trip_date')->update([
            'trip_date' => now()->addDay()->toDateString()
        ]);

        // Make the field non-nullable
        Schema::table('schedule', function (Blueprint $table) {
            $table->date('trip_date')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('schedule', function (Blueprint $table) {
            $table->dropIndex('idx_schedule_datetime');
            $table->dropColumn('trip_date');
        });
    }
};
