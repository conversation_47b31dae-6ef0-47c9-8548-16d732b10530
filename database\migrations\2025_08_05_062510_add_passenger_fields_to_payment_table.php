<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment', function (Blueprint $table) {
            $table->string('reference_number')->nullable()->after('payment_method');
            $table->string('passenger_name')->nullable()->after('reference_number');
            $table->string('passenger_email')->nullable()->after('passenger_name');
            $table->string('passenger_mobile')->nullable()->after('passenger_email');
            $table->text('passenger_address')->nullable()->after('passenger_mobile');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payment', function (Blueprint $table) {
            $table->dropColumn([
                'reference_number',
                'passenger_name',
                'passenger_email',
                'passenger_mobile',
                'passenger_address'
            ]);
        });
    }
};
