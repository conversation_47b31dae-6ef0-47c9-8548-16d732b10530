<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the payment_method enum to include new e-wallet options
        DB::statement("ALTER TABLE payment MODIFY COLUMN payment_method ENUM('visa', 'master', 'gpay', 'gcash', 'cash', 'maya', 'qrph') NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original enum values
        DB::statement("ALTER TABLE payment MODIFY COLUMN payment_method ENUM('visa', 'master', 'gpay', 'gcash', 'cash') NULL");
    }
};
