<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;

class AgentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create agent user
        $agent = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Terminal Agent',
                'password' => bcrypt('agent123'),
                'email_verified_at' => now(),
            ]
        );

        // Get agent role
        $agentRole = Role::where('type', 'agent')->first();
        
        if ($agentRole && !$agent->roles()->where('role_id', $agentRole->id)->exists()) {
            $agent->roles()->attach($agentRole->id);
        }

        echo "Agent user created:\n";
        echo "Email: <EMAIL>\n";
        echo "Password: agent123\n";
    }
}
