<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Database\Seeders\SampleDataSeeder;
use Illuminate\Support\Str;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Seed users with unique emails
        \App\Models\User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin One',
                'email_verified_at' => now(),
                'password' => bcrypt('Admin@2025'),
                'remember_token' => Str::random(10),
            ]
        );
        \App\Models\User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'User One',
                'email_verified_at' => now(),
                'password' => bcrypt('User@2025'),
                'remember_token' => Str::random(10),
            ]
        );
        $this->call([
            ImprovedSampleDataSeeder::class,
        ]);
    }
}
