<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class ImprovedSampleDataSeeder extends Seeder
{
    public function run(): void
    {
        // Clear old data for a clean seed
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('payment')->truncate();
        DB::table('users_roles')->truncate();
        DB::table('roles')->truncate();
        DB::table('tickets')->truncate();
        DB::table('trip')->truncate();
        DB::table('schedule')->truncate();
        DB::table('routes')->truncate();
        DB::table('buses')->truncate();
        DB::table('users')->truncate();
        DB::table('fares')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Create 5 different buses with Filipino names
        $buses = [
            ['name' => 'Mayon Express', 'seat' => 45, 'bus_code' => 'ME-001', 'capacity' => 45, 'driver_name' => '<PERSON>', 'conductor_name' => '<PERSON> <PERSON>'],
            ['name' => 'Taal Voyager', 'seat' => 45, 'bus_code' => 'TV-002', 'capacity' => 45, 'driver_name' => '<PERSON> <PERSON>', 'conductor_name' => 'Jose <PERSON>'],
            ['name' => 'Banaue Cruiser', 'seat' => 45, 'bus_code' => 'BC-003', 'capacity' => 45, 'driver_name' => 'Antonio <PERSON>', 'conductor_name' => 'Pedro Flores'],
            ['name' => 'Palawan Pioneer', 'seat' => 45, 'bus_code' => 'PP-004', 'capacity' => 45, 'driver_name' => 'Ricardo Villanueva', 'conductor_name' => 'Ramon Torres'],
            ['name' => 'Boracay Breeze', 'seat' => 45, 'bus_code' => 'BB-005', 'capacity' => 45, 'driver_name' => 'Fernando Aquino', 'conductor_name' => 'Alfredo Ramos'],
        ];

        foreach ($buses as $bus) {
            DB::table('buses')->insert(array_merge($bus, [
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }

        // Create routes between major Philippine cities
        $routes = [
            ['start_point' => 'Tabuk', 'end_point' => 'Baguio'],
            ['start_point' => 'Baguio', 'end_point' => 'Tabuk'],
            ['start_point' => 'Tabuk', 'end_point' => 'Manila'],
            ['start_point' => 'Manila', 'end_point' => 'Tabuk'],
            ['start_point' => 'Baguio', 'end_point' => 'Manila'],
            ['start_point' => 'Manila', 'end_point' => 'Baguio'],
        ];

        foreach ($routes as $route) {
            DB::table('routes')->insert(array_merge($route, [
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }

        // Create fare structure
        $fares = [
            ['route_id' => 1, 'base_fare' => 1200.00, 'is_active' => true], // Tabuk -> Baguio
            ['route_id' => 2, 'base_fare' => 1200.00, 'is_active' => true], // Baguio -> Tabuk
            ['route_id' => 3, 'base_fare' => 1800.00, 'is_active' => true], // Tabuk -> Manila
            ['route_id' => 4, 'base_fare' => 1800.00, 'is_active' => true], // Manila -> Tabuk
            ['route_id' => 5, 'base_fare' => 1500.00, 'is_active' => true], // Baguio -> Manila
            ['route_id' => 6, 'base_fare' => 1500.00, 'is_active' => true], // Manila -> Baguio
        ];

        foreach ($fares as $fare) {
            DB::table('fares')->insert(array_merge($fare, [
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }

        // Create schedules for different times of the day
        $schedules = [
            ['start_time' => '06:00:00', 'end_time' => '12:00:00'], // Morning
            ['start_time' => '08:00:00', 'end_time' => '14:00:00'], // Mid-morning
            ['start_time' => '10:00:00', 'end_time' => '16:00:00'], // Late morning
            ['start_time' => '14:00:00', 'end_time' => '20:00:00'], // Afternoon
            ['start_time' => '18:00:00', 'end_time' => '00:00:00'], // Evening
        ];

        foreach ($schedules as $schedule) {
            DB::table('schedule')->insert(array_merge($schedule, [
                'trip_date' => Carbon::today(),
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }

        // Create trips for the next 7 days (5 buses per day per route)
        $tripId = 1;
        for ($day = 0; $day < 7; $day++) {
            $currentDate = Carbon::today()->addDays($day);
            
            // For each route
            for ($routeId = 1; $routeId <= 6; $routeId++) {
                // Create 5 trips per route per day (one for each bus)
                for ($busIndex = 0; $busIndex < 5; $busIndex++) {
                    $busId = $busIndex + 1;
                    $scheduleId = $busIndex + 1;
                    
                    // Create schedule for this specific date
                    $scheduleData = $schedules[$busIndex];
                    $specificScheduleId = DB::table('schedule')->insertGetId(array_merge($scheduleData, [
                        'trip_date' => $currentDate,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]));
                    
                    // Create trip
                    DB::table('trip')->insert([
                        'id' => $tripId,
                        'bus_id' => $busId,
                        'route_id' => $routeId,
                        'schedule_id' => $specificScheduleId,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                    
                    $tripId++;
                }
            }
        }

        // Create roles
        $roles = [
            ['type' => 'admin'],
            ['type' => 'agent'],
            ['type' => 'user'],
        ];

        foreach ($roles as $role) {
            DB::table('roles')->insert(array_merge($role, [
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }

        // Create sample users with Filipino names
        $users = [
            [
                'name' => 'Maria Santos',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'role' => 'admin'
            ],
            [
                'name' => 'Juan Dela Cruz',
                'email' => '<EMAIL>',
                'password' => Hash::make('agent123'),
                'role' => 'agent'
            ],
            [
                'name' => 'Ana Reyes',
                'email' => '<EMAIL>',
                'password' => Hash::make('agent123'),
                'role' => 'agent'
            ],
            [
                'name' => 'Carlos Mendoza',
                'email' => '<EMAIL>',
                'password' => Hash::make('customer123'),
                'role' => 'user'
            ],
            [
                'name' => 'Luz Garcia',
                'email' => '<EMAIL>',
                'password' => Hash::make('customer123'),
                'role' => 'user'
            ],
        ];

        foreach ($users as $userData) {
            $role = $userData['role'];
            unset($userData['role']);
            
            $userId = DB::table('users')->insertGetId(array_merge($userData, [
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now()
            ]));

            // Assign role
            $roleId = DB::table('roles')->where('type', $role)->first()->id;
            DB::table('users_roles')->insert([
                'user_id' => $userId,
                'role_id' => $roleId,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        $this->command->info('✅ Sample data seeded successfully!');
        $this->command->info('📊 Created:');
        $this->command->info('   • 5 buses with Filipino names');
        $this->command->info('   • 6 routes between major cities');
        $this->command->info('   • 5 daily schedules');
        $this->command->info('   • 210 trips (5 buses × 6 routes × 7 days)');
        $this->command->info('   • 5 sample users (1 admin, 2 agents, 2 customers)');
        $this->command->info('   • Fare structure for all routes');
    }
}
