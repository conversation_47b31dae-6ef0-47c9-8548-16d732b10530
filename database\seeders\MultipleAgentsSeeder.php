<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;

class MultipleAgentsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get agent role
        $agentRole = Role::where('type', 'agent')->first();
        
        if (!$agentRole) {
            echo "Agent role not found. Please run migrations first.\n";
            return;
        }

        // Create multiple agent accounts
        $agents = [
            [
                'name' => 'Terminal Agent 1',
                'email' => '<EMAIL>',
                'password' => 'agent123',
                'phone' => '***********'
            ],
            [
                'name' => 'Terminal Agent 2', 
                'email' => '<EMAIL>',
                'password' => 'agent123',
                'phone' => '***********'
            ],
            [
                'name' => 'Counter Agent',
                'email' => '<EMAIL>', 
                'password' => 'agent123',
                'phone' => '***********'
            ],
            [
                'name' => 'Booking Agent',
                'email' => '<EMAIL>',
                'password' => 'agent123', 
                'phone' => '***********'
            ]
        ];

        foreach ($agents as $agentData) {
            // Create agent user
            $agent = User::firstOrCreate(
                ['email' => $agentData['email']],
                [
                    'name' => $agentData['name'],
                    'password' => bcrypt($agentData['password']),
                    'phone' => $agentData['phone'],
                    'email_verified_at' => now(),
                ]
            );

            // Assign agent role if not already assigned
            if (!$agent->roles()->where('role_id', $agentRole->id)->exists()) {
                $agent->roles()->attach($agentRole->id);
            }

            echo "Agent created: {$agentData['email']} / {$agentData['password']}\n";
        }

        echo "\nAll agent accounts created successfully!\n";
        echo "Default password for all agents: agent123\n";
    }
}
