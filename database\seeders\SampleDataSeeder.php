<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class SampleDataSeeder extends Seeder
{
    public function run(): void
    {
        // Clear old data for a clean seed
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('users_roles')->truncate();
        DB::table('roles')->truncate();
        DB::table('tickets')->truncate();
        DB::table('trip')->truncate();
        DB::table('schedule')->truncate();
        DB::table('routes')->truncate();
        DB::table('buses')->truncate();
        DB::table('users')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        // Only one bus: G-Liner (GL)
        DB::table('buses')->insert([
            ['name' => 'G-Liner', 'seat' => 45, 'created_at' => now(), 'updated_at' => now()],
        ]);

        // Sample Routes (Tabuk <-> Baguio only)
        DB::table('routes')->insert([
            ['start_point' => 'Tabuk', 'end_point' => 'Baguio', 'created_at' => now(), 'updated_at' => now()],
            ['start_point' => 'Baguio', 'end_point' => 'Tabuk', 'created_at' => now(), 'updated_at' => now()],
        ]);

        // Sample Schedules (for the single bus)
        DB::table('schedule')->insert([
            ['start_time' => '08:00:00', 'end_time' => '14:00:00', 'created_at' => now(), 'updated_at' => now()],
            ['start_time' => '09:00:00', 'end_time' => '15:00:00', 'created_at' => now(), 'updated_at' => now()],
        ]);

        // Only G-Liner trips for both directions
        DB::table('trip')->insert([
            ['bus_id' => 1, 'route_id' => 1, 'schedule_id' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['bus_id' => 1, 'route_id' => 2, 'schedule_id' => 2, 'created_at' => now(), 'updated_at' => now()],
        ]);

        // Create admin role and user last
        $adminRoleId = DB::table('roles')->insertGetId([
            'type' => 'admin',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        $adminUserId = DB::table('users')->insertGetId([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('Admin@2025'),
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        DB::table('users_roles')->insert([
            'user_id' => $adminUserId,
            'role_id' => $adminRoleId,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        DB::table('buses')->insert([
            ['name' => 'G-Liner', 'seat' => 45, 'created_at' => now(), 'updated_at' => now()],
        ]);

        // Sample Routes (Tabuk <-> Baguio only)
        DB::table('routes')->insert([
            ['start_point' => 'Tabuk', 'end_point' => 'Baguio', 'created_at' => now(), 'updated_at' => now()],
            ['start_point' => 'Baguio', 'end_point' => 'Tabuk', 'created_at' => now(), 'updated_at' => now()],
        ]);

        // Sample Schedules (for the single bus)
        DB::table('schedule')->insert([
            ['start_time' => '08:00:00', 'end_time' => '14:00:00', 'created_at' => now(), 'updated_at' => now()],
            ['start_time' => '09:00:00', 'end_time' => '15:00:00', 'created_at' => now(), 'updated_at' => now()],
        ]);

        // Only G-Liner trips for both directions
        DB::table('trip')->insert([
            ['bus_id' => 1, 'route_id' => 1, 'schedule_id' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['bus_id' => 1, 'route_id' => 2, 'schedule_id' => 2, 'created_at' => now(), 'updated_at' => now()],
        ]);

        // (Removed duplicate schedules and trips)
    }

    // (Removed duplicate schedules and trips)
    }
