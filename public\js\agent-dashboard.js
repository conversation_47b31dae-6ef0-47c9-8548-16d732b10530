// Agent Dashboard JavaScript
console.log('Agent Dashboard JavaScript loaded successfully');

// Basic modal functions
function openWalkInBooking() {
    document.getElementById('walkInModal').classList.remove('hidden');
    toastr.success('Walk-in booking modal opened');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Test function to verify JavaScript is working
function testJavaScript() {
    console.log('JavaScript test function called');
    toastr.success('JavaScript is working perfectly!');
    return true;
}

// Time validation function
function validateTripTime(tripId) {
    const tripRow = document.querySelector(`button[onclick="bookWalkIn(${tripId})"]`)?.closest('tr');
    if (!tripRow) return false;

    // Check if the row has expired styling (opacity-60 class indicates expired)
    return !tripRow.classList.contains('opacity-60');
}

// Basic booking functions with time validation
function bookWalkIn(tripId) {
    // Check if trip is still bookable (time validation)
    if (!validateTripTime(tripId)) {
        toastr.error('This trip has already departed or is no longer available for booking.');
        return;
    }

    openWalkInBooking();
    const tripSelect = document.getElementById('tripSelect');
    if (tripSelect) {
        tripSelect.value = tripId;
    }
    toastr.info('Opening walk-in booking for trip ' + tripId);
}

function viewSeats(tripId) {
    toastr.info('Loading seat map for trip ' + tripId + '...');
    
    // Get trip data from the table row
    const tripRow = document.querySelector('button[onclick="viewSeats(' + tripId + ')"]').closest('tr');
    const tripData = extractTripDataFromRow(tripRow, tripId);
    
    if (tripData) {
        showSeatMap(tripData);
    } else {
        toastr.error('Could not load trip information');
        // Fallback with basic data
        showSeatMap({
            id: tripId,
            number: '#' + tripId.toString().padStart(4, '0'),
            route: { start_point: 'Unknown', end_point: 'Unknown' },
            bus: { name: 'Bus', capacity: 45 },
            schedule: { time: 'TBD' },
            seats: { total: 45, available: 35, booked: 10 }
        });
    }
}

function extractTripDataFromRow(row, tripId) {
    try {
        const cells = row.querySelectorAll('td');
        
        // Extract data from table cells
        const tripNumber = cells[0]?.textContent.trim() || '#' + tripId.toString().padStart(4, '0');
        const routeText = cells[1]?.textContent.trim() || '';
        const busText = cells[2]?.textContent.trim() || '';
        const scheduleText = cells[3]?.textContent.trim() || '';
        const availabilityText = cells[4]?.textContent.trim() || '';
        
        // Parse route (Start → End)
        const routeParts = routeText.split('→').map(part => part.trim());
        const startPoint = routeParts[0] || 'Unknown';
        const endPoint = routeParts[1] || 'Unknown';
        
        // Parse availability (e.g., "40/45 Available")
        const availabilityMatch = availabilityText.match(/(\d+)\/(\d+)/);
        const available = availabilityMatch ? parseInt(availabilityMatch[1]) : 35;
        const total = availabilityMatch ? parseInt(availabilityMatch[2]) : 45;
        const booked = total - available;
        
        return {
            id: tripId,
            number: tripNumber,
            route: {
                start_point: startPoint,
                end_point: endPoint
            },
            bus: {
                name: busText || 'Bus',
                capacity: total
            },
            schedule: {
                time: scheduleText || 'TBD'
            },
            seats: {
                total: total,
                available: available,
                booked: booked
            }
        };
    } catch (error) {
        console.error('Error extracting trip data:', error);
        return null;
    }
}

function showSeatMap(tripData) {
    const seatMapHtml = generateSeatMap(tripData);
    
    const modalHtml = '<div class="text-left">' +
        '<!-- Trip Info Card -->' +
        '<div class="trip-info-card">' +
            '<div class="grid grid-cols-2 gap-4 text-sm">' +
                '<div><strong>Trip:</strong> ' + tripData.number + '</div>' +
                '<div><strong>Bus:</strong> ' + tripData.bus.name + '</div>' +
                '<div><strong>Route:</strong> ' + tripData.route.start_point + ' → ' + tripData.route.end_point + '</div>' +
                '<div><strong>Schedule:</strong> ' + tripData.schedule.time + '</div>' +
            '</div>' +
        '</div>' +
        
        '<!-- Seat Legend -->' +
        '<div class="seat-legend">' +
            '<div class="legend-item">' +
                '<div class="legend-seat seat-available"></div>' +
                '<span>Available</span>' +
            '</div>' +
            '<div class="legend-item">' +
                '<div class="legend-seat seat-booked"></div>' +
                '<span>Booked</span>' +
            '</div>' +
            '<div class="legend-item">' +
                '<div class="legend-seat seat-selected"></div>' +
                '<span>Selected</span>' +
            '</div>' +
        '</div>' +
        
        '<!-- Seat Map -->' +
        '<div class="seat-map-container">' + seatMapHtml + '</div>' +
        
        '<!-- Seat Summary -->' +
        '<div class="seat-summary">' +
            '<div class="grid grid-cols-3 gap-4 text-center text-sm">' +
                '<div>' +
                    '<div class="text-2xl font-bold text-green-600">' + tripData.seats.available + '</div>' +
                    '<div class="text-gray-600">Available</div>' +
                '</div>' +
                '<div>' +
                    '<div class="text-2xl font-bold text-red-600">' + tripData.seats.booked + '</div>' +
                    '<div class="text-gray-600">Booked</div>' +
                '</div>' +
                '<div>' +
                    '<div class="text-2xl font-bold text-blue-600">' + Math.round((tripData.seats.booked / tripData.bus.capacity) * 100) + '%</div>' +
                    '<div class="text-gray-600">Occupied</div>' +
                '</div>' +
            '</div>' +
        '</div>' +
        
        '<!-- Instructions -->' +
        '<div class="mt-4 p-3 bg-yellow-50 rounded-lg text-sm text-gray-600 text-center">' +
            '💡 Click on any <span class="text-green-600 font-bold">green seat</span> to book it for a walk-in passenger' +
        '</div>' +
    '</div>';
    
    Swal.fire({
        title: '🚌 Seat Map - ' + tripData.number,
        html: modalHtml,
        showConfirmButton: true,
        confirmButtonText: 'Close',
        confirmButtonColor: '#6b7280',
        width: '650px',
        customClass: {
            popup: 'seat-map-modal'
        }
    });
}

function generateSeatMap(tripData) {
    const capacity = tripData.bus.capacity;
    const seatsPerRow = 4; // Typical bus layout: 2 seats + aisle + 2 seats
    const rows = Math.ceil(capacity / seatsPerRow);
    const bookedSeats = generateRandomBookedSeats(tripData.seats.booked, capacity);
    
    let seatMapHtml = '<div class="bus-layout">';
    
    // Driver area
    seatMapHtml += '<div class="driver-area">🚗 Driver Area</div>';
    
    // Seat rows
    seatMapHtml += '<div class="seats-container">';
    
    let seatNumber = 1;
    for (let row = 0; row < rows; row++) {
        seatMapHtml += '<div class="seat-row">';
        
        // Left side seats (2 seats)
        for (let leftSeat = 0; leftSeat < 2; leftSeat++) {
            if (seatNumber <= capacity) {
                const isBooked = bookedSeats.includes(seatNumber);
                const seatClass = isBooked ? 'seat-booked' : 'seat-available';
                
                seatMapHtml += '<div class="seat ' + seatClass + '" ' +
                    'data-seat="' + seatNumber + '" ' +
                    'data-trip-id="' + tripData.id + '" ' +
                    'onclick="' + (isBooked ? '' : 'selectSeatForBooking(' + tripData.id + ', ' + seatNumber + ')') + '" ' +
                    'title="Seat ' + seatNumber + ' - ' + (isBooked ? 'Booked' : 'Available - Click to book') + '"' +
                    (isBooked ? ' style="cursor: not-allowed;"' : '') + '>' +
                    seatNumber +
                '</div>';
                seatNumber++;
            }
        }
        
        // Aisle
        seatMapHtml += '<div class="aisle">||</div>';
        
        // Right side seats (2 seats)
        for (let rightSeat = 0; rightSeat < 2; rightSeat++) {
            if (seatNumber <= capacity) {
                const isBooked = bookedSeats.includes(seatNumber);
                const seatClass = isBooked ? 'seat-booked' : 'seat-available';
                
                seatMapHtml += '<div class="seat ' + seatClass + '" ' +
                    'data-seat="' + seatNumber + '" ' +
                    'data-trip-id="' + tripData.id + '" ' +
                    'onclick="' + (isBooked ? '' : 'selectSeatForBooking(' + tripData.id + ', ' + seatNumber + ')') + '" ' +
                    'title="Seat ' + seatNumber + ' - ' + (isBooked ? 'Booked' : 'Available - Click to book') + '"' +
                    (isBooked ? ' style="cursor: not-allowed;"' : '') + '>' +
                    seatNumber +
                '</div>';
                seatNumber++;
            }
        }
        
        seatMapHtml += '</div>'; // End seat-row
    }
    
    seatMapHtml += '</div>'; // End seats-container
    seatMapHtml += '</div>'; // End bus-layout
    
    return seatMapHtml;
}

function generateRandomBookedSeats(bookedCount, totalSeats) {
    const bookedSeats = [];
    const maxAttempts = totalSeats * 2; // Prevent infinite loop
    let attempts = 0;
    
    while (bookedSeats.length < bookedCount && attempts < maxAttempts) {
        const randomSeat = Math.floor(Math.random() * totalSeats) + 1;
        if (!bookedSeats.includes(randomSeat)) {
            bookedSeats.push(randomSeat);
        }
        attempts++;
    }
    
    return bookedSeats.sort((a, b) => a - b);
}

function selectSeatForBooking(tripId, seatNumber) {
    // Close the seat map modal first
    Swal.close();
    
    // Highlight the selected seat visually
    const selectedSeat = document.querySelector('[data-seat="' + seatNumber + '"][data-trip-id="' + tripId + '"]');
    if (selectedSeat) {
        selectedSeat.classList.remove('seat-available');
        selectedSeat.classList.add('seat-selected');
    }
    
    const bookingFormHtml = '<div class="text-left space-y-4">' +
        '<div class="bg-blue-50 p-4 rounded-lg border border-blue-200">' +
            '<h3 class="text-lg font-bold text-blue-700 mb-2">Selected Seat Information</h3>' +
            '<div class="grid grid-cols-2 gap-4 text-sm">' +
                '<div><strong>Trip:</strong> #' + tripId + '</div>' +
                '<div><strong>Seat Number:</strong> <span class="text-blue-600 font-bold">#' + seatNumber + '</span></div>' +
            '</div>' +
        '</div>' +
        
        '<div class="space-y-3">' +
            '<div>' +
                '<label class="block text-sm font-medium text-gray-700 mb-1">Passenger Name *</label>' +
                '<input type="text" id="passengerNameModal" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter passenger full name" required>' +
            '</div>' +
            
            '<div>' +
                '<label class="block text-sm font-medium text-gray-700 mb-1">Contact Number *</label>' +
                '<input type="tel" id="contactNumberModal" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., 09123456789" required>' +
            '</div>' +
            
            '<div>' +
                '<label class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>' +
                '<select id="paymentMethodModal" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">' +
                    '<option value="cash">Cash Payment</option>' +
                    '<option value="card">Credit/Debit Card</option>' +
                    '<option value="gcash">GCash</option>' +
                    '<option value="paymaya">PayMaya</option>' +
                '</select>' +
            '</div>' +
            
            '<div>' +
                '<label class="block text-sm font-medium text-gray-700 mb-1">Notes (Optional)</label>' +
                '<textarea id="notesModal" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" rows="2" placeholder="Any special instructions or notes..."></textarea>' +
            '</div>' +
        '</div>' +
        
        '<div class="bg-gray-50 p-3 rounded-lg text-xs text-gray-600">' +
            '<strong>Note:</strong> Please ensure all information is correct before confirming the booking.' +
        '</div>' +
    '</div>';
    
    Swal.fire({
        title: '🎫 Book Seat ' + seatNumber,
        html: bookingFormHtml,
        showCancelButton: true,
        confirmButtonText: 'Confirm Booking',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#FCB404',
        cancelButtonColor: '#6b7280',
        width: '500px',
        preConfirm: function() {
            const name = document.getElementById('passengerNameModal').value.trim();
            const contact = document.getElementById('contactNumberModal').value.trim();
            const payment = document.getElementById('paymentMethodModal').value;
            const notes = document.getElementById('notesModal').value.trim();
            
            if (!name || !contact) {
                Swal.showValidationMessage('Please fill in all required fields (Name and Contact)');
                return false;
            }
            
            if (contact.length < 11) {
                Swal.showValidationMessage('Please enter a valid contact number');
                return false;
            }
            
            return { name: name, contact: contact, payment: payment, notes: notes };
        }
    }).then(function(result) {
        if (result.isConfirmed) {
            processWalkInBookingFromSeatMap(tripId, seatNumber, result.value);
        } else {
            // If cancelled, restore seat to available state
            if (selectedSeat) {
                selectedSeat.classList.remove('seat-selected');
                selectedSeat.classList.add('seat-available');
            }
        }
    });
}

function processWalkInBookingFromSeatMap(tripId, seatNumber, passengerData) {
    Swal.fire({
        title: 'Processing Booking...',
        html: 'Please wait while we process your walk-in booking...',
        allowOutsideClick: false,
        showConfirmButton: false,
        willOpen: function() {
            Swal.showLoading();
        }
    });
    
    // Simulate booking process
    setTimeout(function() {
        const ticketNumber = 'GL' + Date.now().toString().slice(-6);
        const currentTime = new Date();
        
        const ticketHtml = '<div class="text-left space-y-2 bg-gray-50 p-4 rounded-lg">' +
            '<div class="text-center mb-3">' +
                '<h3 class="text-lg font-bold text-green-600">GL BUS TICKET</h3>' +
                '<p class="text-sm text-gray-600">Ticket #' + ticketNumber + '</p>' +
                '<div class="w-full h-1 bg-green-200 rounded my-2"></div>' +
            '</div>' +
            
            '<div class="grid grid-cols-2 gap-2 text-sm">' +
                '<div><strong>Passenger:</strong></div>' +
                '<div>' + passengerData.name + '</div>' +
                
                '<div><strong>Contact:</strong></div>' +
                '<div>' + passengerData.contact + '</div>' +
                
                '<div><strong>Trip:</strong></div>' +
                '<div>#' + tripId + '</div>' +
                
                '<div><strong>Seat:</strong></div>' +
                '<div class="text-blue-600 font-bold">#' + seatNumber + '</div>' +
                
                '<div><strong>Payment:</strong></div>' +
                '<div>' + passengerData.payment + '</div>' +
                
                '<div><strong>Date:</strong></div>' +
                '<div>' + currentTime.toLocaleDateString() + '</div>' +
                
                '<div><strong>Time:</strong></div>' +
                '<div>' + currentTime.toLocaleTimeString() + '</div>' +
            '</div>' +
            
            (passengerData.notes ? 
                '<div class="mt-3 pt-2 border-t border-gray-300">' +
                    '<strong class="text-xs">Notes:</strong>' +
                    '<p class="text-xs text-gray-600">' + passengerData.notes + '</p>' +
                '</div>' : '') +
            
            '<div class="w-full h-1 bg-green-200 rounded my-2"></div>' +
            '<p class="text-xs text-gray-500 text-center">Thank you for choosing GL Bus!</p>' +
        '</div>';
        
        Swal.fire({
            icon: 'success',
            title: 'Booking Confirmed! 🎉',
            html: ticketHtml,
            confirmButtonText: 'Print Ticket',
            confirmButtonColor: '#FCB404',
            showCancelButton: true,
            cancelButtonText: 'Close',
            cancelButtonColor: '#6b7280',
            width: '450px'
        }).then(function(result) {
            if (result.isConfirmed) {
                printTicketFromSeatMap(ticketNumber, tripId, seatNumber, passengerData);
            }
            
            // Update the seat availability in the current view
            updateSeatAvailabilityInTable(tripId);
            
            // Show success notification
            toastr.success('Seat ' + seatNumber + ' booked successfully for ' + passengerData.name + '!');
        });
    }, 2500); // Slightly longer simulation for realism
}

function printTicketFromSeatMap(ticketNumber, tripId, seatNumber, passengerData) {
    const printContent = '<div style="font-family: Arial, sans-serif; max-width: 350px; margin: 0 auto; padding: 25px; border: 3px solid #FCB404; border-radius: 10px;">' +
        '<div style="text-align: center; margin-bottom: 20px;">' +
            '<h1 style="color: #FCB404; margin: 0; font-size: 24px;">GL BUS</h1>' +
            '<p style="margin: 5px 0; font-size: 12px; color: #666;">Premium Bus Reservation System</p>' +
            '<div style="border-bottom: 2px solid #FCB404; margin: 10px 0;"></div>' +
        '</div>' +

        '<div style="text-align: center; margin-bottom: 20px;">' +
            '<h2 style="margin: 0 0 10px 0; font-size: 18px;">BOARDING TICKET</h2>' +
            '<p style="font-weight: bold; font-size: 16px; color: #FCB404;">Ticket #' + ticketNumber + '</p>' +
        '</div>' +

        '<div style="font-size: 14px; line-height: 1.8; margin-bottom: 20px;">' +
            '<div style="display: flex; justify-content: space-between; border-bottom: 1px dotted #ccc; padding: 5px 0;">' +
                '<span><strong>Passenger:</strong></span>' +
                '<span>' + passengerData.name + '</span>' +
            '</div>' +
            '<div style="display: flex; justify-content: space-between; border-bottom: 1px dotted #ccc; padding: 5px 0;">' +
                '<span><strong>Contact:</strong></span>' +
                '<span>' + passengerData.contact + '</span>' +
            '</div>' +
            '<div style="display: flex; justify-content: space-between; border-bottom: 1px dotted #ccc; padding: 5px 0;">' +
                '<span><strong>Trip:</strong></span>' +
                '<span>#' + tripId + '</span>' +
            '</div>' +
            '<div style="display: flex; justify-content: space-between; border-bottom: 1px dotted #ccc; padding: 5px 0;">' +
                '<span><strong>Seat Number:</strong></span>' +
                '<span style="color: #FCB404; font-weight: bold; font-size: 16px;">#' + seatNumber + '</span>' +
            '</div>' +
            '<div style="display: flex; justify-content: space-between; border-bottom: 1px dotted #ccc; padding: 5px 0;">' +
                '<span><strong>Payment:</strong></span>' +
                '<span>' + passengerData.payment + '</span>' +
            '</div>' +
            '<div style="display: flex; justify-content: space-between; border-bottom: 1px dotted #ccc; padding: 5px 0;">' +
                '<span><strong>Date:</strong></span>' +
                '<span>' + new Date().toLocaleDateString() + '</span>' +
            '</div>' +
            '<div style="display: flex; justify-content: space-between; padding: 5px 0;">' +
                '<span><strong>Time:</strong></span>' +
                '<span>' + new Date().toLocaleTimeString() + '</span>' +
            '</div>' +
        '</div>' +

        (passengerData.notes ? 
            '<div style="margin: 15px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; border-left: 3px solid #FCB404;">' +
                '<strong style="font-size: 12px;">Notes:</strong>' +
                '<p style="margin: 5px 0 0 0; font-size: 12px; color: #666;">' + passengerData.notes + '</p>' +
            '</div>' : '') +

        '<div style="border-top: 2px dashed #ccc; margin: 20px 0; padding-top: 15px;">' +
            '<p style="text-align: center; font-size: 12px; color: #666; margin: 0;">' +
                '<strong>Important:</strong><br>' +
                '• Please arrive 30 minutes before departure<br>' +
                '• Present this ticket to the conductor<br>' +
                '• Keep this ticket for the entire journey' +
            '</p>' +
        '</div>' +
        
        '<div style="text-align: center; margin-top: 15px;">' +
            '<p style="font-size: 11px; color: #999; margin: 0;">' +
                'Thank you for choosing GL Bus!<br>' +
                'Safe travels! 🚌' +
            '</p>' +
        '</div>' +
    '</div>';

    const printWindow = window.open('', '_blank', 'width=400,height=600');
    if (printWindow) {
        const htmlContent = '<!DOCTYPE html>' +
            '<html>' +
            '<head>' +
                '<title>GL Bus Ticket - ' + ticketNumber + '</title>' +
                '<style>' +
                    'body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }' +
                    '@media print { body { margin: 0; padding: 10px; } .no-print { display: none; } }' +
                '</style>' +
            '</head>' +
            '<body>' +
                printContent +
                '<div class="no-print" style="text-align: center; margin-top: 20px;">' +
                    '<button onclick="window.print()" style="background: #FCB404; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">Print Ticket</button>' +
                    '<button onclick="window.close()" style="background: #6b7280; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-left: 10px;">Close</button>' +
                '</div>' +
                '<script>' +
                    'setTimeout(function() { window.print(); }, 500);' +
                '</script>' +
            '</body>' +
            '</html>';
        
        printWindow.document.write(htmlContent);
        printWindow.document.close();
        printWindow.focus();
    } else {
        toastr.error('Please allow popups to print tickets');
    }
}

function updateSeatAvailabilityInTable(tripId) {
    // Find the trip row and update the availability
    const tripRows = document.querySelectorAll('tbody tr');
    tripRows.forEach(row => {
        const viewButton = row.querySelector('button[onclick="viewSeats(' + tripId + ')"]');
        if (viewButton) {
            const availabilityCell = row.cells[4]; // Availability column
            if (availabilityCell) {
                const currentText = availabilityCell.textContent;
                const match = currentText.match(/(\d+)\/(\d+)/);
                if (match) {
                    const available = parseInt(match[1]) - 1; // Decrease available by 1
                    const total = parseInt(match[2]);
                    
                    if (available >= 0) {
                        let statusClass, statusText;
                        const percentage = (available / total) * 100;
                        
                        if (percentage > 50) {
                            statusClass = 'status-available';
                            statusText = available + '/' + total + ' Available';
                        } else if (percentage > 0) {
                            statusClass = 'status-partial';
                            statusText = available + '/' + total + ' Few Left';
                        } else {
                            statusClass = 'status-full';
                            statusText = 'Full';
                        }
                        
                        availabilityCell.innerHTML = '<span class="status-badge ' + statusClass + '">' + statusText + '</span>';
                    }
                }
            }
        }
    });
}

function printTicketById(ticketId) {
    toastr.success('Opening print view for ticket ' + ticketId);
    window.open('/agent/print-ticket/' + ticketId, '_blank');
}

function viewTicket(ticketId) {
    toastr.info('Viewing ticket details for ' + ticketId);
}

function processWalkInBooking() {
    const tripId = document.getElementById('tripSelect').value;
    const passengerName = document.getElementById('passengerName').value;
    const contactNumber = document.getElementById('contactNumber').value;
    const seatNumber = document.getElementById('seatNumber').value;

    if (!tripId || !passengerName || !contactNumber || !seatNumber) {
        toastr.error('Please fill in all required fields');
        return;
    }

    toastr.info('Processing walk-in booking...');
    
    setTimeout(function() {
        toastr.success('Walk-in booking completed successfully!');
        closeModal('walkInModal');
        
        // Clear form
        document.getElementById('tripSelect').value = '';
        document.getElementById('passengerName').value = '';
        document.getElementById('contactNumber').value = '';
        document.getElementById('seatNumber').value = '';
        
        // Refresh page to show updated data
        setTimeout(function() {
            window.location.reload();
        }, 1500);
    }, 2000);
}

// Toggle trips view
function toggleTripsView(view) {
    if (view === 'today') {
        window.location.href = '/agent/dashboard';
    } else {
        window.location.href = '/agent/dashboard?view=all';
    }
}

function viewReports() {
    toastr.info('Opening reports dashboard...');
    alert('Reports feature coming soon!');
}

// Close modal when clicking outside
window.addEventListener('click', function(e) {
    if (e.target.classList.contains('fixed')) {
        e.target.classList.add('hidden');
    }
});

// Real-time clock function
function updateClock() {
    const now = new Date();

    // Format the time for Manila timezone
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        second: '2-digit',
        hour12: true,
        timeZone: 'Asia/Manila'
    };

    const formattedTime = now.toLocaleString('en-US', options);

    // Update the clock display with subtle animation
    const clockElement = document.getElementById('current-time');
    if (clockElement) {
        // Add a subtle flash effect every second
        clockElement.style.transition = 'color 0.2s ease';
        clockElement.style.color = '#059669'; // Green flash

        setTimeout(() => {
            clockElement.style.color = '#1d4ed8'; // Back to blue
            clockElement.textContent = formattedTime;
        }, 100);
    }
}

// Start the real-time clock
function startClock() {
    updateClock(); // Update immediately
    setInterval(updateClock, 1000); // Update every second
}

// Refresh trip status function
function refreshTripStatus() {
    toastr.info('Refreshing trip status...', 'Update');

    // Add a small delay to show the loading message
    setTimeout(() => {
        window.location.reload();
    }, 500);
}

// Toggle trips view function
function toggleTripsView(view) {
    const currentUrl = new URL(window.location);

    if (view === 'today') {
        currentUrl.searchParams.delete('view');
    } else {
        currentUrl.searchParams.set('view', 'all');
    }

    toastr.info(`Switching to ${view === 'today' ? 'today\'s' : 'all'} trips...`);
    window.location.href = currentUrl.toString();
}

// Terminal Actions Functions
function viewDailyReports() {
    // Show loading state
    Swal.fire({
        title: '📊 Loading Daily Sales Report...',
        html: '<div class="text-center"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div><p class="mt-2">Fetching today\'s data...</p></div>',
        showConfirmButton: false,
        allowOutsideClick: false
    });

    // Fetch real data from API
    fetch('/api/agent/daily-sales')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const salesData = data.data;

                Swal.fire({
                    title: '📊 Daily Sales Report',
                    html: `
                        <div class="text-left space-y-4">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="font-bold text-gray-800 mb-3">Today's Summary - ${salesData.date}</h3>

                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div class="bg-blue-100 p-3 rounded">
                                        <div class="font-bold text-blue-800">Total Bookings</div>
                                        <div class="text-2xl font-bold text-blue-600">${salesData.totalBookings}</div>
                                    </div>
                                    <div class="bg-green-100 p-3 rounded">
                                        <div class="font-bold text-green-800">Total Revenue</div>
                                        <div class="text-2xl font-bold text-green-600">₱${parseFloat(salesData.totalRevenue || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</div>
                                    </div>
                                    <div class="bg-yellow-100 p-3 rounded">
                                        <div class="font-bold text-yellow-800">Walk-in Bookings</div>
                                        <div class="text-2xl font-bold text-yellow-600">${salesData.walkInBookings}</div>
                                    </div>
                                    <div class="bg-purple-100 p-3 rounded">
                                        <div class="font-bold text-purple-800">Online Bookings</div>
                                        <div class="text-2xl font-bold text-purple-600">${salesData.onlineBookings}</div>
                                    </div>
                                </div>

                                <div class="mt-4 pt-3 border-t">
                                    <div class="text-sm text-gray-600">
                                        <div class="flex justify-between"><span>Peak Hour:</span><span class="font-medium">${salesData.peakHour}</span></div>
                                        <div class="flex justify-between"><span>Most Popular Route:</span><span class="font-medium">${salesData.mostPopularRoute}</span></div>
                                        <div class="flex justify-between"><span>Average Ticket Price:</span><span class="font-medium">₱${parseFloat(salesData.averageTicketPrice || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `,
                    showCancelButton: true,
                    confirmButtonText: 'Print Report',
                    cancelButtonText: 'Close',
                    confirmButtonColor: '#FCB404',
                    cancelButtonColor: '#6b7280',
                    width: '500px'
                }).then((result) => {
                    if (result.isConfirmed) {
                        printDailyReport(salesData);
                    }
                });
            } else {
                throw new Error(data.message || 'Failed to load sales data');
            }
        })
        .catch(error => {
            console.error('Error fetching daily sales:', error);
            Swal.fire({
                title: '❌ Error',
                text: 'Failed to load daily sales data. Please try again.',
                icon: 'error',
                confirmButtonColor: '#FCB404'
            });
        });
}

function printTicketSummary() {
    Swal.fire({
        title: '🎫 Print Ticket Summary',
        html: `
            <div class="text-left space-y-4">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="font-bold text-gray-800 mb-3">Select Print Options</h3>

                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="radio" name="printType" value="today" class="mr-2" checked>
                            <span>Today's Tickets (24 tickets)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="printType" value="trip" class="mr-2">
                            <span>Specific Trip Tickets</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="printType" value="passenger" class="mr-2">
                            <span>Passenger Manifest</span>
                        </label>
                    </div>

                    <div class="mt-4 p-3 bg-blue-50 rounded">
                        <div class="text-sm text-blue-700">
                            <div class="font-medium">Print Summary will include:</div>
                            <ul class="list-disc list-inside mt-1 text-xs">
                                <li>Ticket numbers and passenger details</li>
                                <li>Trip information and seat assignments</li>
                                <li>Payment status and booking time</li>
                                <li>Agent information and terminal stamp</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Print Summary',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#8b5cf6',
        cancelButtonColor: '#6b7280',
        width: '450px'
    }).then((result) => {
        if (result.isConfirmed) {
            const printType = document.querySelector('input[name="printType"]:checked').value;
            generateTicketSummary(printType);
        }
    });
}

function manageSchedules() {
    Swal.fire({
        title: '📅 Schedule Management',
        html: `
            <div class="text-left space-y-4">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="font-bold text-gray-800 mb-3">Quick Schedule Actions</h3>

                    <div class="grid grid-cols-1 gap-3">
                        <button onclick="viewTodaySchedules()" class="w-full p-3 bg-blue-100 hover:bg-blue-200 rounded-lg text-left transition-colors">
                            <div class="font-medium text-blue-800">View Today's Schedules</div>
                            <div class="text-sm text-blue-600">See all trips scheduled for today</div>
                        </button>

                        <button onclick="viewUpcomingSchedules()" class="w-full p-3 bg-green-100 hover:bg-green-200 rounded-lg text-left transition-colors">
                            <div class="font-medium text-green-800">Upcoming Schedules</div>
                            <div class="text-sm text-green-600">View next 7 days schedules</div>
                        </button>

                        <button onclick="checkAvailability()" class="w-full p-3 bg-yellow-100 hover:bg-yellow-200 rounded-lg text-left transition-colors">
                            <div class="font-medium text-yellow-800">Check Availability</div>
                            <div class="text-sm text-yellow-600">Real-time seat availability</div>
                        </button>
                    </div>

                    <div class="mt-4 p-3 bg-orange-50 rounded">
                        <div class="text-sm text-orange-700">
                            <div class="font-medium">Schedule Information:</div>
                            <div class="text-xs mt-1">All schedules are in Manila timezone and updated in real-time</div>
                        </div>
                    </div>
                </div>
            </div>
        `,
        showConfirmButton: true,
        confirmButtonText: 'Close',
        confirmButtonColor: '#f97316',
        width: '450px'
    });
}

// Helper functions for the new actions
function printDailyReport(salesData = null) {
    // Use provided data or fetch fresh data
    if (salesData) {
        generatePrintReport(salesData);
    } else {
        // Fetch fresh data if not provided
        fetch('/api/agent/daily-sales')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    generatePrintReport(data.data);
                } else {
                    toastr.error('Failed to load data for printing');
                }
            })
            .catch(error => {
                console.error('Error fetching data for print:', error);
                toastr.error('Failed to load data for printing');
            });
    }
}

function generatePrintReport(salesData) {
    const reportContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #FCB404; padding-bottom: 20px;">
                <h1 style="color: #FCB404; margin: 0;">GL BUS</h1>
                <h2 style="margin: 5px 0;">Daily Sales Report</h2>
                <p style="margin: 0; color: #666;">${salesData.date}</p>
            </div>

            <div style="margin-bottom: 20px;">
                <h3>Summary</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr><td style="padding: 5px; border-bottom: 1px solid #ddd;">Total Bookings:</td><td style="padding: 5px; border-bottom: 1px solid #ddd; font-weight: bold;">${salesData.totalBookings}</td></tr>
                    <tr><td style="padding: 5px; border-bottom: 1px solid #ddd;">Total Revenue:</td><td style="padding: 5px; border-bottom: 1px solid #ddd; font-weight: bold;">₱${parseFloat(salesData.totalRevenue || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td></tr>
                    <tr><td style="padding: 5px; border-bottom: 1px solid #ddd;">Walk-in Bookings:</td><td style="padding: 5px; border-bottom: 1px solid #ddd; font-weight: bold;">${salesData.walkInBookings}</td></tr>
                    <tr><td style="padding: 5px; border-bottom: 1px solid #ddd;">Online Bookings:</td><td style="padding: 5px; border-bottom: 1px solid #ddd; font-weight: bold;">${salesData.onlineBookings}</td></tr>
                    <tr><td style="padding: 5px; border-bottom: 1px solid #ddd;">Peak Hour:</td><td style="padding: 5px; border-bottom: 1px solid #ddd; font-weight: bold;">${salesData.peakHour}</td></tr>
                    <tr><td style="padding: 5px; border-bottom: 1px solid #ddd;">Most Popular Route:</td><td style="padding: 5px; border-bottom: 1px solid #ddd; font-weight: bold;">${salesData.mostPopularRoute}</td></tr>
                    <tr><td style="padding: 5px; border-bottom: 1px solid #ddd;">Average Ticket Price:</td><td style="padding: 5px; border-bottom: 1px solid #ddd; font-weight: bold;">₱${parseFloat(salesData.averageTicketPrice || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td></tr>
                </table>
            </div>

            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
                <p style="font-size: 12px; color: #666;">Generated on ${new Date().toLocaleString('en-US', {timeZone: 'Asia/Manila'})}</p>
                <p style="font-size: 12px; color: #666;">GL Bus Terminal Management System</p>
            </div>
        </div>
    `;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head><title>GL Bus Daily Report - ${salesData.date}</title></head>
            <body>${reportContent}<script>window.onload = function() { window.print(); window.close(); }</script></body>
        </html>
    `);
    printWindow.document.close();
    toastr.success('Daily report sent to printer!');
}

function generateTicketSummary(type) {
    toastr.success(`Generating ${type} ticket summary...`);

    setTimeout(() => {
        toastr.success('Ticket summary sent to printer!');
    }, 2000);
}

// Schedule management helper functions
function viewTodaySchedules() {
    Swal.close();
    toastr.info('Loading today\'s schedules...');

    setTimeout(() => {
        Swal.fire({
            title: '📅 Today\'s Schedules',
            html: `
                <div class="text-left">
                    <div class="space-y-3">
                        <div class="bg-green-50 p-3 rounded border-l-4 border-green-400">
                            <div class="font-medium text-green-800">08:00 AM - Manila → Baguio</div>
                            <div class="text-sm text-green-600">Available seats: 12/45 • Status: Boarding</div>
                        </div>
                        <div class="bg-red-50 p-3 rounded border-l-4 border-red-400">
                            <div class="font-medium text-red-800">02:00 PM - Manila → Baguio</div>
                            <div class="text-sm text-red-600">Available seats: 0/45 • Status: Departed</div>
                        </div>
                        <div class="bg-blue-50 p-3 rounded border-l-4 border-blue-400">
                            <div class="font-medium text-blue-800">10:00 PM - Manila → Baguio</div>
                            <div class="text-sm text-blue-600">Available seats: 35/45 • Status: Available</div>
                        </div>
                    </div>
                </div>
            `,
            confirmButtonText: 'Close',
            confirmButtonColor: '#3b82f6'
        });
    }, 1000);
}

function viewUpcomingSchedules() {
    Swal.close();
    toastr.info('Loading upcoming schedules...');

    setTimeout(() => {
        Swal.fire({
            title: '📅 Next 7 Days Schedules',
            html: `
                <div class="text-left">
                    <div class="space-y-3">
                        <div class="bg-gray-50 p-3 rounded">
                            <div class="font-medium text-gray-800">Tomorrow - ${new Date(Date.now() + 86400000).toLocaleDateString()}</div>
                            <div class="text-sm text-gray-600">6 trips scheduled • 180 total seats</div>
                        </div>
                        <div class="bg-gray-50 p-3 rounded">
                            <div class="font-medium text-gray-800">Day After - ${new Date(Date.now() + 172800000).toLocaleDateString()}</div>
                            <div class="text-sm text-gray-600">8 trips scheduled • 240 total seats</div>
                        </div>
                        <div class="bg-blue-50 p-3 rounded">
                            <div class="font-medium text-blue-800">This Week Total</div>
                            <div class="text-sm text-blue-600">42 trips • 1,260 seats • 85% booking rate</div>
                        </div>
                    </div>
                </div>
            `,
            confirmButtonText: 'Close',
            confirmButtonColor: '#10b981'
        });
    }, 1000);
}

function checkAvailability() {
    Swal.close();
    toastr.info('Checking real-time availability...');

    setTimeout(() => {
        Swal.fire({
            title: '🔍 Real-time Availability',
            html: `
                <div class="text-left">
                    <div class="mb-4">
                        <div class="text-sm text-gray-600 mb-2">Last updated: ${new Date().toLocaleTimeString('en-US', {timeZone: 'Asia/Manila'})}</div>
                    </div>
                    <div class="space-y-3">
                        <div class="bg-green-100 p-3 rounded">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-medium text-green-800">High Availability</div>
                                    <div class="text-sm text-green-600">30+ seats available</div>
                                </div>
                                <div class="text-2xl font-bold text-green-600">3 trips</div>
                            </div>
                        </div>
                        <div class="bg-yellow-100 p-3 rounded">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-medium text-yellow-800">Limited Availability</div>
                                    <div class="text-sm text-yellow-600">5-29 seats available</div>
                                </div>
                                <div class="text-2xl font-bold text-yellow-600">2 trips</div>
                            </div>
                        </div>
                        <div class="bg-red-100 p-3 rounded">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-medium text-red-800">Fully Booked</div>
                                    <div class="text-sm text-red-600">0 seats available</div>
                                </div>
                                <div class="text-2xl font-bold text-red-600">1 trip</div>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            confirmButtonText: 'Refresh',
            confirmButtonColor: '#f59e0b',
            showCancelButton: true,
            cancelButtonText: 'Close'
        }).then((result) => {
            if (result.isConfirmed) {
                checkAvailability(); // Refresh the data
            }
        });
    }, 1500);
}

// Recent Bookings Functions
function viewTicket(ticketId) {
    toastr.info(`Loading ticket details for #${ticketId}...`);

    // Fetch real ticket data from server
    fetch(`/agent/ticket-details/${ticketId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const ticket = data.ticket;
                const payment = ticket.payments && ticket.payments.length > 0 ? ticket.payments[0] : null;
                const passengerName = payment && payment.passenger_name ? payment.passenger_name : ticket.user.name;
                const bookingType = ticket.booking_type === 'walk_in' ? 'Walk-in' : 'Online';
                const agentInfo = ticket.booked_by_agent ? 'Agent Terminal' : 'Online Booking';

                Swal.fire({
                    title: `🎫 Ticket Details - #${ticketId}`,
                    html: `
                        <div class="text-left space-y-4">
                            <div class="bg-gray-50 p-4 rounded-lg border">
                                <div class="text-center mb-4">
                                    <h3 class="text-lg font-bold text-blue-600">GL BUS TICKET</h3>
                                    <p class="text-sm text-gray-600">Ticket #GL${ticketId.toString().padStart(6, '0')}</p>
                                </div>

                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <div class="font-medium text-gray-700">Passenger Name:</div>
                                        <div class="text-gray-900">${passengerName}</div>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-700">Contact Number:</div>
                                        <div class="text-gray-900">${payment ? payment.passenger_mobile : (ticket.user.phone || 'N/A')}</div>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-700">Route:</div>
                                        <div class="text-gray-900">${ticket.trip.route.start_point} → ${ticket.trip.route.end_point}</div>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-700">Seat Number:</div>
                                        <div class="text-gray-900 font-bold">#${ticket.seat_number}</div>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-700">Departure Date:</div>
                                        <div class="text-gray-900">${new Date(ticket.trip.schedule.trip_date || Date.now()).toLocaleDateString('en-US', {timeZone: 'Asia/Manila'})}</div>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-700">Departure Time:</div>
                                        <div class="text-gray-900">${ticket.trip.schedule.start_time}</div>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-700">Booking Type:</div>
                                        <div class="text-gray-900">${bookingType}</div>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-700">Payment Status:</div>
                                        <div class="text-green-600 font-medium">${payment ? payment.status.charAt(0).toUpperCase() + payment.status.slice(1) : 'Pending'}</div>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-700">Booking Date:</div>
                                        <div class="text-gray-900">${new Date(ticket.created_at).toLocaleDateString('en-US', {timeZone: 'Asia/Manila'})}</div>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-700">Agent:</div>
                                        <div class="text-gray-900">${agentInfo}</div>
                                    </div>
                                </div>

                        <div class="mt-4 pt-3 border-t">
                            <div class="text-xs text-gray-500 text-center">
                                <p>Please arrive 30 minutes before departure</p>
                                <p>Present this ticket and valid ID for boarding</p>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Print Ticket',
            cancelButtonText: 'Close',
            confirmButtonColor: '#3b82f6',
            cancelButtonColor: '#6b7280',
            width: '600px'
        }).then((result) => {
            if (result.isConfirmed) {
                printTicketById(ticketId);
            }
        });
            } else {
                toastr.error('Failed to load ticket details');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('Error loading ticket details');
        });
}

function printTicketById(ticketId) {
    toastr.info(`Preparing ticket #${ticketId} for printing...`);

    // Get ticket data from the table row
    const ticketRow = document.querySelector(`button[onclick="printTicketById(${ticketId})"]`).closest('tr');
    const ticketData = extractTicketDataFromRow(ticketRow, ticketId);

    const ticketContent = `
        <div style="font-family: Arial, sans-serif; max-width: 400px; margin: 0 auto; padding: 20px; border: 2px solid #3b82f6;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #3b82f6; margin: 0;">GL BUS</h2>
                <p style="margin: 5px 0; font-size: 12px;">Bus Reservation System</p>
                <hr style="border: 1px solid #3b82f6;">
            </div>

            <div style="margin-bottom: 15px;">
                <h3 style="text-align: center; margin: 0 0 10px 0;">TICKET</h3>
                <p style="text-align: center; font-weight: bold;">#GL${ticketId.toString().padStart(6, '0')}</p>
            </div>

            <div style="font-size: 14px; line-height: 1.6;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 3px 0; font-weight: bold;">Passenger:</td>
                        <td style="padding: 3px 0;">${ticketData.passengerName}</td>
                    </tr>
                    <tr>
                        <td style="padding: 3px 0; font-weight: bold;">Contact:</td>
                        <td style="padding: 3px 0;">${ticketData.contactNumber}</td>
                    </tr>
                    <tr>
                        <td style="padding: 3px 0; font-weight: bold;">Route:</td>
                        <td style="padding: 3px 0;">${ticketData.route.full}</td>
                    </tr>
                    <tr>
                        <td style="padding: 3px 0; font-weight: bold;">Seat:</td>
                        <td style="padding: 3px 0; font-weight: bold;">#${ticketData.seatNumber}</td>
                    </tr>
                    <tr>
                        <td style="padding: 3px 0; font-weight: bold;">Date:</td>
                        <td style="padding: 3px 0;">${new Date().toLocaleDateString('en-US', {timeZone: 'Asia/Manila'})}</td>
                    </tr>
                    <tr>
                        <td style="padding: 3px 0; font-weight: bold;">Time:</td>
                        <td style="padding: 3px 0;">08:00 AM</td>
                    </tr>
                    <tr>
                        <td style="padding: 3px 0; font-weight: bold;">Status:</td>
                        <td style="padding: 3px 0; color: #10b981; font-weight: bold;">CONFIRMED</td>
                    </tr>
                </table>
            </div>

            <hr style="margin: 15px 0; border: 1px dashed #ccc;">

            <div style="text-align: center; font-size: 12px; color: #666;">
                <p style="margin: 5px 0;">Thank you for choosing GL Bus!</p>
                <p style="margin: 5px 0;">Please arrive 30 minutes before departure</p>
                <p style="margin: 5px 0;">Present this ticket and valid ID for boarding</p>
            </div>

            <div style="text-align: center; margin-top: 15px; padding-top: 10px; border-top: 1px solid #ddd;">
                <p style="font-size: 10px; color: #999;">Printed: ${new Date().toLocaleString('en-US', {timeZone: 'Asia/Manila'})}</p>
            </div>
        </div>
    `;

    const printWindow = window.open('', '_blank');
    const htmlContent = `
        <html>
            <head>
                <title>GL Bus Ticket - #GL${ticketId.toString().padStart(6, '0')}</title>
                <style>
                    @media print {
                        body { margin: 0; }
                        @page { margin: 0.5in; }
                    }
                </style>
            </head>
            <body>
                ${ticketContent}
                <script>
                    window.onload = function() {
                        window.print();
                        setTimeout(function() { window.close(); }, 1000);
                    }
                </script>
            </body>
        </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();

    toastr.success(`Ticket #${ticketId} sent to printer!`);
}

// Helper function to extract ticket data from table row
function extractTicketDataFromRow(row, ticketId) {
    try {
        const cells = row.querySelectorAll('td');

        const passengerName = cells[1]?.textContent.trim() || 'Unknown Passenger';
        const routeText = cells[2]?.textContent.trim() || '';
        const seatNumber = cells[3]?.textContent.trim() || 'N/A';
        const statusText = cells[4]?.textContent.trim() || 'Confirmed';

        const routeParts = routeText.split('→').map(part => part.trim());
        const startPoint = routeParts[0] || 'Unknown';
        const endPoint = routeParts[1] || 'Unknown';

        return {
            id: ticketId,
            passengerName: passengerName,
            route: {
                start: startPoint,
                end: endPoint,
                full: routeText
            },
            seatNumber: seatNumber,
            status: statusText,
            bookingDate: new Date().toLocaleDateString('en-US', {timeZone: 'Asia/Manila'}),
            departureTime: '08:00 AM', // Default time, could be extracted from trip data
            contactNumber: '+63 ************' // Default, could be from user data
        };
    } catch (error) {
        console.error('Error extracting ticket data:', error);
        return {
            id: ticketId,
            passengerName: 'Unknown Passenger',
            route: { start: 'Unknown', end: 'Unknown', full: 'Unknown → Unknown' },
            seatNumber: 'N/A',
            status: 'Confirmed',
            bookingDate: new Date().toLocaleDateString('en-US', {timeZone: 'Asia/Manila'}),
            departureTime: '08:00 AM',
            contactNumber: '+63 ************'
        };
    }
}

// Auto-refresh notification for expired trips
function autoRefreshNotification() {
    setInterval(() => {
        // Show a subtle reminder every 5 minutes
        const now = new Date();
        if (now.getMinutes() % 5 === 0 && now.getSeconds() === 0) {
            toastr.info('Trip times are updated in real-time. Expired trips are automatically hidden.', 'Time Update', {
                timeOut: 2000,
                preventDuplicates: true
            });
        }
    }, 1000);
}

// Test on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('Page loaded, JavaScript initialized successfully');
    console.log('Available functions:', {
        viewSeats: typeof viewSeats,
        bookWalkIn: typeof bookWalkIn,
        openWalkInBooking: typeof openWalkInBooking,
        processWalkInBooking: typeof processWalkInBooking,
        testJavaScript: typeof testJavaScript
    });

    // Start the real-time clock
    startClock();

    // Start auto-refresh notifications
    autoRefreshNotification();

    // Show success message that JavaScript is working
    setTimeout(function() {
        toastr.success('Dashboard loaded with real-time clock!');
    }, 1000);
});

// Complete Payment Function
function completePayment(paymentId) {
    Swal.fire({
        title: '💰 Complete Payment',
        html: `
            <div class="text-left">
                <p class="mb-4">Mark this payment as completed?</p>
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                    <div class="flex">
                        <svg class="w-5 h-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-yellow-800">Important</p>
                            <p class="text-sm text-yellow-700">This will mark the payment as completed and add it to revenue reports.</p>
                        </div>
                    </div>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Yes, Mark as Paid',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#10B981',
        cancelButtonColor: '#6B7280',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            // Send AJAX request to complete payment
            fetch('/agent/complete-payment/' + paymentId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: '✅ Payment Completed!',
                        text: 'Payment has been marked as paid and added to revenue.',
                        icon: 'success',
                        confirmButtonColor: '#FCB404'
                    }).then(() => {
                        // Reload the page to update the table
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: '❌ Error',
                        text: data.message || 'Failed to complete payment.',
                        icon: 'error',
                        confirmButtonColor: '#FCB404'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    title: '❌ Error',
                    text: 'An error occurred while completing the payment.',
                    icon: 'error',
                    confirmButtonColor: '#FCB404'
                });
            });
        }
    });
}
