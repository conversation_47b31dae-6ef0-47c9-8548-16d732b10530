/* Custom palette from user image */
:root {
  --main-bg: #E9E3DF;
  --accent-orange: #FF8432;
  --accent-blue: #4B5C82;
  --main-black: #000000;
}

body {
  background-color: var(--main-bg) !important;
}

.btn-primary {
  background-color: var(--accent-blue) !important;
  color: #fff !important;
  border: none;
}
.btn-primary:hover {
  background-color: #3a4666 !important;
}
.btn-accent {
  background-color: var(--accent-orange) !important;
  color: #fff !important;
}
.header-main {
  background-color: var(--main-black) !important;
  color: #fff !important;
}
.seat-available {
  background-color: var(--accent-blue) !important;
  color: #fff !important;
}
.seat-selected {
  background-color: var(--accent-orange) !important;
  color: #fff !important;
}
.seat-booked {
  background-color: #888 !important;
  color: #fff !important;
}
@tailwind base;
@tailwind components;
@tailwind utilities;
