@php
$add_new_bus = json_encode([2, "bus", "seat", "busForm"]);
$add_new_route = json_encode([2, "start_point", "end_point", "routeForm"]);
$add_new_schedule = json_encode([2, "start_time", "end_time", "scheduleForm"]);
@endphp

<x-app-layout>
    <script src="https://code.jquery.com/jquery-3.7.1.slim.js" integrity="sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: #e2e8f0;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .nav-item:hover {
            background-color: rgba(59, 130, 246, 0.2);
            color: white;
            border-left-color: #3b82f6;
        }
        .nav-item.active {
            background-color: rgba(59, 130, 246, 0.3);
            color: white;
            border-left-color: #60a5fa;
        }
        .nav-item svg {
            margin-right: 12px;
        }
        .section-content {
            display: none;
        }
        .section-content.active {
            display: block;
        }
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            padding: 24px;
            margin-bottom: 24px;
        }
        .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
        }
        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            transition: border-color 0.3s ease;
        }
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .table-container {
            overflow-x: auto;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }
        .admin-table {
            width: 100%;
            border-collapse: collapse;
        }
        .admin-table th {
            background: #f8fafc;
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }
        .admin-table td {
            padding: 16px;
            border-bottom: 1px solid #f3f4f6;
        }
        .admin-table tr:hover {
            background: #f8fafc;
        }
    </style>

    <div class="flex h-screen bg-gray-50">
        <!-- Sidebar -->
        <div class="bg-gradient-to-b from-blue-800 to-blue-900 text-white w-64 flex-shrink-0 shadow-xl">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-600 rounded-lg">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h2 class="text-xl font-bold">Admin Panel</h2>
                        <p class="text-blue-200 text-sm">Bus Management</p>
                    </div>
                </div>
            </div>
            <nav class="mt-2">
                <a href="#dashboard" class="nav-item active" data-section="dashboard">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    Dashboard
                </a>
                <a href="#buses" class="nav-item" data-section="buses">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"></path>
                    </svg>
                    Buses
                </a>
                <a href="#routes" class="nav-item" data-section="routes">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                    </svg>
                    Routes
                </a>
                <a href="#schedules" class="nav-item" data-section="schedules">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Schedules
                </a>
                <a href="#trips" class="nav-item" data-section="trips">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5zM5 14a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1v-3a2 2 0 00-2-2H5z"></path>
                    </svg>
                    Trips
                </a>
                <a href="#fares" class="nav-item" data-section="fares">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    Fare Management
                </a>
                <a href="{{ route('reports.dashboard') }}" class="nav-item">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Reports
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Bar -->
            <header class="bg-white shadow">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
                    <h1 class="text-xl font-bold">Welcome, {{Auth::user()->name}}</h1>
                </div>
            </header>

            <!-- Content Area -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
                <!-- Buses Section -->
                <div class="mb-6 bg-white p-6 rounded-lg shadow">
                    <h2 class="text-xl font-bold mb-4">Buses</h2>
                    <table class="w-full border-collapse border border-gray-300" id="bus_table">
                        <thead class="bg-gray-200">
                            <tr>
                                <th class="border border-gray-300 px-6 py-3 text-lg">ID</th>
                                <th class="border border-gray-300 px-6 py-3 text-lg">Bus Name</th>
                                <th class="border border-gray-300 px-6 py-3 text-lg">Seats</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($data['buses'] as $bus)
                                <tr class="bg-white">
                                    <td class="border border-gray-300 px-6 py-3 text-lg">{{ $bus->id }}</td>
                                    <td class="border border-gray-300 px-6 py-3 text-lg">{{ $bus->name }}</td>
                                    <td class="border border-gray-300 px-6 py-3 text-lg">{{ $bus->seat }}</td>
                                </tr>
                            @empty
                                <tr><td colspan="3" class="text-center py-4 text-lg">No buses found.</td></tr>
                            @endforelse
                        </tbody>
                    </table>
                    <button id="show_bus" class="bg-blue-500 text-white px-6 py-3 rounded mt-4">Add Bus</button>
                </div>

                <!-- Routes Section -->
                <div class="mb-6 bg-white p-6 rounded-lg shadow">
                    <h2 class="text-xl font-bold mb-4">Routes</h2>
                    <table class="w-full border-collapse border border-gray-300" id="route_table">
                        <thead class="bg-gray-200">
                            <tr>
                                <th class="border border-gray-300 px-6 py-3 text-lg">ID</th>
                                <th class="border border-gray-300 px-6 py-3 text-lg">Start Point</th>
                                <th class="border border-gray-300 px-6 py-3 text-lg">End Point</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($data['routes'] as $route)
                                <tr class="bg-white">
                                    <td class="border border-gray-300 px-6 py-3 text-lg">{{ $route->id }}</td>
                                    <td class="border border-gray-300 px-6 py-3 text-lg">{{ $route->start_point }}</td>
                                    <td class="border border-gray-300 px-6 py-3 text-lg">{{ $route->end_point }}</td>
                                </tr>
                            @empty
                                <tr><td colspan="3" class="text-center py-4 text-lg">No routes found.</td></tr>
                            @endforelse
                        </tbody>
                    </table>
                    <button id="show_route" class="bg-green-500 text-white px-6 py-3 rounded mt-4">Add Route</button>
                </div>

                <!-- Schedules Section -->
                <div class="mb-6 bg-white p-6 rounded-lg shadow">
                    <h2 class="text-xl font-bold mb-4">Schedules</h2>
                    <table class="w-full border-collapse border border-gray-300" id="schedule_table">
                        <thead class="bg-gray-200">
                            <tr>
                                <th class="border border-gray-300 px-6 py-3 text-lg">ID</th>
                                <th class="border border-gray-300 px-6 py-3 text-lg">Start Time</th>
                                <th class="border border-gray-300 px-6 py-3 text-lg">End Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($data['schedules'] as $schedule)
                                <tr class="bg-white">
                                    <td class="border border-gray-300 px-6 py-3 text-lg">{{ $schedule->id }}</td>
                                    <td class="border border-gray-300 px-6 py-3 text-lg">{{ $schedule->start_time }}</td>
                                    <td class="border border-gray-300 px-6 py-3 text-lg">{{ $schedule->end_time }}</td>
                                </tr>
                            @empty
                                <tr><td colspan="3" class="text-center py-4 text-lg">No schedules found.</td></tr>
                            @endforelse
                        </tbody>
                    </table>
                    <button id="show_schedule" class="bg-yellow-500 text-white px-6 py-3 rounded mt-4">Add Schedule</button>
                </div>

                <!-- Trips Section -->
                <div class="mb-6 bg-white p-6 rounded-lg shadow">
                    @php
                        $trips = $data['trips'];
                    @endphp
                    <h2 class="text-xl font-bold mb-4">Trips</h2>
                    <table class="w-full border-collapse border border-gray-300" id="trip_table">
                        <thead class="bg-gray-200">
                            <tr>
                                <th class="border border-gray-300 px-6 py-3 text-lg">ID</th>
                                <th class="border border-gray-300 px-6 py-3 text-lg">Bus Name</th>
                                <th class="border border-gray-300 px-6 py-3 text-lg">Route Distance</th>
                                <th class="border border-gray-300 px-6 py-3 text-lg">Schedule</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($trips as $trip)
                                <tr class="bg-white">
                                    <td class="border border-gray-300 px-6 py-3 text-lg">{{ $trip->id }}</td>
                                    <td class="border border-gray-300 px-6 py-3 text-lg">{{ $trip->bus->name }}</td>
                                    <td class="border border-gray-300 px-6 py-3 text-lg">{{ $trip->route->start_point . " - " . $trip->route->end_point }}</td>
                                    <td class="border border-gray-300 px-6 py-3 text-lg">{{ $trip->schedule->start_time . " - " . $trip->schedule->end_time }}</td>
                                </tr>
                            @empty
                                <tr><td colspan="4" class="text-center py-4 text-lg">No trips found.</td></tr>
                            @endforelse
                        </tbody>
                    </table>
                    <button id="show_add_trip" class="bg-red-500 text-white px-6 py-3 rounded mt-4">Add Trip</button>
                </div>
            </main>
        </div>
    </div>

    <!-- Modals -->
    <div id="busModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center">
        <div class="bg-white p-6 rounded shadow-lg w-1/3">
            <h2 class="text-xl font-bold mb-4">Add Bus</h2>
            <x-buscomponents.BusForm :data="$add_new_bus" />
            <button id="close_bus" class="mt-4 bg-red-500 text-white px-4 py-2 rounded">Close</button>
        </div>
    </div>

    <div id="routeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center">
        <div class="bg-white p-6 rounded shadow-lg w-1/3">
            <h2 class="text-xl font-bold mb-4">Add Route</h2>
            <x-buscomponents.BusForm :data="$add_new_route" />
            <button id="close_route" class="mt-4 bg-red-500 text-white px-4 py-2 rounded">Close</button>
        </div>
    </div>

    <div id="scheduleModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center">
        <div class="bg-white p-6 rounded shadow-lg w-1/3">
            <h2 class="text-xl font-bold mb-4">Add Schedule</h2>
            <x-buscomponents.BusForm :data="$add_new_schedule" />
            <button id="close_schedule" class="mt-4 bg-red-500 text-white px-4 py-2 rounded">Close</button>
        </div>
    </div>

    <div id="addTripModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center">
        <div class="bg-white p-6 rounded shadow-lg w-1/3">
            <h2 class="text-xl font-bold mb-4">Add Trip</h2>
            <x-buscomponents.Add_trip :data="$data" formid="add_trip"/>
            <button id="close_add_trip" class="mt-4 bg-red-500 text-white px-4 py-2 rounded">Close</button>
        </div>
    </div>



</x-app-layout>


<script>
$(document).ready(function() {
   
    function showAlert(icon, title, message) {
   Swal.fire({
       icon: icon,
       title: title,
       text: message,
       showConfirmButton: true
   });
   }

    $('#show_bus').click(function() { $('#busModal').removeClass('hidden'); });
    $('#show_route').click(function() { $('#routeModal').removeClass('hidden'); });
    $('#show_schedule').click(function() { $('#scheduleModal').removeClass('hidden'); });
    $('#show_add_trip').click(function() { $('#addTripModal').removeClass('hidden'); });
   
    $('#close_bus').click(function() { $('#busModal').addClass('hidden'); });
    $('#close_route').click(function() { $('#routeModal').addClass('hidden'); });
    $('#close_schedule').click(function() { $('#scheduleModal').addClass('hidden'); });
    $('#close_add_trip').click(function() { $('#addTripModal').addClass('hidden'); });
   
    $(document).on('submit', '#busForm', function(e) {
        e.preventDefault();

       
        let name = $('#bus').val();
        let seat = $('#seat').val();
     

        let formData = new FormData();
        formData.append('name', name);
        formData.append('seat', seat);

        console.log("Submitting Bus:", { name, seat });

        $.ajax({
            url: "{{ route('Add_bus') }}",
            method: "POST",
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: formData,
            contentType: false, 
            processData: false, 
            success: function(d) {
                
                let data=d.data
                showAlert('success', 'Success', d.message);
                $('#bus_table').prepend('<tr><td class="border border-gray-300 px-6 py-3 text-lg">' + data.id + '</td><td class="border border-gray-300 px-6 py-3 text-lg">' + data.name + '</td><td class="border border-gray-300 px-6 py-3 text-lg">' + data.seat + '</td></tr>');
                $('#busModal').addClass('hidden');

            },
            error: function(xhr) {
                showAlert('error', 'Error', 'An error occurred while processing your request.');

            }
        });
    });

   
    $(document).on('submit', '#routeForm ', function(e) {
        e.preventDefault(); 

       
        let start_point = $('#start_point').val();
        let end_point = $('#end_point').val();

        let formData = new FormData();
        formData.append('start_point', start_point);
        formData.append('end_point', end_point);

        console.log("Submitting Route:", { start_point, end_point });

        $.ajax({
            url: "{{ route('add_route') }}",
            method: "POST",
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: formData,
            contentType: false, 
            processData: false, 
            success: function(d) {
             
                let data=d.data
                showAlert('success', 'Success', d.message);
                $('#route_table').prepend('<tr><td class="border border-gray-300 px-6 py-3 text-lg">' + data.id + '</td><td class="border border-gray-300 px-6 py-3 text-lg">' + data.start_point + '</td><td class="border border-gray-300 px-6 py-3 text-lg">' + data.end_point + '</td></tr>');
                $('#routeModal').addClass('hidden');
            },
            error: function(xhr) {
                showAlert('error', 'Error', 'An error occurred while processing your request.');

            }
        });
    });

    $(document).on('submit', '#scheduleForm', function(e) {
        e.preventDefault();

        let start_time = $('#start_time').val();    
let end_time = $('#end_time').val();

let formData = new FormData();

formData.append('start_time', start_time);
formData.append('end_time', end_time);

console.log("Submitting Schedule:", { start_time, end_time }); 
        $.ajax({
            url: "{{ route('add_schedule') }}",
            method: "POST",
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: formData,
            contentType: false, 
            processData: false, 
            success: function(d) {
               
                let data=d.data
                showAlert('success', 'Success', d.message);
                $('#schedule_table').prepend('<tr><td class="border border-gray-300 px-6 py-3 text-lg">' + data.id + '</td><td class="border border-gray-300 px-6 py-3 text-lg">' + data.start_time + '</td><td class="border border-gray-300 px-6 py-3 text-lg">' + data.end_time + '</td></tr>');
                $('#scheduleModal').addClass('hidden');
            },
            error: function(xhr) {
                showAlert('error', 'Error', 'An error occurred while processing your request.');

            }
        })
    });
  $(document).on('submit', '#add_trip', function(e) {
    e.preventDefault();

    let bus_id = $('#buses').val();
    let route_id = $('#routes').val();
    let schedule_id = $('#schedules').val();

    let formData = new FormData();

    formData.append('bus_id', bus_id);
    formData.append('route_id', route_id);
    formData.append('schedule_id', schedule_id);

    console.log("Submitting Trip:", { bus_id, route_id, schedule_id });

    $.ajax({
        url: "{{ route('add_trip') }}",
        method: "POST",
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: formData,
        contentType: false, 
        processData: false, 
        success: function(d) {
            let data=d.data
            showAlert('success', 'Success', data.message);

            
            $('#addTripModal').addClass('hidden');
        },
        error: function(xhr) {
            
            showAlert('error', 'Error', 'An error occurred while processing your request.');
        }
  })
  });
  
});

</script>

