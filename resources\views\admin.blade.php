@php
$pageTitle = 'Admin Dashboard';
$add_new_bus = json_encode([3, "name", "seat", "busForm"]);
$add_new_route = json_encode([3, "start_point", "end_point", "routeForm"]);
$add_new_schedule = json_encode([2, "start_time", "end_time", "scheduleForm"]);
@endphp

<x-app-layout>
    <script src="https://code.jquery.com/jquery-3.7.1.slim.js" integrity="sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        /* Enhanced Navigation Styles */
        .nav-item {
            display: flex;
            align-items: center;
            padding: 14px 20px;
            color: #f1f5f9;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-left: 4px solid transparent;
            margin: 2px 8px;
            border-radius: 8px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }
        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }
        .nav-item:hover::before {
            left: 100%;
        }
        .nav-item:hover {
            background-color: rgba(255, 255, 255, 0.15);
            color: white;
            border-left-color: #60a5fa;
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .nav-item.active {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border-left-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        .nav-item svg {
            margin-right: 12px;
            transition: transform 0.3s ease;
        }
        .nav-item:hover svg {
            transform: scale(1.1);
        }

        /* Section Content */
        .section-content {
            display: none;
            animation: fadeIn 0.5s ease-in-out;
        }
        .section-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Enhanced Card Styles */
        .card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #FCB404, #E6A200);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        .card:hover::before {
            transform: scaleX(1);
        }
        .card:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            transform: translateY(-4px);
        }

        /* Stats Card Enhancements */
        .stats-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            padding: 28px;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }
        .stats-card::after {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(252, 180, 4, 0.1) 0%, transparent 70%);
            transition: all 0.3s ease;
            opacity: 0;
        }
        .stats-card:hover::after {
            opacity: 1;
            top: -25%;
            right: -25%;
        }
        .stats-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
        }
        /* Enhanced Button Styles */
        .btn-primary {
            background: linear-gradient(135deg, #FCB404 0%, #E6A200 100%);
            color: white;
            padding: 14px 28px;
            border-radius: 12px;
            border: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(252, 180, 4, 0.3);
        }
        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        .btn-primary:hover::before {
            left: 100%;
        }
        .btn-primary:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 25px rgba(252, 180, 4, 0.4);
        }
        .btn-primary:active {
            transform: translateY(0) scale(0.98);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 14px 28px;
            border-radius: 12px;
            border: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
        }
        .btn-success:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            border: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
        }
        .btn-warning:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            border: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
        }
        .btn-danger:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
        }
        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            transition: border-color 0.3s ease;
        }
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        /* Enhanced Table Styles */
        .table-container {
            overflow-x: auto;
            border-radius: 16px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            background: white;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .admin-table {
            width: 100%;
            border-collapse: collapse;
        }
        .admin-table th {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            padding: 18px 20px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            font-size: 14px;
            position: relative;
        }
        .admin-table th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #FCB404, #E6A200);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        .admin-table th:hover::after {
            transform: scaleX(1);
        }
        .admin-table td {
            padding: 18px 20px;
            border-bottom: 1px solid #f3f4f6;
            transition: all 0.2s ease;
            font-size: 14px;
        }
        .admin-table tr:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            transform: scale(1.001);
        }
        .admin-table tr:hover td {
            color: #1f2937;
        }
        .admin-table tr:last-child td {
            border-bottom: none;
        }
        /* Enhanced Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(8px);
            animation: modalFadeIn 0.3s ease-out;
        }
        @keyframes modalFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .modal-content {
            background-color: white;
            margin: 3% auto;
            padding: 32px;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            animation: modalSlideIn 0.3s ease-out;
            border: 1px solid rgba(0, 0, 0, 0.1);
            position: relative;
        }
        @keyframes modalSlideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        .close {
            color: #9ca3af;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            position: absolute;
            top: 16px;
            right: 16px;
        }
        .close:hover {
            color: #ef4444;
            background-color: rgba(239, 68, 68, 0.1);
        }

        /* Quick Action Cards */
        .quick-action-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        .quick-action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #FCB404, #E6A200);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        .quick-action-card:hover::before {
            transform: scaleX(1);
        }
        .quick-action-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-item {
                padding: 12px 16px;
                margin: 1px 4px;
            }
            .card, .stats-card {
                padding: 20px;
                margin-bottom: 16px;
            }
            .modal-content {
                margin: 10% auto;
                padding: 24px;
                width: 95%;
            }
            .admin-table th, .admin-table td {
                padding: 12px 16px;
                font-size: 13px;
            }
        }
    </style>

    <div class="flex h-screen bg-gray-50">
        <!-- Sidebar -->
        <div class="text-white w-64 flex-shrink-0 shadow-xl" style="background: linear-gradient(135deg, #FCB404 0%, #E6A200 100%);">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-2 rounded-lg" style="background-color: rgba(255, 255, 255, 0.2);">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h2 class="text-xl font-bold">Admin Panel</h2>
                        <p class="text-yellow-100 text-sm">Bus Management System</p>
                    </div>
                </div>
            </div>
            <nav class="mt-2">
                <a href="#dashboard" class="nav-item active" data-section="dashboard">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    Dashboard
                </a>
                <a href="#buses" class="nav-item" data-section="buses">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"></path>
                    </svg>
                    Buses
                </a>
                <a href="#routes" class="nav-item" data-section="routes">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                    </svg>
                    Routes
                </a>
                <a href="#schedules" class="nav-item" data-section="schedules">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Schedules
                </a>
                <a href="#trips" class="nav-item" data-section="trips">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5zM5 14a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1v-3a2 2 0 00-2-2H5z"></path>
                    </svg>
                    Trips
                </a>
                <a href="#fares" class="nav-item" data-section="fares">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    Fare Management
                </a>
                <a href="#users" class="nav-item" data-section="users">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    User Management
                </a>
                <a href="#reports" class="nav-item" data-section="reports">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Reports
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Bar -->
            <header class="bg-white shadow-sm border-b">
                <div class="px-6 py-4 flex justify-between items-center">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800" id="page-title">Dashboard</h1>
                        <p class="text-gray-600 text-sm">Welcome back, {{ Auth::user()->name }}</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <p class="text-sm text-gray-600">{{ now()->format('F d, Y') }}</p>
                            <p class="text-xs text-gray-500">{{ now()->format('l, g:i A') }}</p>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content Area -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6">
                <!-- Dashboard Section -->
                <div id="dashboard" class="section-content active">
                    <!-- Welcome Header -->
                    <div class="mb-8">
                        <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h2 class="text-3xl font-bold mb-2">Welcome back, {{ Auth::user()->name }}! 👋</h2>
                                    <p class="text-blue-100 text-lg">Here's what's happening with your bus system today.</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-blue-100 text-sm">{{ now()->format('l') }}</p>
                                    <p class="text-white text-xl font-semibold">{{ now()->format('M d, Y') }}</p>
                                    <p class="text-blue-200 text-sm">{{ now()->format('g:i A') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Total Users Card -->
                        <div class="stats-card group">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="flex items-center mb-2">
                                        <div class="p-3 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg">
                                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <p class="text-sm font-medium text-gray-600 mb-1">Total Users</p>
                                    <p class="text-3xl font-bold text-gray-900 mb-1">{{ $data['users']->count() }}</p>
                                    <p class="text-xs text-green-600 font-medium">↗ +12% from last month</p>
                                </div>
                                <div class="text-blue-500 opacity-20 group-hover:opacity-30 transition-opacity">
                                    <svg class="w-16 h-16" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Total Buses Card -->
                        <div class="stats-card group">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="flex items-center mb-2">
                                        <div class="p-3 rounded-full bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg">
                                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <p class="text-sm font-medium text-gray-600 mb-1">Total Buses</p>
                                    <p class="text-3xl font-bold text-gray-900 mb-1">{{ $data['buses']->count() }}</p>
                                    <p class="text-xs text-green-600 font-medium">{{ $data['buses']->count() }} Active</p>
                                </div>
                                <div class="text-green-500 opacity-20 group-hover:opacity-30 transition-opacity">
                                    <svg class="w-16 h-16" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"/>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Total Routes Card -->
                        <div class="stats-card group">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="flex items-center mb-2">
                                        <div class="p-3 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white shadow-lg">
                                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <p class="text-sm font-medium text-gray-600 mb-1">Total Routes</p>
                                    <p class="text-3xl font-bold text-gray-900 mb-1">{{ $data['routes']->count() }}</p>
                                    <p class="text-xs text-blue-600 font-medium">{{ $data['routes']->count() }} Active Routes</p>
                                </div>
                                <div class="text-yellow-500 opacity-20 group-hover:opacity-30 transition-opacity">
                                    <svg class="w-16 h-16" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"/>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Total Trips Card -->
                        <div class="stats-card group">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="flex items-center mb-2">
                                        <div class="p-3 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg">
                                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5zM5 14a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1v-3a2 2 0 00-2-2H5z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <p class="text-sm font-medium text-gray-600 mb-1">Total Trips</p>
                                    <p class="text-3xl font-bold text-gray-900 mb-1">{{ $data['trips']->count() }}</p>
                                    <p class="text-xs text-purple-600 font-medium">{{ $data['available_trips']->count() }} Available</p>
                                </div>
                                <div class="text-purple-500 opacity-20 group-hover:opacity-30 transition-opacity">
                                    <svg class="w-16 h-16" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5zM5 14a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1v-3a2 2 0 00-2-2H5z"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Quick Actions -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">Quick Actions</h3>
                                <p class="text-gray-600 text-sm">Manage your bus system efficiently</p>
                            </div>
                            <div class="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div class="quick-action-card" onclick="showSection('buses')">
                                <div class="p-3 bg-gradient-to-r from-green-500 to-green-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </div>
                                <h4 class="font-semibold text-gray-800 mb-1">Add New Bus</h4>
                                <p class="text-xs text-gray-600">Register a new bus to your fleet</p>
                            </div>
                            <div class="quick-action-card" onclick="showSection('routes')">
                                <div class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                                    </svg>
                                </div>
                                <h4 class="font-semibold text-gray-800 mb-1">Add New Route</h4>
                                <p class="text-xs text-gray-600">Create new bus routes</p>
                            </div>
                            <div class="quick-action-card" onclick="showSection('trips')">
                                <div class="p-3 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5zM5 14a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1v-3a2 2 0 00-2-2H5z"></path>
                                    </svg>
                                </div>
                                <h4 class="font-semibold text-gray-800 mb-1">Create New Trip</h4>
                                <p class="text-xs text-gray-600">Schedule new bus trips</p>
                            </div>
                            <div class="quick-action-card" onclick="showSection('users')">
                                <div class="p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                </div>
                                <h4 class="font-semibold text-gray-800 mb-1">Manage Users</h4>
                                <p class="text-xs text-gray-600">Add and manage system users</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Buses Section -->
                <div id="buses" class="section-content">
                    <div class="card">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">Bus Management</h3>
                            <button class="btn-primary" onclick="openModal('busModal')">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add New Bus
                            </button>
                        </div>

                        <div class="table-container">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Bus Name</th>
                                        <th>Bus Code</th>
                                        <th>Seats</th>
                                        <th>Driver</th>
                                        <th>Conductor</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="busTableBody">
                                    @forelse ($data['buses'] as $bus)
                                        <tr>
                                            <td>{{ $bus->id }}</td>
                                            <td>{{ $bus->name }}</td>
                                            <td><span class="badge badge-info">{{ $bus->bus_code ?? 'N/A' }}</span></td>
                                            <td>{{ $bus->seat }}</td>
                                            <td>{{ $bus->driver_name ?? 'N/A' }}</td>
                                            <td>{{ $bus->conductor_name ?? 'N/A' }}</td>
                                            <td>
                                                <button class="btn-warning mr-2" onclick="editBus({{ $bus->id }}, '{{ $bus->name }}', '{{ $bus->bus_code ?? '' }}', {{ $bus->seat }}, '{{ $bus->driver_name ?? '' }}', '{{ $bus->conductor_name ?? '' }}')">Edit</button>
                                                <button class="btn-danger" onclick="deleteBus({{ $bus->id }})">Delete</button>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr><td colspan="7" class="text-center py-4">No buses found.</td></tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Routes Section -->
                <div id="routes" class="section-content">
                    <div class="card">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">Route Management</h3>
                            <button class="btn-primary" onclick="openModal('routeModal')">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add New Route
                            </button>
                        </div>

                        <div class="table-container">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Start Point</th>
                                        <th>End Point</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="routeTableBody">
                                    @forelse ($data['routes'] as $route)
                                        <tr>
                                            <td>{{ $route->id }}</td>
                                            <td>{{ $route->start_point }}</td>
                                            <td>{{ $route->end_point }}</td>
                                            <td>
                                                <button class="btn-warning mr-2" onclick="editRoute({{ $route->id }}, '{{ $route->start_point }}', '{{ $route->end_point }}')">Edit</button>
                                                <button class="btn-danger" onclick="deleteRoute({{ $route->id }})">Delete</button>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr><td colspan="4" class="text-center py-4">No routes found.</td></tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Schedules Section -->
                <div id="schedules" class="section-content">
                    <div class="card">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">Schedule Management</h3>
                            <button class="btn-primary" onclick="openModal('scheduleModal')">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add New Schedule
                            </button>
                        </div>

                        <div class="table-container">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Trip Date</th>
                                        <th>Start Time</th>
                                        <th>End Time</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="scheduleTableBody">
                                    @forelse ($data['schedules'] as $schedule)
                                        <tr>
                                            <td>{{ $schedule->id }}</td>
                                            <td>{{ $schedule->formatted_trip_date ?? 'N/A' }}</td>
                                            <td>{{ $schedule->formatted_start_time }}</td>
                                            <td>{{ $schedule->formatted_end_time }}</td>
                                            <td>
                                                @if($schedule->trip_date && \Carbon\Carbon::parse($schedule->trip_date)->isPast())
                                                    <span class="badge badge-danger">Expired</span>
                                                @else
                                                    <span class="badge badge-success">Available</span>
                                                @endif
                                            </td>
                                            <td>
                                                <button class="btn-warning mr-2" onclick="editSchedule({{ $schedule->id }}, '{{ $schedule->trip_date }}', '{{ $schedule->start_time }}', '{{ $schedule->end_time }}')">Edit</button>
                                                <button class="btn-danger" onclick="deleteSchedule({{ $schedule->id }})">Delete</button>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr><td colspan="6" class="text-center py-4">No schedules found.</td></tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Trips Section -->
                <div id="trips" class="section-content">
                    <div class="card">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">Trip Management</h3>
                            <button class="btn-primary" onclick="openModal('tripModal')">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create New Trip
                            </button>
                        </div>

                        <div class="table-container">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Bus</th>
                                        <th>Route</th>
                                        <th>Schedule</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="tripTableBody">
                                    @forelse ($data['trips'] as $trip)
                                        <tr>
                                            <td>{{ $trip->id }}</td>
                                            <td>{{ $trip->bus->name }}</td>
                                            <td>{{ $trip->route->start_point }} → {{ $trip->route->end_point }}</td>
                                            <td>{{ $trip->schedule->start_time }} - {{ $trip->schedule->end_time }}</td>
                                            <td>
                                                <button class="btn-warning mr-2" onclick="editTrip({{ $trip->id }})">Edit</button>
                                                <button class="btn-danger" onclick="deleteTrip({{ $trip->id }})">Delete</button>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr><td colspan="5" class="text-center py-4">No trips found.</td></tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Fare Management Section -->
                <div id="fares" class="section-content">
                    <div class="card">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">Fare Management</h3>
                            <button class="btn-primary" onclick="openModal('fareModal')">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add New Fare
                            </button>
                        </div>

                        <div class="table-container">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Route</th>
                                        <th>Base Fare</th>
                                        <th>Per KM Rate</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="fareTableBody">
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Enhanced User Management Section -->
                <div id="users" class="section-content">
                    <!-- User Management Header -->
                    <div class="mb-8">
                        <div class="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl p-8 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h2 class="text-3xl font-bold mb-2">User Management 👥</h2>
                                    <p class="text-indigo-100 text-lg">Manage users, agents, and their permissions</p>
                                </div>
                                <div class="flex space-x-3">
                                    <button class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 flex items-center" onclick="openModal('userModal')">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        Add User
                                    </button>
                                    <button class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 flex items-center" onclick="openModal('agentModal')">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        Add Agent
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Statistics Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="stats-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 mb-1">Total Users</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ $data['users']->count() }}</p>
                                    <p class="text-xs text-blue-600 font-medium">All system users</p>
                                </div>
                                <div class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="stats-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 mb-1">Agents</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ $data['users']->filter(function($user) { return $user->roles->first() && $user->roles->first()->type === 'agent'; })->count() }}</p>
                                    <p class="text-xs text-green-600 font-medium">Active agents</p>
                                </div>
                                <div class="p-3 bg-gradient-to-r from-green-500 to-green-600 rounded-full">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="stats-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 mb-1">Admins</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ $data['users']->filter(function($user) { return $user->roles->first() && $user->roles->first()->type === 'admin'; })->count() }}</p>
                                    <p class="text-xs text-red-600 font-medium">System admins</p>
                                </div>
                                <div class="p-3 bg-gradient-to-r from-red-500 to-red-600 rounded-full">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="stats-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600 mb-1">Customers</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ $data['users']->filter(function($user) { return !$user->roles->first() || $user->roles->first()->type === 'customer'; })->count() }}</p>
                                    <p class="text-xs text-purple-600 font-medium">Regular customers</p>
                                </div>
                                <div class="p-3 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filter Section -->
                    <div class="card mb-6">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                            <div class="flex-1">
                                <div class="relative">
                                    <input type="text" id="userSearch" placeholder="Search users by name, email, or role..."
                                           class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="flex gap-3">
                                <select id="roleFilter" class="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">All Roles</option>
                                    <option value="admin">Admin</option>
                                    <option value="agent">Agent</option>
                                    <option value="customer">Customer</option>
                                </select>
                                <button class="btn-primary" onclick="exportUsers()">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Export
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Users Table -->
                    <div class="table-container"

                        <div class="table-header">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-800">Users List</h3>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-600">{{ $data['users']->count() }} users total</span>
                                    <div class="flex space-x-1">
                                        <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors" title="Refresh">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                            </svg>
                                        </button>
                                        <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors" title="Settings">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <table class="admin-table" id="usersTable">
                            <thead>
                                <tr>
                                    <th class="cursor-pointer hover:bg-gray-100 transition-colors" onclick="sortTable(0)">
                                        <div class="flex items-center">
                                            ID
                                            <svg class="w-4 h-4 ml-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th class="cursor-pointer hover:bg-gray-100 transition-colors" onclick="sortTable(1)">
                                        <div class="flex items-center">
                                            User
                                            <svg class="w-4 h-4 ml-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th class="cursor-pointer hover:bg-gray-100 transition-colors" onclick="sortTable(2)">
                                        <div class="flex items-center">
                                            Role
                                            <svg class="w-4 h-4 ml-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th>Status</th>
                                    <th class="cursor-pointer hover:bg-gray-100 transition-colors" onclick="sortTable(4)">
                                        <div class="flex items-center">
                                            Joined
                                            <svg class="w-4 h-4 ml-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="users-table-body">
                                @forelse ($data['users'] as $user)
                                    <tr class="user-row" data-user-id="{{ $user->id }}">
                                        <td>
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                                                    {{ substr($user->name, 0, 1) }}
                                                </div>
                                                <span class="ml-3 font-medium text-gray-900">#{{ $user->id }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-12 w-12">
                                                    <div class="h-12 w-12 rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 flex items-center justify-center">
                                                        <span class="text-lg font-semibold text-white">{{ substr($user->name, 0, 1) }}</span>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">{{ $user->name }}</div>
                                                    <div class="text-sm text-gray-500">{{ $user->email }}</div>
                                                    @if($user->phone)
                                                        <div class="text-xs text-gray-400">{{ $user->phone }}</div>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if($user->roles->first())
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                    @if($user->roles->first()->type === 'admin') bg-red-100 text-red-800 border border-red-200
                                                    @elseif($user->roles->first()->type === 'agent') bg-blue-100 text-blue-800 border border-blue-200
                                                    @else bg-green-100 text-green-800 border border-green-200 @endif">
                                                    @if($user->roles->first()->type === 'admin')
                                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M9.504 1.132a1 1 0 01.992 0l1.75 1a1 1 0 11-.992 1.736L10 3.152l-1.254.716a1 1 0 11-.992-1.736l1.75-1zM5.618 4.504a1 1 0 01-.372 1.364L5.016 6l.23.132a1 1 0 11-.992 1.736L4 7.723V8a1 1 0 01-2 0V6a.996.996 0 01.52-.878l1.734-.99a1 1 0 011.364.372zm8.764 0a1 1 0 011.364-.372l1.733.99A1.002 1.002 0 0118 6v2a1 1 0 11-2 0v-.277l-.254.145a1 1 0 11-.992-1.736l.23-.132-.23-.132a1 1 0 01-.372-1.364zm-7 4a1 1 0 011.364-.372L10 8.848l1.254-.716a1 1 0 11.992 1.736L11 10.58V12a1 1 0 11-2 0v-1.42l-1.246-.712a1 1 0 01-.372-1.364zM3 11a1 1 0 011 1v1.42l1.246.712a1 1 0 11-.992 1.736L3 15.152l-1.254.716a1 1 0 11-.992-1.736L2 13.42V12a1 1 0 011-1zm14 0a1 1 0 011 1v1.42l1.246.712a1 1 0 11-.992 1.736L17 15.152l-1.254.716a1 1 0 11-.992-1.736L16 13.42V12a1 1 0 011-1zm-9.618 4.504a1 1 0 01.372-1.364L9 13.848l1.254.716a1 1 0 11-.992 1.736L9 15.58l-.254-.145a1 1 0 01-1.364-.931z" clip-rule="evenodd"></path>
                                                        </svg>
                                                    @elseif($user->roles->first()->type === 'agent')
                                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                                                        </svg>
                                                    @else
                                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                                                        </svg>
                                                    @endif
                                                    {{ ucfirst($user->roles->first()->type) }}
                                                </span>
                                            @else
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                                                    </svg>
                                                    Customer
                                                </span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="flex items-center">
                                                <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                                                <span class="text-sm text-green-600 font-medium">Active</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-sm text-gray-900">{{ $user->created_at->format('M d, Y') }}</div>
                                            <div class="text-xs text-gray-500">{{ $user->created_at->format('g:i A') }}</div>
                                        </td>
                                        <td>
                                            <div class="flex items-center justify-center space-x-2">
                                                <button class="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors" onclick="viewUser({{ $user->id }})" title="View Details">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                    </svg>
                                                </button>
                                                <button class="p-2 text-yellow-600 hover:bg-yellow-50 rounded-lg transition-colors" onclick="editUser({{ $user->id }})" title="Edit User">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                    </svg>
                                                </button>
                                                @if($user->id !== Auth::id())
                                                <button class="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors" onclick="deleteUser({{ $user->id }})" title="Delete User">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="px-6 py-12 text-center">
                                            <div class="flex flex-col items-center">
                                                <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                                </svg>
                                                <h3 class="text-lg font-medium text-gray-900 mb-1">No users found</h3>
                                                <p class="text-gray-500">Get started by adding your first user.</p>
                                                <button class="mt-4 btn-primary" onclick="openModal('userModal')">
                                                    Add User
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Reports Section -->
                <div id="reports" class="section-content">
                    <div class="card">
                        <h3 class="text-xl font-semibold text-gray-800 mb-6">Reports Dashboard</h3>

                        <!-- Summary Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <div class="flex items-center">
                                    <div class="p-2 bg-blue-100 rounded-lg">
                                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-blue-600">Total Bookings</p>
                                        <p class="text-lg font-bold text-blue-800">{{ $data['tickets']->count() }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="flex items-center">
                                    <div class="p-2 bg-green-100 rounded-lg">
                                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-green-600">Total Revenue</p>
                                        <p class="text-lg font-bold text-green-800">₱{{ number_format($data['tickets']->sum('price'), 2) }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-purple-50 p-4 rounded-lg">
                                <div class="flex items-center">
                                    <div class="p-2 bg-purple-100 rounded-lg">
                                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-purple-600">Active Trips</p>
                                        <p class="text-lg font-bold text-purple-800">{{ $data['trips']->count() }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <div class="flex items-center">
                                    <div class="p-2 bg-yellow-100 rounded-lg">
                                        <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-yellow-600">Total Routes</p>
                                        <p class="text-lg font-bold text-yellow-800">{{ $data['routes']->count() }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Bookings Table -->
                        <div class="mt-6">
                            <h4 class="text-lg font-semibold text-gray-800 mb-4">Recent Bookings</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Booking ID</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">User</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Route</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Seat</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        @forelse($data['tickets']->take(10) as $ticket)
                                            <tr>
                                                <td class="px-4 py-3 text-sm text-gray-900">{{ $ticket->id }}</td>
                                                <td class="px-4 py-3 text-sm text-gray-900">{{ $ticket->user->name ?? 'N/A' }}</td>
                                                <td class="px-4 py-3 text-sm text-gray-900">
                                                    {{ $ticket->trip->route->start_point ?? 'N/A' }} → {{ $ticket->trip->route->end_point ?? 'N/A' }}
                                                </td>
                                                <td class="px-4 py-3 text-sm text-gray-900">{{ $ticket->seat_number ?? 'N/A' }}</td>
                                                <td class="px-4 py-3 text-sm text-gray-900">₱{{ number_format($ticket->price, 2) }}</td>
                                                <td class="px-4 py-3 text-sm text-gray-900">{{ $ticket->created_at->format('M d, Y') }}</td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="6" class="px-4 py-3 text-center text-gray-500">No bookings found</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modals -->
    <!-- Bus Modal -->
    <div id="busModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('busModal')">&times;</span>
            <h2 class="text-xl font-bold mb-4" id="busModalTitle">Add New Bus</h2>
            <form id="busForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Bus Name</label>
                    <input type="text" id="busName" name="name" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Bus Code</label>
                    <input type="text" id="busCode" name="bus_code" class="form-input" placeholder="e.g., BUS001" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Number of Seats</label>
                    <input type="number" id="busSeats" name="seat" class="form-input" min="1" max="100" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Driver Name</label>
                    <input type="text" id="busDriver" name="driver_name" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Conductor Name</label>
                    <input type="text" id="busConductor" name="conductor_name" class="form-input" required>
                </div>
                <div class="flex gap-4">
                    <button type="submit" class="btn-success">Save Bus</button>
                    <button type="button" class="btn-primary" onclick="closeModal('busModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Route Modal -->
    <div id="routeModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('routeModal')">&times;</span>
            <h2 class="text-xl font-bold mb-4" id="routeModalTitle">Add New Route</h2>
            <form id="routeForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Start Point</label>
                    <input type="text" id="routeStartPoint" name="start_point" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">End Point</label>
                    <input type="text" id="routeEndPoint" name="end_point" class="form-input" required>
                </div>
                <div class="flex gap-4">
                    <button type="submit" class="btn-success">Save Route</button>
                    <button type="button" class="btn-primary" onclick="closeModal('routeModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Schedule Modal -->
    <div id="scheduleModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('scheduleModal')">&times;</span>
            <h2 class="text-xl font-bold mb-4" id="scheduleModalTitle">Add New Schedule</h2>
            <form id="scheduleForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Trip Date</label>
                    <input type="date" id="scheduleTripDate" name="trip_date" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Start Time</label>
                    <input type="time" id="scheduleStartTime" name="start_time" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">End Time</label>
                    <input type="time" id="scheduleEndTime" name="end_time" class="form-input" required>
                </div>
                <div class="flex gap-4">
                    <button type="submit" class="btn-success">Save Schedule</button>
                    <button type="button" class="btn-primary" onclick="closeModal('scheduleModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Trip Modal -->
    <div id="tripModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('tripModal')">&times;</span>
            <h2 class="text-xl font-bold mb-4" id="tripModalTitle">Create New Trip</h2>
            <form id="tripForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Bus</label>
                    <select id="tripBus" name="bus_id" class="form-input" required>
                        <option value="">Select Bus</option>
                        @foreach($data['buses'] as $bus)
                            <option value="{{ $bus->id }}">{{ $bus->name }} ({{ $bus->seat }} seats)</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Route</label>
                    <select id="tripRoute" name="route_id" class="form-input" required>
                        <option value="">Select Route</option>
                        @foreach($data['routes'] as $route)
                            <option value="{{ $route->id }}">{{ $route->start_point }} → {{ $route->end_point }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Schedule</label>
                    <select id="tripSchedule" name="schedule_id" class="form-input" required>
                        <option value="">Select Schedule</option>
                        @foreach($data['schedules'] as $schedule)
                            <option value="{{ $schedule->id }}">{{ $schedule->start_time }} - {{ $schedule->end_time }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="flex gap-4">
                    <button type="submit" class="btn-success">Create Trip</button>
                    <button type="button" class="btn-primary" onclick="closeModal('tripModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Fare Modal -->
    <div id="fareModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('fareModal')">&times;</span>
            <h2 class="text-xl font-bold mb-4" id="fareModalTitle">Add New Fare</h2>
            <form id="fareForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Route</label>
                    <select id="fareRoute" name="route_id" class="form-input" required>
                        <option value="">Select Route</option>
                        @foreach($data['routes'] as $route)
                            <option value="{{ $route->id }}">{{ $route->start_point }} → {{ $route->end_point }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Base Fare (₱)</label>
                    <input type="number" id="fareBaseFare" name="base_fare" class="form-input" step="0.01" min="0" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Per KM Rate (₱) - Optional</label>
                    <input type="number" id="farePerKmRate" name="per_km_rate" class="form-input" step="0.01" min="0">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Fare (₱) - Optional</label>
                    <input type="number" id="fareMinFare" name="minimum_fare" class="form-input" step="0.01" min="0">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Maximum Fare (₱) - Optional</label>
                    <input type="number" id="fareMaxFare" name="maximum_fare" class="form-input" step="0.01" min="0">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Description - Optional</label>
                    <textarea id="fareDescription" name="description" class="form-input" rows="3"></textarea>
                </div>
                <div class="flex gap-4">
                    <button type="submit" class="btn-success">Save Fare</button>
                    <button type="button" class="btn-primary" onclick="closeModal('fareModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- User Modal -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('userModal')">&times;</span>
            <h2 class="text-xl font-bold mb-4" id="userModalTitle">Add New User</h2>
            <form id="userForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <input type="text" id="userName" name="name" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                    <input type="email" id="userEmail" name="email" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                    <input type="tel" id="userPhone" name="phone" class="form-input" placeholder="09123456789">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                    <select id="userGender" name="Gender" class="form-input" required>
                        <option value="">Select Gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                    <select id="userRole" name="role" class="form-input" required>
                        <option value="">Select Role</option>
                        <option value="customer">Customer</option>
                        <option value="agent">Agent</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input type="password" id="userPassword" name="password" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Confirm Password</label>
                    <input type="password" id="userPasswordConfirm" name="password_confirmation" class="form-input" required>
                </div>
                <div class="flex gap-4">
                    <button type="submit" class="btn-success">Save User</button>
                    <button type="button" class="btn-primary" onclick="closeModal('userModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Navigation functionality
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.section-content').forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionName).classList.add('active');

            // Add active class to clicked nav item
            document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

            // Update page title
            const titles = {
                'dashboard': 'Dashboard',
                'buses': 'Bus Management',
                'routes': 'Route Management',
                'schedules': 'Schedule Management',
                'trips': 'Trip Management',
                'fares': 'Fare Management'
            };
            document.getElementById('page-title').textContent = titles[sectionName] || 'Admin Panel';

            // Load section-specific data
            if (sectionName === 'fares') {
                loadFares();
            }
        }

        // Navigation click handlers
        document.querySelectorAll('.nav-item[data-section]').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const section = this.getAttribute('data-section');
                showSection(section);
            });
        });

        // Modal functionality
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            // Reset form if exists
            const form = document.querySelector(`#${modalId} form`);
            if (form) form.reset();
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // Fare Management
        function loadFares() {
            fetch('/fares')
                .then(response => response.json())
                .then(data => {
                    const tbody = document.getElementById('fareTableBody');
                    tbody.innerHTML = '';

                    if (data.fares && data.fares.length > 0) {
                        data.fares.forEach(fare => {
                            const row = `
                                <tr>
                                    <td>${fare.id}</td>
                                    <td>${fare.route.start_point} → ${fare.route.end_point}</td>
                                    <td>₱${parseFloat(fare.base_fare).toFixed(2)}</td>
                                    <td>₱${parseFloat(fare.per_km_rate || 0).toFixed(2)}</td>
                                    <td>
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full ${fare.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                            ${fare.is_active ? 'Active' : 'Inactive'}
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn-warning mr-2" onclick="editFare(${fare.id})">Edit</button>
                                        ${!fare.is_active ? `<button class="btn-success mr-2" onclick="activateFare(${fare.id})">Activate</button>` : ''}
                                        <button class="btn-danger" onclick="deleteFare(${fare.id})">Delete</button>
                                    </td>
                                </tr>
                            `;
                            tbody.innerHTML += row;
                        });
                    } else {
                        tbody.innerHTML = '<tr><td colspan="6" class="text-center py-4">No fares found.</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('Error loading fares:', error);
                });
        }

        document.getElementById('fareForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch('/fares', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(Object.fromEntries(formData))
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: data.message,
                        confirmButtonColor: '#10b981'
                    });
                    closeModal('fareModal');
                    loadFares(); // Refresh fare table
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: data.message || 'An error occurred',
                        confirmButtonColor: '#ef4444'
                    });
                }
            })
            .catch(error => {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'An error occurred while saving the fare.',
                    confirmButtonColor: '#ef4444'
                });
            });
        });

        function deleteFare(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/fares/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted!',
                                text: 'Fare has been deleted.',
                                confirmButtonColor: '#10b981'
                            });
                            loadFares();
                        }
                    });
                }
            });
        }

        function editFare(id) {
            // Fetch fare data first
            fetch(`/fares/${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const fare = data.fare;

                        // Show edit modal with current values
                        Swal.fire({
                            title: 'Edit Fare',
                            html: `
                                <div class="text-left">
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Route</label>
                                        <select id="edit-route-id" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                            <option value="">Select Route</option>
                                            ${window.routeOptions.map(route =>
                                                `<option value="${route.id}" ${route.id == fare.route_id ? 'selected' : ''}>
                                                    ${route.start_point} → ${route.end_point}
                                                </option>`
                                            ).join('')}
                                        </select>
                                    </div>
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Base Fare (₱)</label>
                                        <input type="number" id="edit-base-fare" step="0.01" min="0"
                                               value="${fare.base_fare}" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Per KM Rate (₱)</label>
                                        <input type="number" id="edit-per-km-rate" step="0.01" min="0"
                                               value="${fare.per_km_rate || 0}" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Fare (₱)</label>
                                        <input type="number" id="edit-minimum-fare" step="0.01" min="0"
                                               value="${fare.minimum_fare || 0}" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Maximum Fare (₱)</label>
                                        <input type="number" id="edit-maximum-fare" step="0.01" min="0"
                                               value="${fare.maximum_fare || 0}" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                        <textarea id="edit-description" rows="3"
                                                  class="w-full border border-gray-300 rounded-md px-3 py-2">${fare.description || ''}</textarea>
                                    </div>
                                </div>
                            `,
                            showCancelButton: true,
                            confirmButtonText: 'Update Fare',
                            cancelButtonText: 'Cancel',
                            confirmButtonColor: '#3b82f6',
                            preConfirm: () => {
                                const routeId = document.getElementById('edit-route-id').value;
                                const baseFare = document.getElementById('edit-base-fare').value;
                                const perKmRate = document.getElementById('edit-per-km-rate').value;
                                const minimumFare = document.getElementById('edit-minimum-fare').value;
                                const maximumFare = document.getElementById('edit-maximum-fare').value;
                                const description = document.getElementById('edit-description').value;

                                if (!routeId || !baseFare) {
                                    Swal.showValidationMessage('Route and Base Fare are required');
                                    return false;
                                }

                                return {
                                    route_id: routeId,
                                    base_fare: baseFare,
                                    per_km_rate: perKmRate,
                                    minimum_fare: minimumFare,
                                    maximum_fare: maximumFare,
                                    description: description
                                };
                            }
                        }).then((result) => {
                            if (result.isConfirmed) {
                                // Update fare
                                fetch(`/fares/${id}`, {
                                    method: 'PUT',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                    },
                                    body: JSON.stringify(result.value)
                                })
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        Swal.fire({
                                            icon: 'success',
                                            title: 'Updated!',
                                            text: 'Fare has been updated successfully.',
                                            confirmButtonColor: '#10b981'
                                        });
                                        loadFares();
                                    } else {
                                        Swal.fire({
                                            icon: 'error',
                                            title: 'Error!',
                                            text: data.message || 'Failed to update fare.',
                                            confirmButtonColor: '#ef4444'
                                        });
                                    }
                                })
                                .catch(error => {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Error!',
                                        text: 'An error occurred while updating the fare.',
                                        confirmButtonColor: '#ef4444'
                                    });
                                });
                            }
                        });
                    }
                })
                .catch(error => {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Failed to fetch fare data.',
                        confirmButtonColor: '#ef4444'
                    });
                });
        }

        function activateFare(id) {
            fetch(`/fares/${id}/activate`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Activated!',
                        text: 'Fare has been activated.',
                        confirmButtonColor: '#10b981'
                    });
                    loadFares();
                }
            });
        }

        // Route Management Functions
        document.getElementById('routeForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            // Simple form submission - you can implement proper API endpoints
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Route functionality will be implemented with proper API endpoints.',
                confirmButtonColor: '#10b981'
            });
            closeModal('routeModal');
        });

        function editRoute(id, startPoint, endPoint) {
            document.getElementById('routeModalTitle').textContent = 'Edit Route';
            document.getElementById('routeStartPoint').value = startPoint;
            document.getElementById('routeEndPoint').value = endPoint;
            openModal('routeModal');
        }

        function deleteRoute(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "This will delete the route permanently!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Deleted!',
                        text: 'Route deletion functionality will be implemented.',
                        confirmButtonColor: '#10b981'
                    });
                }
            });
        }

        // Schedule Management Functions
        document.getElementById('scheduleForm').addEventListener('submit', function(e) {
            e.preventDefault();

            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Schedule functionality will be implemented with proper API endpoints.',
                confirmButtonColor: '#10b981'
            });
            closeModal('scheduleModal');
        });

        function editSchedule(id, tripDate, startTime, endTime) {
            document.getElementById('scheduleModalTitle').textContent = 'Edit Schedule';
            document.getElementById('scheduleTripDate').value = tripDate;
            document.getElementById('scheduleStartTime').value = startTime;
            document.getElementById('scheduleEndTime').value = endTime;

            // Store the schedule ID for updating
            document.getElementById('scheduleForm').setAttribute('data-schedule-id', id);
            openModal('scheduleModal');
        }

        function deleteSchedule(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "This will delete the schedule permanently!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Deleted!',
                        text: 'Schedule deletion functionality will be implemented.',
                        confirmButtonColor: '#10b981'
                    });
                }
            });
        }

        // Trip Management Functions
        document.getElementById('tripForm').addEventListener('submit', function(e) {
            e.preventDefault();

            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Trip functionality will be implemented with proper API endpoints.',
                confirmButtonColor: '#10b981'
            });
            closeModal('tripModal');
        });

        function editTrip(id) {
            Swal.fire({
                icon: 'info',
                title: 'Edit Trip',
                text: 'Edit functionality will be implemented soon.',
                confirmButtonColor: '#3b82f6'
            });
        }

        function deleteTrip(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "This will delete the trip permanently!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Deleted!',
                        text: 'Trip deletion functionality will be implemented.',
                        confirmButtonColor: '#10b981'
                    });
                }
            });
        }

        // Make route options available globally
        window.routeOptions = @json($data['routes']);

        // Bus Management Functions
        function editBus(id, name, busCode, seats, driver, conductor) {
            document.getElementById('busModalTitle').textContent = 'Edit Bus';
            document.getElementById('busName').value = name;
            document.getElementById('busCode').value = busCode;
            document.getElementById('busSeats').value = seats;
            document.getElementById('busDriver').value = driver;
            document.getElementById('busConductor').value = conductor;

            // Store the bus ID for updating
            document.getElementById('busForm').setAttribute('data-bus-id', id);
            openModal('busModal');
        }

        function deleteBus(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/buses/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted!',
                                text: 'Bus has been deleted.',
                                confirmButtonColor: '#10b981'
                            });
                            location.reload(); // Refresh the page
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: data.message || 'Failed to delete bus.',
                                confirmButtonColor: '#ef4444'
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'An error occurred while deleting the bus.',
                            confirmButtonColor: '#ef4444'
                        });
                    });
                }
            });
        }

        // Route Management Functions
        function deleteRoute(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/routes/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted!',
                                text: 'Route has been deleted.',
                                confirmButtonColor: '#10b981'
                            });
                            location.reload(); // Refresh the page
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: data.message || 'Failed to delete route.',
                                confirmButtonColor: '#ef4444'
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'An error occurred while deleting the route.',
                            confirmButtonColor: '#ef4444'
                        });
                    });
                }
            });
        }

        // Schedule Management Functions
        function deleteSchedule(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/schedules/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted!',
                                text: 'Schedule has been deleted.',
                                confirmButtonColor: '#10b981'
                            });
                            location.reload(); // Refresh the page
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: data.message || 'Failed to delete schedule.',
                                confirmButtonColor: '#ef4444'
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'An error occurred while deleting the schedule.',
                            confirmButtonColor: '#ef4444'
                        });
                    });
                }
            });
        }

        // Trip Management Functions
        function deleteTrip(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/trips/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted!',
                                text: 'Trip has been deleted.',
                                confirmButtonColor: '#10b981'
                            });
                            location.reload(); // Refresh the page
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: data.message || 'Failed to delete trip.',
                                confirmButtonColor: '#ef4444'
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'An error occurred while deleting the trip.',
                            confirmButtonColor: '#ef4444'
                        });
                    });
                }
            });
        }

        // User Management Functions
        function editUser(id) {
            // Fetch user data first
            fetch(`/admin/users/${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const user = data.user;

                        // Populate form with current values
                        document.getElementById('userModalTitle').textContent = 'Edit User';
                        document.getElementById('userName').value = user.name;
                        document.getElementById('userEmail').value = user.email;
                        document.getElementById('userPhone').value = user.phone || '';
                        document.getElementById('userGender').value = user.Gender || '';
                        document.getElementById('userRole').value = user.roles.length > 0 ? user.roles[0].type : 'customer';

                        // Hide password fields for editing
                        document.getElementById('userPassword').parentElement.style.display = 'none';
                        document.getElementById('userPasswordConfirm').parentElement.style.display = 'none';

                        // Store user ID for updating
                        document.getElementById('userForm').setAttribute('data-user-id', id);
                        openModal('userModal');
                    }
                })
                .catch(error => {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Failed to fetch user data.',
                        confirmButtonColor: '#ef4444'
                    });
                });
        }

        function deleteUser(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/admin/users/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted!',
                                text: 'User has been deleted.',
                                confirmButtonColor: '#10b981'
                            });
                            location.reload(); // Refresh the page
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: data.message || 'Failed to delete user.',
                                confirmButtonColor: '#ef4444'
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'An error occurred while deleting the user.',
                            confirmButtonColor: '#ef4444'
                        });
                    });
                }
            });
        }

        // User form submission
        document.getElementById('userForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const userId = this.getAttribute('data-user-id');
            const isEdit = userId !== null;

            const url = isEdit ? `/admin/users/${userId}` : '/admin/users';
            const method = isEdit ? 'PUT' : 'POST';

            // Convert FormData to JSON
            const data = {};
            formData.forEach((value, key) => {
                data[key] = value;
            });

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: isEdit ? 'Updated!' : 'Created!',
                        text: data.message,
                        confirmButtonColor: '#10b981'
                    });
                    closeModal('userModal');
                    location.reload(); // Refresh the page
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: data.message || 'Failed to save user.',
                        confirmButtonColor: '#ef4444'
                    });
                }
            })
            .catch(error => {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'An error occurred while saving the user.',
                    confirmButtonColor: '#ef4444'
                });
            });
        });

        // Enhanced User Management Functions

        // Search functionality
        document.getElementById('userSearch')?.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('#users-table-body .user-row');

            rows.forEach(row => {
                const name = row.querySelector('td:nth-child(2) .text-sm.font-medium')?.textContent.toLowerCase() || '';
                const email = row.querySelector('td:nth-child(2) .text-gray-500')?.textContent.toLowerCase() || '';
                const role = row.querySelector('td:nth-child(3) span')?.textContent.toLowerCase() || '';

                if (name.includes(searchTerm) || email.includes(searchTerm) || role.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // Role filter functionality
        document.getElementById('roleFilter')?.addEventListener('change', function(e) {
            const selectedRole = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('#users-table-body .user-row');

            rows.forEach(row => {
                const role = row.querySelector('td:nth-child(3) span')?.textContent.toLowerCase() || '';

                if (selectedRole === '' || role.includes(selectedRole)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // View user details
        function viewUser(userId) {
            Swal.fire({
                title: 'User Details',
                html: `
                    <div class="text-left">
                        <div class="mb-4">
                            <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-2xl font-bold text-white">U</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div><strong>ID:</strong> #${userId}</div>
                            <div><strong>Status:</strong> <span class="text-green-600">Active</span></div>
                            <div><strong>Last Login:</strong> Recently</div>
                            <div><strong>Permissions:</strong> Based on role</div>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'Edit User',
                cancelButtonText: 'Close',
                confirmButtonColor: '#3b82f6'
            }).then((result) => {
                if (result.isConfirmed) {
                    editUser(userId);
                }
            });
        }

        // Export users functionality
        function exportUsers() {
            Swal.fire({
                title: 'Export Users',
                text: 'Choose export format:',
                showCancelButton: true,
                confirmButtonText: 'Export CSV',
                cancelButtonText: 'Export PDF',
                confirmButtonColor: '#10b981'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Export as CSV
                    Swal.fire('Success!', 'Users exported as CSV', 'success');
                } else if (result.dismiss === Swal.DismissReason.cancel) {
                    // Export as PDF
                    Swal.fire('Success!', 'Users exported as PDF', 'success');
                }
            });
        }

        // Table sorting functionality
        function sortTable(columnIndex) {
            const table = document.getElementById('usersTable');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr:not(.empty-state)'));

            // Toggle sort direction
            const isAscending = table.dataset.sortDirection !== 'asc';
            table.dataset.sortDirection = isAscending ? 'asc' : 'desc';

            rows.sort((a, b) => {
                const aText = a.cells[columnIndex]?.textContent.trim() || '';
                const bText = b.cells[columnIndex]?.textContent.trim() || '';

                if (columnIndex === 0) { // ID column - numeric sort
                    return isAscending ?
                        parseInt(aText.replace('#', '')) - parseInt(bText.replace('#', '')) :
                        parseInt(bText.replace('#', '')) - parseInt(aText.replace('#', ''));
                }

                return isAscending ?
                    aText.localeCompare(bText) :
                    bText.localeCompare(aText);
            });

            // Reorder rows
            rows.forEach(row => tbody.appendChild(row));

            // Update sort indicators
            const headers = table.querySelectorAll('th svg');
            headers.forEach(svg => svg.classList.remove('text-blue-500'));
            const currentHeader = table.querySelectorAll('th')[columnIndex]?.querySelector('svg');
            if (currentHeader) {
                currentHeader.classList.add('text-blue-500');
                currentHeader.style.transform = isAscending ? 'rotate(0deg)' : 'rotate(180deg)';
            }
        }

        // Reset user form when modal closes
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';

            if (modalId === 'userModal') {
                // Reset form
                document.getElementById('userForm').reset();
                document.getElementById('userForm').removeAttribute('data-user-id');
                document.getElementById('userModalTitle').textContent = 'Add New User';

                // Show password fields again
                document.getElementById('userPassword').parentElement.style.display = 'block';
                document.getElementById('userPasswordConfirm').parentElement.style.display = 'block';
            }

            // Reset other forms if exists
            const form = document.querySelector(`#${modalId} form`);
            if (form) form.reset();
        }

        // Initialize enhanced features when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading states to buttons
            const buttons = document.querySelectorAll('.btn-primary, .btn-success, .btn-warning, .btn-danger');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    if (!this.classList.contains('loading')) {
                        const originalText = this.innerHTML;
                        this.classList.add('loading');
                        this.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Loading...';

                        setTimeout(() => {
                            this.classList.remove('loading');
                            this.innerHTML = originalText;
                        }, 1000);
                    }
                });
            });

            // Add hover effects to table rows
            const tableRows = document.querySelectorAll('#users-table-body tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.01)';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</x-app-layout>
