@php
$pageTitle = 'Admin Dashboard';
$add_new_bus = json_encode([3, "name", "seat", "busForm"]);
$add_new_route = json_encode([3, "start_point", "end_point", "routeForm"]);
$add_new_schedule = json_encode([2, "start_time", "end_time", "scheduleForm"]);
@endphp

<x-app-layout>
    <script src="https://code.jquery.com/jquery-3.7.1.slim.js" integrity="sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: #e2e8f0;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .nav-item:hover {
            background-color: rgba(59, 130, 246, 0.2);
            color: white;
            border-left-color: #3b82f6;
        }
        .nav-item.active {
            background-color: rgba(59, 130, 246, 0.3);
            color: white;
            border-left-color: #60a5fa;
        }
        .nav-item svg {
            margin-right: 12px;
        }
        .section-content {
            display: none;
        }
        .section-content.active {
            display: block;
        }
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            padding: 24px;
            margin-bottom: 24px;
        }
        .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #FCB404, #E6A200);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(252, 180, 4, 0.4);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        .btn-success:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
        }
        .btn-warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            transition: border-color 0.3s ease;
        }
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .table-container {
            overflow-x: auto;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }
        .admin-table {
            width: 100%;
            border-collapse: collapse;
        }
        .admin-table th {
            background: #f8fafc;
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }
        .admin-table td {
            padding: 16px;
            border-bottom: 1px solid #f3f4f6;
        }
        .admin-table tr:hover {
            background: #f8fafc;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: #000;
        }
    </style>

    <div class="flex h-screen bg-gray-50">
        <!-- Sidebar -->
        <div class="text-white w-64 flex-shrink-0 shadow-xl" style="background: linear-gradient(135deg, #FCB404 0%, #E6A200 100%);">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="p-2 rounded-lg" style="background-color: rgba(255, 255, 255, 0.2);">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h2 class="text-xl font-bold">Admin Panel</h2>
                        <p class="text-yellow-100 text-sm">Bus Management System</p>
                    </div>
                </div>
            </div>
            <nav class="mt-2">
                <a href="#dashboard" class="nav-item active" data-section="dashboard">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    Dashboard
                </a>
                <a href="#buses" class="nav-item" data-section="buses">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"></path>
                    </svg>
                    Buses
                </a>
                <a href="#routes" class="nav-item" data-section="routes">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                    </svg>
                    Routes
                </a>
                <a href="#schedules" class="nav-item" data-section="schedules">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Schedules
                </a>
                <a href="#trips" class="nav-item" data-section="trips">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5zM5 14a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1v-3a2 2 0 00-2-2H5z"></path>
                    </svg>
                    Trips
                </a>
                <a href="#fares" class="nav-item" data-section="fares">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    Fare Management
                </a>
                <a href="#users" class="nav-item" data-section="users">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    User Management
                </a>
                <a href="#reports" class="nav-item" data-section="reports">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Reports
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Bar -->
            <header class="bg-white shadow-sm border-b">
                <div class="px-6 py-4 flex justify-between items-center">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800" id="page-title">Dashboard</h1>
                        <p class="text-gray-600 text-sm">Welcome back, {{ Auth::user()->name }}</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <p class="text-sm text-gray-600">{{ now()->format('F d, Y') }}</p>
                            <p class="text-xs text-gray-500">{{ now()->format('l, g:i A') }}</p>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content Area -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6">
                <!-- Dashboard Section -->
                <div id="dashboard" class="section-content active">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Stats Cards -->
                        <div class="card">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ $data['users']->count() }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100 text-green-600">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Buses</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ $data['buses']->count() }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Routes</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ $data['routes']->count() }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5zM5 14a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1v-3a2 2 0 00-2-2H5z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Trips</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ $data['trips']->count() }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="card">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <button class="btn-primary" onclick="showSection('buses')">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add New Bus
                            </button>
                            <button class="btn-primary" onclick="showSection('routes')">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add New Route
                            </button>
                            <button class="btn-primary" onclick="showSection('trips')">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create New Trip
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Buses Section -->
                <div id="buses" class="section-content">
                    <div class="card">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">Bus Management</h3>
                            <button class="btn-primary" onclick="openModal('busModal')">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add New Bus
                            </button>
                        </div>

                        <div class="table-container">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Bus Name</th>
                                        <th>Bus Code</th>
                                        <th>Seats</th>
                                        <th>Driver</th>
                                        <th>Conductor</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="busTableBody">
                                    @forelse ($data['buses'] as $bus)
                                        <tr>
                                            <td>{{ $bus->id }}</td>
                                            <td>{{ $bus->name }}</td>
                                            <td><span class="badge badge-info">{{ $bus->bus_code ?? 'N/A' }}</span></td>
                                            <td>{{ $bus->seat }}</td>
                                            <td>{{ $bus->driver_name ?? 'N/A' }}</td>
                                            <td>{{ $bus->conductor_name ?? 'N/A' }}</td>
                                            <td>
                                                <button class="btn-warning mr-2" onclick="editBus({{ $bus->id }}, '{{ $bus->name }}', '{{ $bus->bus_code ?? '' }}', {{ $bus->seat }}, '{{ $bus->driver_name ?? '' }}', '{{ $bus->conductor_name ?? '' }}')">Edit</button>
                                                <button class="btn-danger" onclick="deleteBus({{ $bus->id }})">Delete</button>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr><td colspan="7" class="text-center py-4">No buses found.</td></tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Routes Section -->
                <div id="routes" class="section-content">
                    <div class="card">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">Route Management</h3>
                            <button class="btn-primary" onclick="openModal('routeModal')">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add New Route
                            </button>
                        </div>

                        <div class="table-container">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Start Point</th>
                                        <th>End Point</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="routeTableBody">
                                    @forelse ($data['routes'] as $route)
                                        <tr>
                                            <td>{{ $route->id }}</td>
                                            <td>{{ $route->start_point }}</td>
                                            <td>{{ $route->end_point }}</td>
                                            <td>
                                                <button class="btn-warning mr-2" onclick="editRoute({{ $route->id }}, '{{ $route->start_point }}', '{{ $route->end_point }}')">Edit</button>
                                                <button class="btn-danger" onclick="deleteRoute({{ $route->id }})">Delete</button>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr><td colspan="4" class="text-center py-4">No routes found.</td></tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Schedules Section -->
                <div id="schedules" class="section-content">
                    <div class="card">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">Schedule Management</h3>
                            <button class="btn-primary" onclick="openModal('scheduleModal')">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add New Schedule
                            </button>
                        </div>

                        <div class="table-container">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Trip Date</th>
                                        <th>Start Time</th>
                                        <th>End Time</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="scheduleTableBody">
                                    @forelse ($data['schedules'] as $schedule)
                                        <tr>
                                            <td>{{ $schedule->id }}</td>
                                            <td>{{ $schedule->formatted_trip_date ?? 'N/A' }}</td>
                                            <td>{{ $schedule->formatted_start_time }}</td>
                                            <td>{{ $schedule->formatted_end_time }}</td>
                                            <td>
                                                @if($schedule->trip_date && \Carbon\Carbon::parse($schedule->trip_date)->isPast())
                                                    <span class="badge badge-danger">Expired</span>
                                                @else
                                                    <span class="badge badge-success">Available</span>
                                                @endif
                                            </td>
                                            <td>
                                                <button class="btn-warning mr-2" onclick="editSchedule({{ $schedule->id }}, '{{ $schedule->trip_date }}', '{{ $schedule->start_time }}', '{{ $schedule->end_time }}')">Edit</button>
                                                <button class="btn-danger" onclick="deleteSchedule({{ $schedule->id }})">Delete</button>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr><td colspan="6" class="text-center py-4">No schedules found.</td></tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Trips Section -->
                <div id="trips" class="section-content">
                    <div class="card">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">Trip Management</h3>
                            <button class="btn-primary" onclick="openModal('tripModal')">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create New Trip
                            </button>
                        </div>

                        <div class="table-container">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Bus</th>
                                        <th>Route</th>
                                        <th>Schedule</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="tripTableBody">
                                    @forelse ($data['trips'] as $trip)
                                        <tr>
                                            <td>{{ $trip->id }}</td>
                                            <td>{{ $trip->bus->name }}</td>
                                            <td>{{ $trip->route->start_point }} → {{ $trip->route->end_point }}</td>
                                            <td>{{ $trip->schedule->start_time }} - {{ $trip->schedule->end_time }}</td>
                                            <td>
                                                <button class="btn-warning mr-2" onclick="editTrip({{ $trip->id }})">Edit</button>
                                                <button class="btn-danger" onclick="deleteTrip({{ $trip->id }})">Delete</button>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr><td colspan="5" class="text-center py-4">No trips found.</td></tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Fare Management Section -->
                <div id="fares" class="section-content">
                    <div class="card">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">Fare Management</h3>
                            <button class="btn-primary" onclick="openModal('fareModal')">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add New Fare
                            </button>
                        </div>

                        <div class="table-container">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Route</th>
                                        <th>Base Fare</th>
                                        <th>Per KM Rate</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="fareTableBody">
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- User Management Section -->
                <div id="users" class="section-content">
                    <div class="card">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">User Management</h3>
                            <button class="btn-primary" onclick="openModal('userModal')">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add New User
                            </button>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Role</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Created</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200" id="users-table-body">
                                    @forelse ($data['users'] as $user)
                                        <tr>
                                            <td class="px-4 py-3 text-sm text-gray-900">{{ $user->id }}</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">{{ $user->name }}</td>
                                            <td class="px-4 py-3 text-sm text-gray-900">{{ $user->email }}</td>
                                            <td class="px-4 py-3 text-sm">
                                                @if($user->roles->first())
                                                    <span class="px-2 py-1 text-xs font-semibold rounded-full
                                                        @if($user->roles->first()->type === 'admin') bg-red-100 text-red-800
                                                        @elseif($user->roles->first()->type === 'agent') bg-blue-100 text-blue-800
                                                        @else bg-green-100 text-green-800 @endif">
                                                        {{ ucfirst($user->roles->first()->type) }}
                                                    </span>
                                                @else
                                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                                        Customer
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="px-4 py-3 text-sm">
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                    Active
                                                </span>
                                            </td>
                                            <td class="px-4 py-3 text-sm text-gray-900">{{ $user->created_at->format('M d, Y') }}</td>
                                            <td class="px-4 py-3 text-sm">
                                                <button class="btn-warning mr-2" onclick="editUser({{ $user->id }})">Edit</button>
                                                @if($user->id !== Auth::id())
                                                    <button class="btn-danger" onclick="deleteUser({{ $user->id }})">Delete</button>
                                                @endif
                                            </td>
                                        </tr>
                                    @empty
                                        <tr><td colspan="7" class="text-center py-4">No users found.</td></tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Reports Section -->
                <div id="reports" class="section-content">
                    <div class="card">
                        <h3 class="text-xl font-semibold text-gray-800 mb-6">Reports Dashboard</h3>

                        <!-- Summary Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <div class="flex items-center">
                                    <div class="p-2 bg-blue-100 rounded-lg">
                                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-blue-600">Total Bookings</p>
                                        <p class="text-lg font-bold text-blue-800">{{ $data['tickets']->count() }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="flex items-center">
                                    <div class="p-2 bg-green-100 rounded-lg">
                                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-green-600">Total Revenue</p>
                                        <p class="text-lg font-bold text-green-800">₱{{ number_format($data['tickets']->sum('price'), 2) }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-purple-50 p-4 rounded-lg">
                                <div class="flex items-center">
                                    <div class="p-2 bg-purple-100 rounded-lg">
                                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-purple-600">Active Trips</p>
                                        <p class="text-lg font-bold text-purple-800">{{ $data['trips']->count() }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <div class="flex items-center">
                                    <div class="p-2 bg-yellow-100 rounded-lg">
                                        <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-yellow-600">Total Routes</p>
                                        <p class="text-lg font-bold text-yellow-800">{{ $data['routes']->count() }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Bookings Table -->
                        <div class="mt-6">
                            <h4 class="text-lg font-semibold text-gray-800 mb-4">Recent Bookings</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Booking ID</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">User</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Route</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Seat</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        @forelse($data['tickets']->take(10) as $ticket)
                                            <tr>
                                                <td class="px-4 py-3 text-sm text-gray-900">{{ $ticket->id }}</td>
                                                <td class="px-4 py-3 text-sm text-gray-900">{{ $ticket->user->name ?? 'N/A' }}</td>
                                                <td class="px-4 py-3 text-sm text-gray-900">
                                                    {{ $ticket->trip->route->start_point ?? 'N/A' }} → {{ $ticket->trip->route->end_point ?? 'N/A' }}
                                                </td>
                                                <td class="px-4 py-3 text-sm text-gray-900">{{ $ticket->seat_number ?? 'N/A' }}</td>
                                                <td class="px-4 py-3 text-sm text-gray-900">₱{{ number_format($ticket->price, 2) }}</td>
                                                <td class="px-4 py-3 text-sm text-gray-900">{{ $ticket->created_at->format('M d, Y') }}</td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="6" class="px-4 py-3 text-center text-gray-500">No bookings found</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modals -->
    <!-- Bus Modal -->
    <div id="busModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('busModal')">&times;</span>
            <h2 class="text-xl font-bold mb-4" id="busModalTitle">Add New Bus</h2>
            <form id="busForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Bus Name</label>
                    <input type="text" id="busName" name="name" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Bus Code</label>
                    <input type="text" id="busCode" name="bus_code" class="form-input" placeholder="e.g., BUS001" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Number of Seats</label>
                    <input type="number" id="busSeats" name="seat" class="form-input" min="1" max="100" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Driver Name</label>
                    <input type="text" id="busDriver" name="driver_name" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Conductor Name</label>
                    <input type="text" id="busConductor" name="conductor_name" class="form-input" required>
                </div>
                <div class="flex gap-4">
                    <button type="submit" class="btn-success">Save Bus</button>
                    <button type="button" class="btn-primary" onclick="closeModal('busModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Route Modal -->
    <div id="routeModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('routeModal')">&times;</span>
            <h2 class="text-xl font-bold mb-4" id="routeModalTitle">Add New Route</h2>
            <form id="routeForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Start Point</label>
                    <input type="text" id="routeStartPoint" name="start_point" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">End Point</label>
                    <input type="text" id="routeEndPoint" name="end_point" class="form-input" required>
                </div>
                <div class="flex gap-4">
                    <button type="submit" class="btn-success">Save Route</button>
                    <button type="button" class="btn-primary" onclick="closeModal('routeModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Schedule Modal -->
    <div id="scheduleModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('scheduleModal')">&times;</span>
            <h2 class="text-xl font-bold mb-4" id="scheduleModalTitle">Add New Schedule</h2>
            <form id="scheduleForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Trip Date</label>
                    <input type="date" id="scheduleTripDate" name="trip_date" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Start Time</label>
                    <input type="time" id="scheduleStartTime" name="start_time" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">End Time</label>
                    <input type="time" id="scheduleEndTime" name="end_time" class="form-input" required>
                </div>
                <div class="flex gap-4">
                    <button type="submit" class="btn-success">Save Schedule</button>
                    <button type="button" class="btn-primary" onclick="closeModal('scheduleModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Trip Modal -->
    <div id="tripModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('tripModal')">&times;</span>
            <h2 class="text-xl font-bold mb-4" id="tripModalTitle">Create New Trip</h2>
            <form id="tripForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Bus</label>
                    <select id="tripBus" name="bus_id" class="form-input" required>
                        <option value="">Select Bus</option>
                        @foreach($data['buses'] as $bus)
                            <option value="{{ $bus->id }}">{{ $bus->name }} ({{ $bus->seat }} seats)</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Route</label>
                    <select id="tripRoute" name="route_id" class="form-input" required>
                        <option value="">Select Route</option>
                        @foreach($data['routes'] as $route)
                            <option value="{{ $route->id }}">{{ $route->start_point }} → {{ $route->end_point }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Schedule</label>
                    <select id="tripSchedule" name="schedule_id" class="form-input" required>
                        <option value="">Select Schedule</option>
                        @foreach($data['schedules'] as $schedule)
                            <option value="{{ $schedule->id }}">{{ $schedule->start_time }} - {{ $schedule->end_time }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="flex gap-4">
                    <button type="submit" class="btn-success">Create Trip</button>
                    <button type="button" class="btn-primary" onclick="closeModal('tripModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Fare Modal -->
    <div id="fareModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('fareModal')">&times;</span>
            <h2 class="text-xl font-bold mb-4" id="fareModalTitle">Add New Fare</h2>
            <form id="fareForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Route</label>
                    <select id="fareRoute" name="route_id" class="form-input" required>
                        <option value="">Select Route</option>
                        @foreach($data['routes'] as $route)
                            <option value="{{ $route->id }}">{{ $route->start_point }} → {{ $route->end_point }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Base Fare (₱)</label>
                    <input type="number" id="fareBaseFare" name="base_fare" class="form-input" step="0.01" min="0" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Per KM Rate (₱) - Optional</label>
                    <input type="number" id="farePerKmRate" name="per_km_rate" class="form-input" step="0.01" min="0">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Fare (₱) - Optional</label>
                    <input type="number" id="fareMinFare" name="minimum_fare" class="form-input" step="0.01" min="0">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Maximum Fare (₱) - Optional</label>
                    <input type="number" id="fareMaxFare" name="maximum_fare" class="form-input" step="0.01" min="0">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Description - Optional</label>
                    <textarea id="fareDescription" name="description" class="form-input" rows="3"></textarea>
                </div>
                <div class="flex gap-4">
                    <button type="submit" class="btn-success">Save Fare</button>
                    <button type="button" class="btn-primary" onclick="closeModal('fareModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- User Modal -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('userModal')">&times;</span>
            <h2 class="text-xl font-bold mb-4" id="userModalTitle">Add New User</h2>
            <form id="userForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <input type="text" id="userName" name="name" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                    <input type="email" id="userEmail" name="email" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                    <input type="tel" id="userPhone" name="phone" class="form-input" placeholder="09123456789">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                    <select id="userGender" name="Gender" class="form-input" required>
                        <option value="">Select Gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                    <select id="userRole" name="role" class="form-input" required>
                        <option value="">Select Role</option>
                        <option value="customer">Customer</option>
                        <option value="agent">Agent</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input type="password" id="userPassword" name="password" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Confirm Password</label>
                    <input type="password" id="userPasswordConfirm" name="password_confirmation" class="form-input" required>
                </div>
                <div class="flex gap-4">
                    <button type="submit" class="btn-success">Save User</button>
                    <button type="button" class="btn-primary" onclick="closeModal('userModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Navigation functionality
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.section-content').forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionName).classList.add('active');

            // Add active class to clicked nav item
            document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

            // Update page title
            const titles = {
                'dashboard': 'Dashboard',
                'buses': 'Bus Management',
                'routes': 'Route Management',
                'schedules': 'Schedule Management',
                'trips': 'Trip Management',
                'fares': 'Fare Management'
            };
            document.getElementById('page-title').textContent = titles[sectionName] || 'Admin Panel';

            // Load section-specific data
            if (sectionName === 'fares') {
                loadFares();
            }
        }

        // Navigation click handlers
        document.querySelectorAll('.nav-item[data-section]').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const section = this.getAttribute('data-section');
                showSection(section);
            });
        });

        // Modal functionality
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            // Reset form if exists
            const form = document.querySelector(`#${modalId} form`);
            if (form) form.reset();
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // Fare Management
        function loadFares() {
            fetch('/fares')
                .then(response => response.json())
                .then(data => {
                    const tbody = document.getElementById('fareTableBody');
                    tbody.innerHTML = '';

                    if (data.fares && data.fares.length > 0) {
                        data.fares.forEach(fare => {
                            const row = `
                                <tr>
                                    <td>${fare.id}</td>
                                    <td>${fare.route.start_point} → ${fare.route.end_point}</td>
                                    <td>₱${parseFloat(fare.base_fare).toFixed(2)}</td>
                                    <td>₱${parseFloat(fare.per_km_rate || 0).toFixed(2)}</td>
                                    <td>
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full ${fare.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                            ${fare.is_active ? 'Active' : 'Inactive'}
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn-warning mr-2" onclick="editFare(${fare.id})">Edit</button>
                                        ${!fare.is_active ? `<button class="btn-success mr-2" onclick="activateFare(${fare.id})">Activate</button>` : ''}
                                        <button class="btn-danger" onclick="deleteFare(${fare.id})">Delete</button>
                                    </td>
                                </tr>
                            `;
                            tbody.innerHTML += row;
                        });
                    } else {
                        tbody.innerHTML = '<tr><td colspan="6" class="text-center py-4">No fares found.</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('Error loading fares:', error);
                });
        }

        document.getElementById('fareForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch('/fares', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(Object.fromEntries(formData))
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: data.message,
                        confirmButtonColor: '#10b981'
                    });
                    closeModal('fareModal');
                    loadFares(); // Refresh fare table
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: data.message || 'An error occurred',
                        confirmButtonColor: '#ef4444'
                    });
                }
            })
            .catch(error => {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'An error occurred while saving the fare.',
                    confirmButtonColor: '#ef4444'
                });
            });
        });

        function deleteFare(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/fares/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted!',
                                text: 'Fare has been deleted.',
                                confirmButtonColor: '#10b981'
                            });
                            loadFares();
                        }
                    });
                }
            });
        }

        function editFare(id) {
            // Fetch fare data first
            fetch(`/fares/${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const fare = data.fare;

                        // Show edit modal with current values
                        Swal.fire({
                            title: 'Edit Fare',
                            html: `
                                <div class="text-left">
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Route</label>
                                        <select id="edit-route-id" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                            <option value="">Select Route</option>
                                            ${window.routeOptions.map(route =>
                                                `<option value="${route.id}" ${route.id == fare.route_id ? 'selected' : ''}>
                                                    ${route.start_point} → ${route.end_point}
                                                </option>`
                                            ).join('')}
                                        </select>
                                    </div>
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Base Fare (₱)</label>
                                        <input type="number" id="edit-base-fare" step="0.01" min="0"
                                               value="${fare.base_fare}" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Per KM Rate (₱)</label>
                                        <input type="number" id="edit-per-km-rate" step="0.01" min="0"
                                               value="${fare.per_km_rate || 0}" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Fare (₱)</label>
                                        <input type="number" id="edit-minimum-fare" step="0.01" min="0"
                                               value="${fare.minimum_fare || 0}" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Maximum Fare (₱)</label>
                                        <input type="number" id="edit-maximum-fare" step="0.01" min="0"
                                               value="${fare.maximum_fare || 0}" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    </div>
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                        <textarea id="edit-description" rows="3"
                                                  class="w-full border border-gray-300 rounded-md px-3 py-2">${fare.description || ''}</textarea>
                                    </div>
                                </div>
                            `,
                            showCancelButton: true,
                            confirmButtonText: 'Update Fare',
                            cancelButtonText: 'Cancel',
                            confirmButtonColor: '#3b82f6',
                            preConfirm: () => {
                                const routeId = document.getElementById('edit-route-id').value;
                                const baseFare = document.getElementById('edit-base-fare').value;
                                const perKmRate = document.getElementById('edit-per-km-rate').value;
                                const minimumFare = document.getElementById('edit-minimum-fare').value;
                                const maximumFare = document.getElementById('edit-maximum-fare').value;
                                const description = document.getElementById('edit-description').value;

                                if (!routeId || !baseFare) {
                                    Swal.showValidationMessage('Route and Base Fare are required');
                                    return false;
                                }

                                return {
                                    route_id: routeId,
                                    base_fare: baseFare,
                                    per_km_rate: perKmRate,
                                    minimum_fare: minimumFare,
                                    maximum_fare: maximumFare,
                                    description: description
                                };
                            }
                        }).then((result) => {
                            if (result.isConfirmed) {
                                // Update fare
                                fetch(`/fares/${id}`, {
                                    method: 'PUT',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                    },
                                    body: JSON.stringify(result.value)
                                })
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        Swal.fire({
                                            icon: 'success',
                                            title: 'Updated!',
                                            text: 'Fare has been updated successfully.',
                                            confirmButtonColor: '#10b981'
                                        });
                                        loadFares();
                                    } else {
                                        Swal.fire({
                                            icon: 'error',
                                            title: 'Error!',
                                            text: data.message || 'Failed to update fare.',
                                            confirmButtonColor: '#ef4444'
                                        });
                                    }
                                })
                                .catch(error => {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Error!',
                                        text: 'An error occurred while updating the fare.',
                                        confirmButtonColor: '#ef4444'
                                    });
                                });
                            }
                        });
                    }
                })
                .catch(error => {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Failed to fetch fare data.',
                        confirmButtonColor: '#ef4444'
                    });
                });
        }

        function activateFare(id) {
            fetch(`/fares/${id}/activate`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Activated!',
                        text: 'Fare has been activated.',
                        confirmButtonColor: '#10b981'
                    });
                    loadFares();
                }
            });
        }

        // Route Management Functions
        document.getElementById('routeForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            // Simple form submission - you can implement proper API endpoints
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Route functionality will be implemented with proper API endpoints.',
                confirmButtonColor: '#10b981'
            });
            closeModal('routeModal');
        });

        function editRoute(id, startPoint, endPoint) {
            document.getElementById('routeModalTitle').textContent = 'Edit Route';
            document.getElementById('routeStartPoint').value = startPoint;
            document.getElementById('routeEndPoint').value = endPoint;
            openModal('routeModal');
        }

        function deleteRoute(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "This will delete the route permanently!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Deleted!',
                        text: 'Route deletion functionality will be implemented.',
                        confirmButtonColor: '#10b981'
                    });
                }
            });
        }

        // Schedule Management Functions
        document.getElementById('scheduleForm').addEventListener('submit', function(e) {
            e.preventDefault();

            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Schedule functionality will be implemented with proper API endpoints.',
                confirmButtonColor: '#10b981'
            });
            closeModal('scheduleModal');
        });

        function editSchedule(id, tripDate, startTime, endTime) {
            document.getElementById('scheduleModalTitle').textContent = 'Edit Schedule';
            document.getElementById('scheduleTripDate').value = tripDate;
            document.getElementById('scheduleStartTime').value = startTime;
            document.getElementById('scheduleEndTime').value = endTime;

            // Store the schedule ID for updating
            document.getElementById('scheduleForm').setAttribute('data-schedule-id', id);
            openModal('scheduleModal');
        }

        function deleteSchedule(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "This will delete the schedule permanently!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Deleted!',
                        text: 'Schedule deletion functionality will be implemented.',
                        confirmButtonColor: '#10b981'
                    });
                }
            });
        }

        // Trip Management Functions
        document.getElementById('tripForm').addEventListener('submit', function(e) {
            e.preventDefault();

            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Trip functionality will be implemented with proper API endpoints.',
                confirmButtonColor: '#10b981'
            });
            closeModal('tripModal');
        });

        function editTrip(id) {
            Swal.fire({
                icon: 'info',
                title: 'Edit Trip',
                text: 'Edit functionality will be implemented soon.',
                confirmButtonColor: '#3b82f6'
            });
        }

        function deleteTrip(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "This will delete the trip permanently!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Deleted!',
                        text: 'Trip deletion functionality will be implemented.',
                        confirmButtonColor: '#10b981'
                    });
                }
            });
        }

        // Make route options available globally
        window.routeOptions = @json($data['routes']);

        // Bus Management Functions
        function editBus(id, name, busCode, seats, driver, conductor) {
            document.getElementById('busModalTitle').textContent = 'Edit Bus';
            document.getElementById('busName').value = name;
            document.getElementById('busCode').value = busCode;
            document.getElementById('busSeats').value = seats;
            document.getElementById('busDriver').value = driver;
            document.getElementById('busConductor').value = conductor;

            // Store the bus ID for updating
            document.getElementById('busForm').setAttribute('data-bus-id', id);
            openModal('busModal');
        }

        function deleteBus(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/buses/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted!',
                                text: 'Bus has been deleted.',
                                confirmButtonColor: '#10b981'
                            });
                            location.reload(); // Refresh the page
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: data.message || 'Failed to delete bus.',
                                confirmButtonColor: '#ef4444'
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'An error occurred while deleting the bus.',
                            confirmButtonColor: '#ef4444'
                        });
                    });
                }
            });
        }

        // Route Management Functions
        function deleteRoute(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/routes/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted!',
                                text: 'Route has been deleted.',
                                confirmButtonColor: '#10b981'
                            });
                            location.reload(); // Refresh the page
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: data.message || 'Failed to delete route.',
                                confirmButtonColor: '#ef4444'
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'An error occurred while deleting the route.',
                            confirmButtonColor: '#ef4444'
                        });
                    });
                }
            });
        }

        // Schedule Management Functions
        function deleteSchedule(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/schedules/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted!',
                                text: 'Schedule has been deleted.',
                                confirmButtonColor: '#10b981'
                            });
                            location.reload(); // Refresh the page
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: data.message || 'Failed to delete schedule.',
                                confirmButtonColor: '#ef4444'
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'An error occurred while deleting the schedule.',
                            confirmButtonColor: '#ef4444'
                        });
                    });
                }
            });
        }

        // Trip Management Functions
        function deleteTrip(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/trips/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted!',
                                text: 'Trip has been deleted.',
                                confirmButtonColor: '#10b981'
                            });
                            location.reload(); // Refresh the page
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: data.message || 'Failed to delete trip.',
                                confirmButtonColor: '#ef4444'
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'An error occurred while deleting the trip.',
                            confirmButtonColor: '#ef4444'
                        });
                    });
                }
            });
        }

        // User Management Functions
        function editUser(id) {
            // Fetch user data first
            fetch(`/admin/users/${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const user = data.user;

                        // Populate form with current values
                        document.getElementById('userModalTitle').textContent = 'Edit User';
                        document.getElementById('userName').value = user.name;
                        document.getElementById('userEmail').value = user.email;
                        document.getElementById('userPhone').value = user.phone || '';
                        document.getElementById('userGender').value = user.Gender || '';
                        document.getElementById('userRole').value = user.roles.length > 0 ? user.roles[0].type : 'customer';

                        // Hide password fields for editing
                        document.getElementById('userPassword').parentElement.style.display = 'none';
                        document.getElementById('userPasswordConfirm').parentElement.style.display = 'none';

                        // Store user ID for updating
                        document.getElementById('userForm').setAttribute('data-user-id', id);
                        openModal('userModal');
                    }
                })
                .catch(error => {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Failed to fetch user data.',
                        confirmButtonColor: '#ef4444'
                    });
                });
        }

        function deleteUser(id) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/admin/users/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted!',
                                text: 'User has been deleted.',
                                confirmButtonColor: '#10b981'
                            });
                            location.reload(); // Refresh the page
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: data.message || 'Failed to delete user.',
                                confirmButtonColor: '#ef4444'
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'An error occurred while deleting the user.',
                            confirmButtonColor: '#ef4444'
                        });
                    });
                }
            });
        }

        // User form submission
        document.getElementById('userForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const userId = this.getAttribute('data-user-id');
            const isEdit = userId !== null;

            const url = isEdit ? `/admin/users/${userId}` : '/admin/users';
            const method = isEdit ? 'PUT' : 'POST';

            // Convert FormData to JSON
            const data = {};
            formData.forEach((value, key) => {
                data[key] = value;
            });

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: isEdit ? 'Updated!' : 'Created!',
                        text: data.message,
                        confirmButtonColor: '#10b981'
                    });
                    closeModal('userModal');
                    location.reload(); // Refresh the page
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: data.message || 'Failed to save user.',
                        confirmButtonColor: '#ef4444'
                    });
                }
            })
            .catch(error => {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'An error occurred while saving the user.',
                    confirmButtonColor: '#ef4444'
                });
            });
        });

        // Reset user form when modal closes
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';

            if (modalId === 'userModal') {
                // Reset form
                document.getElementById('userForm').reset();
                document.getElementById('userForm').removeAttribute('data-user-id');
                document.getElementById('userModalTitle').textContent = 'Add New User';

                // Show password fields again
                document.getElementById('userPassword').parentElement.style.display = 'block';
                document.getElementById('userPasswordConfirm').parentElement.style.display = 'block';
            }

            // Reset other forms if exists
            const form = document.querySelector(`#${modalId} form`);
            if (form) form.reset();
        }
    </script>
</x-app-layout>
