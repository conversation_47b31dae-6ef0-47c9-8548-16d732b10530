<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('User Management - DEBUG VERSION') }}
            </h2>
            <div class="flex gap-2">
                <button id="quickCreateAgentBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                    Quick Create Agent
                </button>
                <button id="createUserBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                    Create User
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Debug Info -->
            <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
                <h3 class="font-bold">Debug Info:</h3>
                <p>Users count: {{ $users->count() }}</p>
                <p>Roles count: {{ $roles->count() }}</p>
                <p>Current user: {{ Auth::user()->name ?? 'Not logged in' }}</p>
                <p>Current role: {{ Auth::user()->roles->first()->type ?? 'No role' }}</p>
            </div>

            <!-- Debug Console -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Debug Console</h3>
                    <div id="debugOutput" class="bg-gray-100 p-4 rounded min-h-32 font-mono text-sm whitespace-pre-wrap"></div>
                </div>
            </div>

            <!-- Users Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">All Users</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($users as $user)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $user->name }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $user->email }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if($user->roles->count() > 0)
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    {{ ucfirst($user->roles->first()->type) }}
                                                </span>
                                            @else
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                    No Role
                                                </span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Simple Modal -->
    <div id="testModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
                <h3 class="text-lg font-semibold mb-4">Test Modal</h3>
                <p>This modal opened successfully!</p>
                <button id="closeTestModal" class="mt-4 bg-gray-500 text-white px-4 py-2 rounded">Close</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function log(message) {
            console.log(message);
            const output = document.getElementById('debugOutput');
            if (output) {
                output.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
                output.scrollTop = output.scrollHeight;
            }
        }

        log('Script loaded');

        document.addEventListener('DOMContentLoaded', function() {
            log('DOM loaded');

            const agentBtn = document.getElementById('quickCreateAgentBtn');
            const userBtn = document.getElementById('createUserBtn');
            const modal = document.getElementById('testModal');
            const closeBtn = document.getElementById('closeTestModal');

            log('Elements found: agentBtn=' + !!agentBtn + ', userBtn=' + !!userBtn + ', modal=' + !!modal);

            if (agentBtn) {
                agentBtn.addEventListener('click', function() {
                    log('Agent button clicked - opening test modal');
                    if (modal) {
                        modal.classList.remove('hidden');
                    }
                });
                log('Agent button listener added');
            }

            if (userBtn) {
                userBtn.addEventListener('click', function() {
                    log('User button clicked - showing SweetAlert');
                    Swal.fire({
                        title: 'User Button Clicked!',
                        text: 'This button is working perfectly!',
                        icon: 'success'
                    });
                });
                log('User button listener added');
            }

            if (closeBtn) {
                closeBtn.addEventListener('click', function() {
                    log('Close button clicked');
                    modal.classList.add('hidden');
                });
            }

            log('All event listeners added successfully');
        });
    </script>
</x-app-layout>
