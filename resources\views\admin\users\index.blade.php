<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('User Management') }}
            </h2>
            <div class="flex gap-2">
                <button onclick="alert('TEST BUTTON WORKS!');" class="bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-sm">
                    TEST
                </button>
                <button onclick="var modal = document.getElementById('createAgentModal'); alert('Modal found: ' + (modal ? 'YES' : 'NO'));" class="bg-yellow-600 hover:bg-yellow-700 text-white px-2 py-1 rounded text-sm">
                    CHECK MODAL
                </button>
                <button onclick="alert('Page HTML length: ' + document.body.innerHTML.length);" class="bg-purple-600 hover:bg-purple-700 text-white px-2 py-1 rounded text-sm">
                    CHECK HTML
                </button>
                <button id="createAgentBtn" onclick="alert('Agent button clicked!'); document.getElementById('createAgentModal').classList.remove('hidden');" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                    Quick Create Agent
                </button>
                <button id="createUserBtn" onclick="alert('User button clicked!'); document.getElementById('createUserModal').classList.remove('hidden');" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                    Create User
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Users Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">All Users</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                        User table temporarily disabled for debugging - Total users: {{ $users->count() }}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination temporarily disabled for debugging -->
                    <div class="mt-4">
                        <p class="text-gray-500">Pagination disabled for debugging</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- DEBUG: This should appear in page source -->
    <!-- MODAL SECTION STARTS HERE -->

    <!-- Create Agent Modal -->
    <div id="createAgentModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Create Agent</h3>
                <form id="createAgentForm">
                    @csrf
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Agent Name</label>
                        <input type="text" name="name" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" name="email" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Phone (Optional)</label>
                        <input type="text" name="phone" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                    </div>
                    <div class="flex justify-end gap-2">
                        <button type="button" onclick="document.getElementById('createAgentModal').classList.add('hidden'); document.getElementById('createAgentForm').reset();" class="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded">
                            Cancel
                        </button>
                        <button type="submit" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded">
                            Create Agent
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Create User Modal -->
    <div id="createUserModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Create User</h3>
                <form id="createUserForm">
                    @csrf
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Name</label>
                        <input type="text" name="name" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" name="email" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Password</label>
                        <input type="password" name="password" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Phone (Optional)</label>
                        <input type="text" name="phone" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Role</label>
                        <select name="role" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                            <option value="">Select Role</option>
                            <option value="customer">Customer</option>
                            <option value="agent">Agent</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <div class="flex justify-end gap-2">
                        <button type="button" onclick="document.getElementById('createUserModal').classList.add('hidden'); document.getElementById('createUserForm').reset();" class="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded">
                            Cancel
                        </button>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded">
                            Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Simple test
        alert('Page is loading with JavaScript!');

        // Simple functions for edit/delete (placeholder)
        function editUser(userId) {
            alert('Edit user ' + userId + ' - Coming soon!');
        }

        function deleteUser(userId) {
            if (confirm('Are you sure you want to delete this user?')) {
                alert('Delete user ' + userId + ' - Coming soon!');
            }
        }
    </script>
</x-app-layout>
