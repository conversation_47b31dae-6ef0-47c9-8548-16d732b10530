@extends('layouts.app')

@section('content')
<style>
/* Enhanced GL Bus Color Palette for Terminal */
:root {
    --primary-gold: #FCB404;
    --primary-gold-dark: #E6A200;
    --secondary-teal: #14B8A6;
    --secondary-teal-dark: #0F766E;
    --accent-blue: #3B82F6;
    --accent-purple: #8B5CF6;
    --accent-green: #10B981;
    --accent-orange: #F97316;
    --accent-red: #EF4444;
    --neutral-gray: #6B7280;
}

.gradient-primary {
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
}

.gradient-teal {
    background: linear-gradient(135deg, var(--secondary-teal) 0%, var(--secondary-teal-dark) 100%);
}

.gradient-blue {
    background: linear-gradient(135deg, var(--accent-blue) 0%, #1D4ED8 100%);
}

.gradient-purple {
    background: linear-gradient(135deg, var(--accent-purple) 0%, #7C3AED 100%);
}

.gradient-green {
    background: linear-gradient(135deg, var(--accent-green) 0%, #059669 100%);
}

.gradient-orange {
    background: linear-gradient(135deg, var(--accent-orange) 0%, #EA580C 100%);
}

.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-available {
    background-color: #D1FAE5;
    color: #065F46;
}

.status-full {
    background-color: #FEE2E2;
    color: #991B1B;
}

.status-partial {
    background-color: #FEF3C7;
    color: #92400E;
}

/* Seat Map Styles */
.bus-layout {
    background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #cbd5e1;
    border-radius: 20px;
    padding: 20px;
    margin: 10px auto;
    max-width: 350px;
    position: relative;
}

.driver-area {
    border: 2px dashed #94a3b8;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-radius: 10px;
    padding: 8px;
    text-align: center;
    margin-bottom: 15px;
    font-weight: bold;
    color: #475569;
}

.seat-row {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
    gap: 4px;
}

.seat {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
}

.seat:hover {
    transform: scale(1.15);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    z-index: 10;
}

.seat-available {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border-color: #065f46;
}

.seat-available:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    border-color: #FCB404;
}

.seat-booked {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    cursor: not-allowed;
    border-color: #991b1b;
}

.seat-selected {
    background: linear-gradient(135deg, #FCB404 0%, #E6A200 100%);
    color: white;
    border-color: #92400e;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.aisle {
    width: 24px;
    text-align: center;
    font-weight: bold;
    color: #64748b;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.seats-container {
    background: rgba(255,255,255,0.7);
    border-radius: 12px;
    padding: 15px;
    margin-top: 10px;
}

.seat-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 15px;
    font-size: 12px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.legend-seat {
    width: 16px;
    height: 16px;
    border-radius: 3px;
}

.seat-map-modal .swal2-popup {
    border-radius: 15px;
    max-width: 600px;
}

.trip-info-card {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #cbd5e1;
}

.seat-summary {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border-radius: 10px;
    padding: 15px;
    margin-top: 15px;
    border: 1px solid #3b82f6;
}
</style>

<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border mb-6">
            <div class="p-6 border-b" style="background: linear-gradient(135deg, #FCB404 0%, #E6A200 100%);">
                <div class="flex items-center">
                    <div class="p-3 bg-white bg-opacity-20 rounded-lg">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-white">🏢 Terminal Dashboard</h1>
                        <p class="text-yellow-100">Agent Control Panel</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm border p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-3 rounded-full gradient-primary">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5zM5 14a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1v-3a2 2 0 00-2-2H5z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Available Trips</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $data['trips']->total() ?? 0 }}</p>
                        <p class="text-xs text-gray-500">{{ $data['trips']->count() }} shown</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-3 rounded-full gradient-teal">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Recent Bookings</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $data['recent_bookings']->total() ?? 0 }}</p>
                        <p class="text-xs text-gray-500">{{ $data['recent_bookings']->count() }} shown</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-3 rounded-full gradient-blue">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Revenue Today</p>
                        <p class="text-2xl font-bold text-gray-900">₱{{ number_format($data['today_revenue'] ?? 0, 2) }}</p>
                        <p class="text-xs text-gray-500">{{ $data['today_bookings'] ?? 0 }} bookings today</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-3 rounded-full gradient-purple">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Passengers</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $data['recent_bookings']->sum(function($booking) { return 1; }) }}</p>
                        <p class="text-xs text-blue-600">Currently traveling</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-sm border mb-8">
            <div class="p-6 border-b">
                <h2 class="text-lg font-semibold text-gray-900">🚀 Terminal Actions</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <a href="{{ route('agent.improved-walk-in') }}" class="flex items-center justify-center p-6 border-2 border-dashed border-yellow-300 rounded-lg hover:border-yellow-400 hover:bg-yellow-50 transition-colors">
                        <div class="text-center">
                            <svg class="w-8 h-8 text-yellow-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span class="text-yellow-700 font-medium">Walk-in Booking</span>
                            <p class="text-sm text-yellow-600 mt-1">Modern booking terminal</p>
                        </div>
                    </a>

                    <button onclick="viewDailyReports()" class="flex items-center justify-center p-6 border-2 border-dashed border-green-300 rounded-lg hover:border-green-400 hover:bg-green-50 transition-colors">
                        <div class="text-center">
                            <svg class="w-8 h-8 text-green-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            <span class="text-green-700 font-medium">Daily Reports</span>
                            <p class="text-sm text-green-600 mt-1">View today's sales</p>
                        </div>
                    </button>

                    <button onclick="printTicketSummary()" class="flex items-center justify-center p-6 border-2 border-dashed border-purple-300 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-colors">
                        <div class="text-center">
                            <svg class="w-8 h-8 text-purple-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                            </svg>
                            <span class="text-purple-700 font-medium">Print Summary</span>
                            <p class="text-sm text-purple-600 mt-1">Print ticket summary</p>
                        </div>
                    </button>

                    <button onclick="manageSchedules()" class="flex items-center justify-center p-6 border-2 border-dashed border-orange-300 rounded-lg hover:border-orange-400 hover:bg-orange-50 transition-colors">
                        <div class="text-center">
                            <svg class="w-8 h-8 text-orange-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <span class="text-orange-700 font-medium">Manage Schedules</span>
                            <p class="text-sm text-orange-600 mt-1">View trip schedules</p>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- Available Trips -->
        <div class="bg-white rounded-lg shadow-sm border mb-8">
            <div class="p-6 border-b">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">
                            🚌 {{ $data['showing_all'] ? 'All Available Trips' : 'Available Trips Today' }}
                        </h2>
                        <p class="text-sm text-gray-600 mt-1">
                            @if($data['showing_all'])
                                Showing all {{ $data['total_trips_count'] }} trips in the system
                                <span class="ml-2 text-xs">
                                    ({{ $data['available_trips_count'] }} available, {{ $data['expired_trips_count'] }} expired)
                                </span>
                            @else
                                {{ $data['today_trips_count'] }} available trips for {{ now('Asia/Manila')->format('F d, Y') }}
                                <span class="ml-2 text-xs text-green-600">
                                    ⏰ Only showing trips that haven't departed yet
                                </span>
                            @endif
                        </p>
                        <div class="mt-2 p-2 bg-blue-50 rounded-lg border border-blue-200">
                            <p class="text-xs text-gray-600 mb-1">🕐 Current Manila Time:</p>
                            <p id="current-time" class="font-mono text-sm font-semibold text-blue-700">{{ now('Asia/Manila')->format('F d, Y g:i:s A') }}</p>
                            <p class="text-xs text-green-600 mt-1">⚡ Updates every second</p>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="toggleTripsView('today')" id="todayBtn" class="px-4 py-2 {{ !$data['showing_all'] ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700' }} rounded-lg text-sm font-medium">
                            Today ({{ $data['today_trips_count'] }})
                        </button>
                        <button onclick="toggleTripsView('all')" id="allBtn" class="px-4 py-2 {{ $data['showing_all'] ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700' }} rounded-lg text-sm font-medium">
                            All Trips ({{ $data['total_trips_count'] }})
                        </button>
                        <button onclick="refreshTripStatus()" class="px-4 py-2 bg-green-500 text-white rounded-lg text-sm font-medium hover:bg-green-600 transition-colors">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Refresh
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table id="trips-table" class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trip ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Route</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bus</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Available Seats</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @forelse($data['trips'] as $trip)
                            @php
                                $availableSeats = $trip->bus->capacity - $trip->tickets->count();
                                $seatPercentage = ($availableSeats / $trip->bus->capacity) * 100;

                                // Use the helper methods from Schedule model
                                $isExpired = $trip->schedule->is_expired;
                                $isAboutToExpire = $trip->schedule->is_about_to_expire;
                                $isBookable = !$isExpired && $availableSeats > 0;
                            @endphp
                            <tr class="hover:bg-gray-50 transition-colors {{ $isExpired ? 'opacity-60 bg-red-50' : '' }}">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <span class="font-mono text-blue-600">#{{ str_pad($trip->id, 4, '0', STR_PAD_LEFT) }}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                        {{ $trip->route->start_point }}
                                        <svg class="w-4 h-4 mx-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                        </svg>
                                        <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                                        {{ $trip->route->end_point }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"></path>
                                        </svg>
                                        {{ $trip->bus->name }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div class="text-center">
                                        <div class="text-sm font-medium {{ $isExpired ? 'text-red-600' : ($isAboutToExpire ? 'text-orange-600' : 'text-gray-900') }}">
                                            {{ $trip->schedule->formatted_start_time }}
                                            @if($isExpired)
                                                <span class="ml-1 text-xs text-red-500">EXPIRED</span>
                                            @elseif($isAboutToExpire)
                                                <span class="ml-1 text-xs text-orange-500">DEPARTING SOON</span>
                                            @endif
                                        </div>
                                        <div class="text-xs text-gray-500">to {{ $trip->schedule->formatted_end_time }}</div>
                                        <div class="text-xs text-gray-400">{{ $trip->schedule->formatted_trip_date }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    @if($seatPercentage > 50)
                                        <span class="status-badge status-available">
                                            {{ $availableSeats }}/{{ $trip->bus->capacity }} Available
                                        </span>
                                    @elseif($seatPercentage > 0)
                                        <span class="status-badge status-partial">
                                            {{ $availableSeats }}/{{ $trip->bus->capacity }} Few Left
                                        </span>
                                    @else
                                        <span class="status-badge status-full">
                                            Full
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    @if($isBookable)
                                        <button onclick="bookWalkIn({{ $trip->id }})" class="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium text-white mr-2 transition-colors gradient-primary">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                            Book
                                        </button>
                                    @elseif($isExpired)
                                        <span class="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium text-red-500 bg-red-100 mr-2">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            Expired
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium text-gray-500 bg-gray-100 mr-2">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                            </svg>
                                            Full
                                        </span>
                                    @endif
                                    <button onclick="viewSeats({{ $trip->id }})" class="text-blue-600 hover:text-blue-900 transition-colors">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        View
                                    </button>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5zM5 14a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1v-3a2 2 0 00-2-2H5z"></path>
                                    </svg>
                                    @if($data['showing_all'])
                                        <p class="text-lg font-medium text-gray-900 mb-2">No trips found</p>
                                        <p class="text-gray-500">No trips have been created in the system yet.</p>
                                    @else
                                        <p class="text-lg font-medium text-gray-900 mb-2">No trips scheduled for today</p>
                                        <p class="text-gray-500">
                                            No trips are scheduled for {{ now()->format('F d, Y') }}.
                                            <button onclick="toggleTripsView('all')" class="text-blue-600 hover:text-blue-800 underline">
                                                View all trips
                                            </button>
                                        </p>
                                    @endif
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Recent Bookings -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="p-6 border-b">
                <h2 class="text-lg font-semibold text-gray-900">📋 Recent Bookings</h2>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ticket ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Passenger</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Route</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Seat</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @forelse($data['recent_bookings'] as $booking)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $booking->id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    @php
                                        $payment = $booking->payments->first();
                                        $passengerName = $payment && $payment->passenger_name ? $payment->passenger_name : $booking->user->name;
                                    @endphp
                                    {{ $passengerName }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $booking->trip->route->start_point }} → {{ $booking->trip->route->end_point }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $booking->seat_number }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @php
                                        $payment = $booking->payments->first();
                                        $paymentStatus = $payment ? $payment->status : 'no_payment';
                                        $statusColors = [
                                            'paid' => 'bg-green-100 text-green-800',
                                            'pending' => 'bg-yellow-100 text-yellow-800',
                                            'cancelled' => 'bg-red-100 text-red-800',
                                            'refunded' => 'bg-gray-100 text-gray-800',
                                            'no_payment' => 'bg-red-100 text-red-800'
                                        ];
                                        $statusText = [
                                            'paid' => 'Confirmed',
                                            'pending' => 'Pending',
                                            'cancelled' => 'Cancelled',
                                            'refunded' => 'Refunded',
                                            'no_payment' => 'No Payment'
                                        ];
                                    @endphp
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $statusColors[$paymentStatus] ?? 'bg-blue-100 text-blue-800' }}">
                                        {{ $statusText[$paymentStatus] ?? 'Confirmed' }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex gap-2 items-center">
                                        @php
                                            $payment = $booking->payments->first();
                                            $paymentStatus = $payment ? $payment->status : 'no_payment';
                                        @endphp

                                        @if($payment && $paymentStatus === 'pending')
                                            <button onclick="completePayment({{ $payment->id }})"
                                                class="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-600 hover:text-orange-900 hover:bg-orange-50 rounded transition-colors">
                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                Complete Payment
                                            </button>
                                        @elseif(!$payment)
                                            <span class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 bg-red-50 rounded">
                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                </svg>
                                                No Payment
                                            </span>
                                        @endif

                                        <button onclick="printTicketById({{ $booking->id }})" class="inline-flex items-center px-2 py-1 text-xs font-medium text-green-600 hover:text-green-900 hover:bg-green-50 rounded transition-colors">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                            </svg>
                                            Print
                                        </button>
                                        <button onclick="viewTicket({{ $booking->id }})" class="inline-flex items-center px-2 py-1 text-xs font-medium text-indigo-600 hover:text-indigo-900 hover:bg-indigo-50 rounded transition-colors">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            View
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <p class="text-lg font-medium text-gray-900 mb-2">No Recent Bookings</p>
                                    <p class="text-sm text-gray-500">Recent ticket bookings will appear here</p>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- Walk-in Booking Modal -->
<div id="walkInModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div class="p-6 border-b">
                <h3 class="text-lg font-semibold text-gray-900">🚶‍♂️ Walk-in Booking</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Select Trip</label>
                        <select id="tripSelect" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Choose a trip...</option>
                            @foreach($data['trips'] as $trip)
                            <option value="{{ $trip->id }}">
                                {{ $trip->route->start_point }} → {{ $trip->route->end_point }}
                                ({{ $trip->schedule->formatted_start_time }})
                            </option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Passenger Name</label>
                        <input type="text" id="passengerName" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Contact Number</label>
                        <input type="tel" id="contactNumber" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Seat Number</label>
                        <input type="number" id="seatNumber" min="1" max="45" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>
            <div class="p-6 border-t flex justify-end space-x-3">
                <button type="button" onclick="closeModal('walkInModal')" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">Cancel</button>
                <button type="button" onclick="processWalkInBooking()" class="px-4 py-2 text-white rounded-lg transition-colors" style="background-color: #FCB404;">Book Ticket</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/agent-dashboard.js') }}"></script>
@endpush
