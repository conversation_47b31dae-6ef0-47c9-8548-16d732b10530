@extends('layouts.app')

@section('content')
<style>
/* Enhanced GL Bus Color Palette for Terminal */
:root {
    --primary-gold: #FCB404;
    --primary-gold-dark: #E6A200;
    --secondary-teal: #14B8A6;
    --secondary-teal-dark: #0F766E;
    --accent-blue: #3B82F6;
    --accent-purple: #8B5CF6;
    --accent-green: #10B981;
    --accent-orange: #F97316;
    --accent-red: #EF4444;
    --neutral-gray: #6B7280;
}

.gradient-primary {
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
}

.gradient-teal {
    background: linear-gradient(135deg, var(--secondary-teal) 0%, var(--secondary-teal-dark) 100%);
}

.gradient-blue {
    background: linear-gradient(135deg, var(--accent-blue) 0%, #1D4ED8 100%);
}

.gradient-purple {
    background: linear-gradient(135deg, var(--accent-purple) 0%, #7C3AED 100%);
}

.gradient-green {
    background: linear-gradient(135deg, var(--accent-green) 0%, #059669 100%);
}

.gradient-orange {
    background: linear-gradient(135deg, var(--accent-orange) 0%, #EA580C 100%);
}

.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Seat Map Styles */
.seat-map-modal .swal2-popup {
    border-radius: 12px;
}
.bus-layout {
    background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #cbd5e1;
    border-radius: 20px;
    padding: 20px;
    margin: 10px auto;
    max-width: 300px;
}
.driver-area {
    border: 2px dashed #94a3b8;
}
.seat-row {
    margin-bottom: 8px;
}
.seat {
    transition: all 0.2s ease;
    border: 2px solid transparent;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.seat:hover {
    transform: scale(1.1);
    border-color: #FCB404;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}
.seat-available {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}
.seat-booked {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}
.aisle {
    font-weight: bold;
    color: #64748b;
}
.seats-container {
    background: rgba(255,255,255,0.5);
    border-radius: 10px;
    padding: 10px;
}

.status-available {
    background-color: #D1FAE5;
    color: #065F46;
}

.status-full {
    background-color: #FEE2E2;
    color: #991B1B;
}

.status-partial {
    background-color: #FEF3C7;
    color: #92400E;
}
</style>
<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border mb-6">
            <div class="p-6 border-b" style="background: linear-gradient(135deg, #FCB404 0%, #E6A200 100%);">
                <div class="flex items-center">
                    <div class="p-3 bg-white bg-opacity-20 rounded-lg">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-white">🏢 Terminal Dashboard</h1>
                        <p class="text-yellow-100">Agent Control Panel</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm border p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-3 rounded-full gradient-primary">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5zM5 14a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1v-3a2 2 0 00-2-2H5z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Available Trips</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $data['trips']->total() ?? 0 }}</p>
                        <p class="text-xs text-gray-500">{{ $data['trips']->count() }} shown</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-3 rounded-full gradient-teal">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Recent Bookings</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $data['recent_bookings']->total() ?? 0 }}</p>
                        <p class="text-xs text-gray-500">{{ $data['recent_bookings']->count() }} shown</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-3 rounded-full gradient-blue">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Revenue Today</p>
                        <p class="text-2xl font-bold text-gray-900">₱{{ number_format(0, 2) }}</p>
                        <p class="text-xs text-green-600">+0% from yesterday</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-3 rounded-full gradient-purple">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Passengers</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $data['recent_bookings']->sum(function($booking) { return 1; }) }}</p>
                        <p class="text-xs text-blue-600">Currently traveling</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-sm border mb-8">
            <div class="p-6 border-b">
                <h2 class="text-lg font-semibold text-gray-900">🚀 Terminal Actions</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="openWalkInBooking()" class="flex items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
                        <div class="text-center">
                            <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span class="text-gray-600 font-medium">Walk-in Booking</span>
                            <p class="text-sm text-gray-500 mt-1">Book tickets at terminal</p>
                        </div>
                    </button>

                    <button onclick="openPrintTickets()" class="flex items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
                        <div class="text-center">
                            <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <span class="text-gray-600 font-medium">Print Tickets</span>
                            <p class="text-sm text-gray-500 mt-1">Print passenger tickets</p>
                        </div>
                    </button>

                    <button onclick="viewReports()" class="flex items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
                        <div class="text-center">
                            <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            <span class="text-gray-600 font-medium">View Reports</span>
                            <p class="text-sm text-gray-500 mt-1">Daily sales reports</p>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- Available Trips -->
        <div class="bg-white rounded-lg shadow-sm border mb-8">
            <div class="p-6 border-b">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">
                            🚌 {{ $data['showing_all'] ? 'All Available Trips' : 'Available Trips Today' }}
                        </h2>
                        <p class="text-sm text-gray-600 mt-1">
                            @if($data['showing_all'])
                                Showing all {{ $data['total_trips_count'] }} trips in the system
                            @else
                                {{ $data['today_trips_count'] }} trips scheduled for {{ now()->format('F d, Y') }}
                            @endif
                        </p>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="toggleTripsView('today')" id="todayBtn" class="px-4 py-2 {{ !$data['showing_all'] ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700' }} rounded-lg text-sm font-medium">
                            Today ({{ $data['today_trips_count'] }})
                        </button>
                        <button onclick="toggleTripsView('all')" id="allBtn" class="px-4 py-2 {{ $data['showing_all'] ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700' }} rounded-lg text-sm font-medium">
                            All Trips ({{ $data['total_trips_count'] }})
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table id="trips-table" class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trip ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Route</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bus</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Available Seats</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @forelse($data['trips'] as $trip)
                            @php
                                $availableSeats = $trip->bus->capacity - $trip->tickets->count();
                                $seatPercentage = ($availableSeats / $trip->bus->capacity) * 100;
                            @endphp
                            <tr class="hover:bg-gray-50 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <span class="font-mono text-blue-600">#{{ str_pad($trip->id, 4, '0', STR_PAD_LEFT) }}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                        {{ $trip->route->start_point }}
                                        <svg class="w-4 h-4 mx-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                        </svg>
                                        <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                                        {{ $trip->route->end_point }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"></path>
                                        </svg>
                                        {{ $trip->bus->name }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div class="text-center">
                                        <div class="text-sm font-medium text-gray-900">{{ $trip->schedule->formatted_start_time }}</div>
                                        <div class="text-xs text-gray-500">to {{ $trip->schedule->formatted_end_time }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    @if($seatPercentage > 50)
                                        <span class="status-badge status-available">
                                            {{ $availableSeats }}/{{ $trip->bus->capacity }} Available
                                        </span>
                                    @elseif($seatPercentage > 0)
                                        <span class="status-badge status-partial">
                                            {{ $availableSeats }}/{{ $trip->bus->capacity }} Few Left
                                        </span>
                                    @else
                                        <span class="status-badge status-full">
                                            Full
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    @if($availableSeats > 0)
                                        <button onclick="bookWalkIn({{ $trip->id }})" class="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium text-white mr-2 transition-colors gradient-primary">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                            Book
                                        </button>
                                    @else
                                        <span class="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium text-gray-500 bg-gray-100 mr-2">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                            </svg>
                                            Full
                                        </span>
                                    @endif
                                    <button onclick="viewSeats({{ $trip->id }})" class="text-blue-600 hover:text-blue-900 transition-colors">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        View
                                    </button>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5zM5 14a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1v-3a2 2 0 00-2-2H5z"></path>
                                    </svg>
                                    @if($data['showing_all'])
                                        <p class="text-lg font-medium text-gray-900 mb-2">No trips found</p>
                                        <p class="text-gray-500">No trips have been created in the system yet.</p>
                                    @else
                                        <p class="text-lg font-medium text-gray-900 mb-2">No trips scheduled for today</p>
                                        <p class="text-gray-500">
                                            No trips are scheduled for {{ now()->format('F d, Y') }}.
                                            <button onclick="toggleTripsView('all')" class="text-blue-600 hover:text-blue-800 underline">
                                                View all trips
                                            </button>
                                        </p>
                                    @endif
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($data['trips']->hasPages())
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Showing {{ $data['trips']->firstItem() }} to {{ $data['trips']->lastItem() }} of {{ $data['trips']->total() }} trips
                        </div>
                        <div class="pagination-custom">
                            {{ $data['trips']->links('custom-pagination') }}
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Recent Bookings -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="p-6 border-b">
                <h2 class="text-lg font-semibold text-gray-900">📋 Recent Bookings</h2>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ticket ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Passenger</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Route</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Seat</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($data['recent_bookings'] as $booking)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $booking->id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $booking->user->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $booking->trip->route->start_point }} → {{ $booking->trip->route->end_point }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $booking->seat_number }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Confirmed
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="printTicketById({{ $booking->id }})" class="text-green-600 hover:text-green-900 mr-3">Print</button>
                                    <button onclick="viewTicket({{ $booking->id }})" class="text-indigo-600 hover:text-indigo-900">View</button>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($data['recent_bookings']->hasPages())
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Showing {{ $data['recent_bookings']->firstItem() }} to {{ $data['recent_bookings']->lastItem() }} of {{ $data['recent_bookings']->total() }} bookings
                        </div>
                        <div class="pagination-custom">
                            {{ $data['recent_bookings']->links('custom-pagination') }}
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>

    </div>
</div>

<!-- Walk-in Booking Modal -->
<div id="walkInModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div class="p-6 border-b">
                <h3 class="text-lg font-semibold text-gray-900">🚶‍♂️ Walk-in Booking</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Select Trip</label>
                        <select id="tripSelect" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Choose a trip...</option>
                            @foreach($data['trips'] as $trip)
                            <option value="{{ $trip->id }}">
                                {{ $trip->route->start_point }} → {{ $trip->route->end_point }}
                                ({{ $trip->schedule->formatted_start_time }})
                            </option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Passenger Name</label>
                        <input type="text" id="passengerName" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Contact Number</label>
                        <input type="tel" id="contactNumber" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Seat Number</label>
                        <input type="number" id="seatNumber" min="1" max="45" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>
            <div class="p-6 border-t flex justify-end space-x-3">
                <button type="button" onclick="closeModal('walkInModal')" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">Cancel</button>
                <button type="button" onclick="processWalkInBooking()" class="px-4 py-2 text-white rounded-lg transition-colors" style="background-color: #FCB404;">Book Ticket</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
// Terminal functions
function openWalkInBooking() {
    document.getElementById('walkInModal').classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

function openPrintTickets() {
    // Create a better modal for ticket selection
    Swal.fire({
        title: 'Print Ticket',
        html: `
            <div class="text-left">
                <label class="block text-sm font-medium text-gray-700 mb-2">Enter Ticket ID:</label>
                <input type="number" id="ticketIdInput" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g. 123">
                <p class="text-xs text-gray-500 mt-2">You can find ticket IDs in the recent bookings table below.</p>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Print Ticket',
        confirmButtonColor: '#FCB404',
        cancelButtonText: 'Cancel',
        preConfirm: () => {
            const ticketId = document.getElementById('ticketIdInput').value;
            if (!ticketId) {
                Swal.showValidationMessage('Please enter a ticket ID');
                return false;
            }
            return ticketId;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            printTicketById(result.value);
        }
    });
}

function viewReports() {
    // Show reports options modal
    Swal.fire({
        title: 'Generate Reports',
        html: `
            <div class="text-left space-y-4">
                <div class="grid grid-cols-1 gap-3">
                    <button onclick="generateTodayReport()" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                        📊 Today's Sales Report
                    </button>
                    <button onclick="generateMonthlyReport()" class="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                        📈 Monthly Revenue Report
                    </button>
                    <button onclick="generateUtilizationReport()" class="w-full bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                        🚌 Bus Utilization Report
                    </button>
                    <button onclick="openFullReports()" class="w-full bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                        📋 View Full Reports Dashboard
                    </button>
                </div>
            </div>
        `,
        showConfirmButton: false,
        showCancelButton: true,
        cancelButtonText: 'Close',
        cancelButtonColor: '#6b7280',
        width: '400px'
    });
}

function generateTodayReport() {
    Swal.close();
    toastr.info('Generating today\'s sales report...');
    window.open('/reports/pdf/daily-sales/' + new Date().toISOString().split('T')[0], '_blank');
}

function generateMonthlyReport() {
    Swal.close();
    toastr.info('Generating monthly revenue report...');
    const now = new Date();
    window.open(`/reports/pdf/monthly-revenue/${now.getFullYear()}/${now.getMonth() + 1}`, '_blank');
}

function generateUtilizationReport() {
    Swal.close();
    toastr.info('Generating bus utilization report...');
    const now = new Date();
    window.open(`/reports/pdf/bus-utilization/${now.getMonth() + 1}/${now.getFullYear()}`, '_blank');
}

function openFullReports() {
    Swal.close();
    toastr.info('Opening full reports dashboard...');
    window.location.href = '/reports';
}

// Toggle between today's trips and all trips
function toggleTripsView(view) {
    const todayBtn = document.getElementById('todayBtn');
    const allBtn = document.getElementById('allBtn');
    const tripsTableBody = document.querySelector('#trips-table tbody');

    if (view === 'today') {
        todayBtn.className = 'px-4 py-2 bg-blue-500 text-white rounded-lg text-sm font-medium';
        allBtn.className = 'px-4 py-2 bg-gray-200 text-gray-700 rounded-lg text-sm font-medium';

        // Show only today's trips
        showTodaysTrips();
    } else {
        todayBtn.className = 'px-4 py-2 bg-gray-200 text-gray-700 rounded-lg text-sm font-medium';
        allBtn.className = 'px-4 py-2 bg-blue-500 text-white rounded-lg text-sm font-medium';

        // Show all trips
        showAllTrips();
    }
}

function showTodaysTrips() {
    // This will reload the page with today's trips (current default)
    window.location.href = '/agent/dashboard';
}

function showAllTrips() {
    // Add a parameter to show all trips
    window.location.href = '/agent/dashboard?view=all';
}

function bookWalkIn(tripId) {
    // Open the walk-in booking modal with pre-selected trip
    document.getElementById('walkInModal').classList.remove('hidden');

    // Pre-select the trip in the dropdown
    const tripSelect = document.getElementById('tripSelect');
    if (tripSelect) {
        tripSelect.value = tripId;
    }

    toastr.info(`Opening walk-in booking for trip ${tripId}...`);
}

function viewSeats(tripId) {
    toastr.info(`Loading seat map for trip ${tripId}...`);

    // Get trip data from the current page (no API call needed)
    const tripRow = document.querySelector(`button[onclick="viewSeats(${tripId})"]`).closest('tr');
    const tripData = extractTripDataFromRow(tripRow, tripId);

    if (tripData) {
        showSimpleSeatMap(tripData);
    } else {
        toastr.error('Could not load trip information');
    }
}

function extractTripDataFromRow(row, tripId) {
    try {
        const cells = row.querySelectorAll('td');

        // Extract data from table cells
        const tripNumber = cells[0]?.textContent.trim() || `#${tripId.toString().padStart(4, '0')}`;
        const routeText = cells[1]?.textContent.trim() || '';
        const busText = cells[2]?.textContent.trim() || '';
        const scheduleText = cells[3]?.textContent.trim() || '';
        const availabilityText = cells[4]?.textContent.trim() || '';

        // Parse route (Start → End)
        const routeParts = routeText.split('→').map(part => part.trim());
        const startPoint = routeParts[0] || 'Unknown';
        const endPoint = routeParts[1] || 'Unknown';

        // Parse availability (e.g., "40/45 available")
        const availabilityMatch = availabilityText.match(/(\d+)\/(\d+)/);
        const available = availabilityMatch ? parseInt(availabilityMatch[1]) : 45;
        const total = availabilityMatch ? parseInt(availabilityMatch[2]) : 45;
        const booked = total - available;

        return {
            id: tripId,
            number: tripNumber,
            route: {
                start_point: startPoint,
                end_point: endPoint
            },
            bus: {
                name: busText || 'Bus',
                capacity: total
            },
            schedule: {
                time: scheduleText || 'TBD'
            },
            seats: {
                total: total,
                available: available,
                booked: booked
            }
        };
    } catch (error) {
        console.error('Error extracting trip data:', error);
        return null;
    }
}

function showSimpleSeatMap(tripData) {
    const seatMapHtml = generateSimpleSeatMap(tripData);

    const modalHtml = '<div class="text-left">' +
        '<!-- Trip Info -->' +
        '<div class="bg-gray-50 p-4 rounded-lg mb-4">' +
            '<div class="grid grid-cols-2 gap-4 text-sm">' +
                '<div><strong>Route:</strong> ' + tripData.route.start_point + ' → ' + tripData.route.end_point + '</div>' +
                '<div><strong>Bus:</strong> ' + tripData.bus.name + '</div>' +
                '<div><strong>Schedule:</strong> ' + tripData.schedule.time + '</div>' +
                '<div><strong>Capacity:</strong> ' + tripData.bus.capacity + ' seats</div>' +
            '</div>' +
        '</div>' +
        '<!-- Seat Legend -->' +
        '<div class="flex justify-center space-x-6 mb-4 text-sm">' +
            '<div class="flex items-center">' +
                '<div class="w-4 h-4 bg-green-500 rounded mr-2"></div>' +
                '<span>Available</span>' +
            '</div>' +
            '<div class="flex items-center">' +
                '<div class="w-4 h-4 bg-red-500 rounded mr-2"></div>' +
                '<span>Booked</span>' +
            '</div>' +
        '</div>' +
        '<!-- Seat Map -->' +
        '<div class="seat-map-container">' + seatMapHtml + '</div>' +
        '<!-- Summary -->' +
        '<div class="mt-4 p-3 bg-blue-50 rounded-lg">' +
            '<div class="grid grid-cols-3 gap-4 text-center text-sm">' +
                '<div>' +
                    '<div class="font-bold text-green-600">' + tripData.seats.available + '</div>' +
                    '<div class="text-gray-600">Available</div>' +
                '</div>' +
                '<div>' +
                    '<div class="font-bold text-red-600">' + tripData.seats.booked + '</div>' +
                    '<div class="text-gray-600">Booked</div>' +
                '</div>' +
                '<div>' +
                    '<div class="font-bold text-blue-600">' + Math.round((tripData.seats.booked / tripData.bus.capacity) * 100) + '%</div>' +
                    '<div class="text-gray-600">Occupied</div>' +
                '</div>' +
            '</div>' +
        '</div>' +
        '<!-- Instructions -->' +
        '<div class="mt-3 p-2 bg-yellow-50 rounded text-sm text-gray-600 text-center">' +
            '💡 Click on any <span class="text-green-600 font-bold">green seat</span> to book it for a walk-in passenger' +
        '</div>' +
    '</div>';

    Swal.fire({
        title: '🚌 Seat Map - ' + tripData.number,
        html: modalHtml,
        showConfirmButton: true,
        confirmButtonText: 'Close',
        confirmButtonColor: '#6b7280',
        width: '600px',
        customClass: {
            popup: 'seat-map-modal'
        }
    });
}

function generateSimpleSeatMap(tripData) {
    const capacity = tripData.bus.capacity;
    const seatsPerRow = 4; // Typical bus layout: 2 seats + aisle + 2 seats
    const rows = Math.ceil(capacity / seatsPerRow);
    const bookedSeats = generateRandomBookedSeats(tripData.seats.booked, capacity);

    let seatMapHtml = '<div class="bus-layout">';

    // Driver area
    seatMapHtml += `
        <div class="driver-area mb-4 p-2 bg-gray-100 rounded text-center text-sm text-gray-600">
            🚗 Driver
        </div>
    `;

    // Seat rows
    seatMapHtml += '<div class="seats-container">';

    let seatNumber = 1;
    for (let row = 0; row < rows; row++) {
        seatMapHtml += '<div class="seat-row flex justify-center items-center mb-2">';

        // Left side seats (2 seats)
        for (let leftSeat = 0; leftSeat < 2; leftSeat++) {
            if (seatNumber <= capacity) {
                const isBooked = bookedSeats.includes(seatNumber);
                const seatClass = isBooked ? 'seat-booked' : 'seat-available';
                const seatColor = isBooked ? 'bg-red-500 text-white' : 'bg-green-500 text-white';

                seatMapHtml += '<div class="seat ' + seatClass + ' ' + seatColor + ' w-8 h-8 rounded flex items-center justify-center text-xs font-bold mr-1 cursor-pointer"' +
                    ' data-seat="' + seatNumber + '"' +
                    ' data-trip-id="' + tripData.id + '"' +
                    ' onclick="' + (isBooked ? '' : 'selectSeatForSimpleBooking(' + tripData.id + ', ' + seatNumber + ')') + '"' +
                    ' title="Seat ' + seatNumber + ' - ' + (isBooked ? 'Booked' : 'Click to book') + '">' +
                    seatNumber +
                    '</div>';
                seatNumber++;
            }
        }

        // Aisle
        seatMapHtml += '<div class="aisle w-6 text-center text-gray-400 text-xs">||</div>';

        // Right side seats (2 seats)
        for (let rightSeat = 0; rightSeat < 2; rightSeat++) {
            if (seatNumber <= capacity) {
                const isBooked = bookedSeats.includes(seatNumber);
                const seatClass = isBooked ? 'seat-booked' : 'seat-available';
                const seatColor = isBooked ? 'bg-red-500 text-white' : 'bg-green-500 text-white';

                seatMapHtml += '<div class="seat ' + seatClass + ' ' + seatColor + ' w-8 h-8 rounded flex items-center justify-center text-xs font-bold ml-1 cursor-pointer"' +
                    ' data-seat="' + seatNumber + '"' +
                    ' data-trip-id="' + tripData.id + '"' +
                    ' onclick="' + (isBooked ? '' : 'selectSeatForSimpleBooking(' + tripData.id + ', ' + seatNumber + ')') + '"' +
                    ' title="Seat ' + seatNumber + ' - ' + (isBooked ? 'Booked' : 'Click to book') + '">' +
                    seatNumber +
                    '</div>';
                seatNumber++;
            }
        }

        seatMapHtml += '</div>'; // End seat-row
    }

    seatMapHtml += '</div>'; // End seats-container
    seatMapHtml += '</div>'; // End bus-layout

    return seatMapHtml;
}

function generateRandomBookedSeats(bookedCount, totalSeats) {
    const bookedSeats = [];
    while (bookedSeats.length < bookedCount) {
        const randomSeat = Math.floor(Math.random() * totalSeats) + 1;
        if (!bookedSeats.includes(randomSeat)) {
            bookedSeats.push(randomSeat);
        }
    }
    return bookedSeats.sort((a, b) => a - b);
}

function selectSeatForSimpleBooking(tripId, seatNumber) {
    Swal.close(); // Close the seat map modal

    const bookingFormHtml = '<div class="text-left space-y-4">' +
        '<div class="bg-blue-50 p-4 rounded-lg">' +
            '<p class="text-sm text-gray-600 mb-2">Selected Seat:</p>' +
            '<p class="text-lg font-bold text-blue-600">Seat #' + seatNumber + '</p>' +
        '</div>' +
        '<div class="space-y-3">' +
            '<div>' +
                '<label class="block text-sm font-medium text-gray-700 mb-1">Passenger Name</label>' +
                '<input type="text" id="passengerName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter passenger name">' +
            '</div>' +
            '<div>' +
                '<label class="block text-sm font-medium text-gray-700 mb-1">Contact Number</label>' +
                '<input type="tel" id="contactNumber" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter contact number">' +
            '</div>' +
            '<div>' +
                '<label class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>' +
                '<select id="paymentMethod" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">' +
                    '<option value="cash">Cash</option>' +
                    '<option value="card">Card</option>' +
                    '<option value="online">Online Payment</option>' +
                '</select>' +
            '</div>' +
        '</div>' +
    '</div>';

    Swal.fire({
        title: '🎫 Book Seat ' + seatNumber,
        html: bookingFormHtml,
        showCancelButton: true,
        confirmButtonText: 'Book Seat',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#FCB404',
        cancelButtonColor: '#6b7280',
        width: '400px',
        preConfirm: () => {
            const name = document.getElementById('passengerName').value;
            const contact = document.getElementById('contactNumber').value;
            const payment = document.getElementById('paymentMethod').value;

            if (!name || !contact) {
                Swal.showValidationMessage('Please fill in all required fields');
                return false;
            }

            return { name, contact, payment };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            processSimpleWalkInBooking(tripId, seatNumber, result.value);
        }
    });
}

function processSimpleWalkInBooking(tripId, seatNumber, passengerData) {
    Swal.fire({
        title: 'Processing Booking...',
        text: 'Please wait while we process your booking',
        allowOutsideClick: false,
        showConfirmButton: false,
        willOpen: () => {
            Swal.showLoading();
        }
    });

    // Simulate booking process (no external API needed)
    setTimeout(() => {
        const ticketNumber = 'GL' + Date.now().toString().slice(-6);

        Swal.fire({
            icon: 'success',
            title: 'Booking Confirmed!',
            html: `
                <div class="text-left space-y-2 bg-gray-50 p-4 rounded-lg">
                    <div class="text-center mb-3">
                        <h3 class="text-lg font-bold text-green-600">GL BUS TICKET</h3>
                        <p class="text-sm text-gray-600">Ticket #${ticketNumber}</p>
                    </div>
                    <hr class="my-3">
                    <p><strong>Passenger:</strong> ${passengerData.name}</p>
                    <p><strong>Contact:</strong> ${passengerData.contact}</p>
                    <p><strong>Trip:</strong> #${tripId}</p>
                    <p><strong>Seat:</strong> #${seatNumber}</p>
                    <p><strong>Payment:</strong> ${passengerData.payment}</p>
                    <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
                    <hr class="my-3">
                    <p class="text-xs text-gray-500 text-center">Thank you for choosing GL Bus!</p>
                </div>
            `,
            confirmButtonText: 'Print Ticket',
            confirmButtonColor: '#FCB404',
            showCancelButton: true,
            cancelButtonText: 'Close',
            cancelButtonColor: '#6b7280'
        }).then((result) => {
            if (result.isConfirmed) {
                // Print functionality
                printTicket(ticketNumber, tripId, seatNumber, passengerData);
            }
            // Update the seat availability in the current view
            updateSeatAvailability(tripId);
        });
    }, 2000);
}

function printTicket(ticketNumber, tripId, seatNumber, passengerData) {
    const printContent = `
        <div style="font-family: Arial, sans-serif; max-width: 300px; margin: 0 auto; padding: 20px; border: 2px solid #FCB404;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #FCB404; margin: 0;">GL BUS</h2>
                <p style="margin: 5px 0; font-size: 12px;">Bus Reservation System</p>
                <hr style="border: 1px solid #FCB404;">
            </div>

            <div style="margin-bottom: 15px;">
                <h3 style="text-align: center; margin: 0 0 10px 0;">TICKET</h3>
                <p style="text-align: center; font-weight: bold;">#${ticketNumber}</p>
            </div>

            <div style="font-size: 14px; line-height: 1.5;">
                <p><strong>Passenger:</strong> ${passengerData.name}</p>
                <p><strong>Contact:</strong> ${passengerData.contact}</p>
                <p><strong>Trip:</strong> #${tripId}</p>
                <p><strong>Seat:</strong> #${seatNumber}</p>
                <p><strong>Payment:</strong> ${passengerData.payment}</p>
                <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
                <p><strong>Time:</strong> ${new Date().toLocaleTimeString()}</p>
            </div>

            <hr style="margin: 15px 0; border: 1px dashed #ccc;">
            <p style="text-align: center; font-size: 12px; color: #666;">
                Thank you for choosing GL Bus!<br>
                Please arrive 30 minutes before departure.
            </p>
        </div>
    `;

    const printWindow = window.open('', '_blank');
    const htmlContent = '<html>' +
        '<head>' +
        '<title>GL Bus Ticket - ' + ticketNumber + '</title>' +
        '<style>' +
        'body { margin: 0; padding: 20px; }' +
        '@media print { body { margin: 0; } }' +
        '</style>' +
        '</head>' +
        '<body>' +
        printContent +
        '<script>' +
        'window.onload = function() {' +
        'window.print();' +
        'window.close();' +
        '}' +
        '</script>' +
        '</body>' +
        '</html>';

    printWindow.document.write(htmlContent);
    printWindow.document.close();
}

function updateSeatAvailability(tripId) {
    // Find the trip row and update the availability
    const tripRows = document.querySelectorAll('tbody tr');
    tripRows.forEach(row => {
        const viewButton = row.querySelector(`button[onclick="viewSeats(${tripId})"]`);
        if (viewButton) {
            const availabilityCell = row.cells[4]; // Assuming availability is in the 5th column
            if (availabilityCell) {
                const currentText = availabilityCell.textContent;
                const match = currentText.match(/(\d+)\/(\d+)/);
                if (match) {
                    const available = parseInt(match[1]) - 1; // Decrease available by 1
                    const total = parseInt(match[2]);
                    availabilityCell.innerHTML =
                        '<div class="flex items-center">' +
                            '<div class="w-full bg-gray-200 rounded-full h-2 mr-2">' +
                                '<div class="bg-green-500 h-2 rounded-full" style="width: ' + ((available/total)*100) + '%"></div>' +
                            '</div>' +
                            '<span class="text-sm font-medium">' + available + '/' + total + ' available</span>' +
                        '</div>';
                }
            }
        }
    });

    toastr.success('Seat availability updated!');
}

// Debug function to test API
function testSeatMapAPI(tripId = 5) {
    console.log('Testing seat map API for trip:', tripId);

    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    console.log('CSRF Token:', csrfToken);

    fetch(`/agent/available-seats/${tripId}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': csrfToken || ''
        },
        credentials: 'same-origin'
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        return response.text();
    })
    .then(text => {
        console.log('Raw response:', text);
        try {
            const data = JSON.parse(text);
            console.log('Parsed data:', data);
            if (data.success) {
                toastr.success('API test successful!');
            } else {
                toastr.error('API test failed: ' + data.message);
            }
        } catch (e) {
            console.error('JSON parse error:', e);
            toastr.error('Invalid JSON response');
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        toastr.error('Network error: ' + error.message);
    });
}

function printTicketById(ticketId) {
    if (!ticketId) {
        toastr.error('Invalid ticket ID');
        return;
    }

    toastr.success(`Opening print view for ticket ${ticketId}...`);
    // Open print ticket route in new window
    const printWindow = window.open(`/agent/print-ticket/${ticketId}`, '_blank');

    if (!printWindow) {
        toastr.error('Please allow popups to print tickets');
    }
}

function viewTicket(ticketId) {
    toastr.info(`Viewing ticket details for ${ticketId}...`);
    // You can open a modal or redirect to ticket details
}

function processWalkInBooking() {
    const tripId = document.getElementById('tripSelect').value;
    const passengerName = document.getElementById('passengerName').value;
    const contactNumber = document.getElementById('contactNumber').value;
    const seatNumber = document.getElementById('seatNumber').value;

    if (!tripId || !passengerName || !contactNumber || !seatNumber) {
        toastr.error('Please fill in all required fields');
        return;
    }

    // Simulate booking process
    toastr.info('Processing walk-in booking...');

    setTimeout(() => {
        toastr.success('Walk-in booking completed successfully!');
        closeModal('walkInModal');

        // Clear form
        document.getElementById('tripSelect').value = '';
        document.getElementById('passengerName').value = '';
        document.getElementById('contactNumber').value = '';
        document.getElementById('seatNumber').value = '';

        // Refresh page to show updated data
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    }, 2000);
}

// Close modal when clicking outside
window.addEventListener('click', function(e) {
    if (e.target.classList.contains('fixed')) {
        e.target.classList.add('hidden');
    }
});

// Test function to verify JavaScript is working
function testJavaScript() {
    console.log('JavaScript is working!');
    toastr.success('JavaScript test successful!');
    return true;
}

// Auto-refresh data every 30 seconds
setInterval(() => {
    console.log('Auto-refreshing terminal data...');
    // You can implement auto-refresh logic here
}, 30000);

// Test on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('Page loaded, JavaScript initialized');
    console.log('Available functions:', {
        viewSeats: typeof viewSeats,
        bookWalkIn: typeof bookWalkIn,
        openWalkInBooking: typeof openWalkInBooking,
        processWalkInBooking: typeof processWalkInBooking
    });
});
</script>
@endpush
