<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Walk-in Booking Terminal</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Toastr for toast notifications -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <style>
        :root {
            --primary-gold: #FCB404;
            --primary-gold-dark: #E6A200;
            --secondary-teal: #14B8A6;
            --secondary-teal-dark: #0F766E;
            --accent-blue: #3B82F6;
            --accent-purple: #8B5CF6;
            --accent-green: #10B981;
            --accent-orange: #F97316;
            --accent-red: #EF4444;
            --neutral-gray: #6B7280;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-gold) 0%, var(--secondary-teal) 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-gold-dark);
        }

        .back-btn {
            background: var(--neutral-gray);
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s;
        }

        .back-btn:hover {
            background: #374151;
            transform: translateY(-1px);
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .booking-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 0.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .tab {
            flex: 1;
            padding: 1rem;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .tab.active {
            background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(252, 180, 4, 0.4);
        }

        .tab:not(.active):hover {
            background: rgba(252, 180, 4, 0.1);
        }
        
        .search-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            display: none;
        }
        
        .search-panel.active {
            display: block;
        }
        
        .search-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #374151;
        }
        
        .form-group input,
        .form-group select {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-gold);
            box-shadow: 0 0 0 3px rgba(252, 180, 4, 0.1);
        }

        .search-btn {
            background: linear-gradient(135deg, var(--secondary-teal) 0%, var(--secondary-teal-dark) 100%);
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .search-btn:hover {
            background: linear-gradient(135deg, var(--secondary-teal-dark) 0%, #0D9488 100%);
            transform: translateY(-1px);
        }
        
        .trips-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .trips-header {
            background: #f8fafc;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 600;
            color: #374151;
        }
        
        .trip-item {
            display: grid;
            grid-template-columns: 2fr 1.5fr 1fr 1fr 1fr 120px;
            gap: 1rem;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            align-items: center;
            transition: all 0.3s;
        }
        
        .trip-item:hover {
            background: #f8fafc;
        }
        
        .trip-route {
            font-weight: 600;
            color: #1f2937;
        }
        
        .trip-bus {
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .trip-time {
            font-weight: 500;
            color: #374151;
        }
        
        .trip-seats {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .seats-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .seats-available {
            background: rgba(16, 185, 129, 0.1);
            color: var(--accent-green);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .seats-limited {
            background: rgba(252, 180, 4, 0.1);
            color: var(--primary-gold-dark);
            border: 1px solid rgba(252, 180, 4, 0.3);
        }

        .seats-full {
            background: rgba(239, 68, 68, 0.1);
            color: var(--accent-red);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .trip-fare {
            font-weight: 600;
            color: var(--accent-green);
            font-size: 1.1rem;
        }

        .book-btn {
            background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .book-btn:hover {
            background: linear-gradient(135deg, var(--primary-gold-dark) 0%, #D97706 100%);
            transform: translateY(-1px);
        }

        .book-btn:disabled {
            background: var(--neutral-gray);
            cursor: not-allowed;
            transform: none;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .modal.active {
            display: flex;
        }
        
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        
        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
            padding: 0.5rem;
            border-radius: 6px;
            transition: all 0.3s;
        }
        
        .close-btn:hover {
            background: #f3f4f6;
            color: #374151;
        }
        
        .modal-body {
            padding: 1.5rem;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #6b7280;
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: var(--primary-gold);
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #6b7280;
        }
        
        .empty-state svg {
            width: 64px;
            height: 64px;
            margin: 0 auto 1rem;
            opacity: 0.5;
        }
        
        @media (max-width: 768px) {
            .trip-item {
                grid-template-columns: 1fr;
                gap: 0.5rem;
                text-align: left;
            }
            
            .search-grid {
                grid-template-columns: 1fr;
            }
            
            .booking-tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="logo">🎫 Walk-in Booking Terminal</div>
            <a href="{{ route('agent.dashboard') }}" class="back-btn">← Dashboard</a>
        </div>
    </div>

    <div class="container">
        <!-- Booking Type Tabs -->
        <div class="booking-tabs">
            <div class="tab active" data-type="today" onclick="switchTab('today')">
                <div>📅 Today's Trips</div>
                <small>Available now</small>
            </div>
            <div class="tab" data-type="advanced" onclick="switchTab('advanced')">
                <div>🔍 Advanced Search</div>
                <small>Future dates</small>
            </div>
        </div>

        <!-- Advanced Search Panel -->
        <div class="search-panel" id="advanced-search">
            <div class="search-grid">
                <div class="form-group">
                    <label>Travel Date</label>
                    <input type="date" id="travel-date" min="{{ date('Y-m-d') }}">
                </div>
                <div class="form-group">
                    <label>From</label>
                    <select id="origin-select">
                        <option value="">Select Origin</option>
                        <option value="Tabuk">Tabuk</option>
                        <option value="Baguio">Baguio</option>
                        <option value="Manila">Manila</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>To</label>
                    <select id="destination-select">
                        <option value="">Select Destination</option>
                        <option value="Tabuk">Tabuk</option>
                        <option value="Baguio">Baguio</option>
                        <option value="Manila">Manila</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>&nbsp;</label>
                    <button class="search-btn" onclick="searchTrips()">🔍 Search</button>
                </div>
            </div>
        </div>

        <!-- Trips List -->
        <div class="trips-container">
            <div class="trips-header">
                <span id="trips-title">Today's Available Trips</span>
            </div>
            <div id="trips-list">
                <div class="loading">
                    <div class="spinner"></div>
                    <div>Loading trips...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Booking Modal -->
    <div class="modal" id="booking-modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">Book Trip</div>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body" id="modal-content">
                <!-- Modal content will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        // Configure toastr
        if (typeof toastr !== 'undefined') {
            toastr.options = {
                "closeButton": true,
                "debug": false,
                "newestOnTop": true,
                "progressBar": true,
                "positionClass": "toast-top-right",
                "preventDuplicates": false,
                "onclick": null,
                "showDuration": "300",
                "hideDuration": "1000",
                "timeOut": "5000",
                "extendedTimeOut": "1000",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut"
            };
        }

        let currentTab = 'today';
        let selectedTrip = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadTodaysTrips();
        });
        
        function switchTab(type) {
            currentTab = type;
            
            // Update tab appearance
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('active');
            
            // Show/hide search panel
            const searchPanel = document.getElementById('advanced-search');
            if (type === 'advanced') {
                searchPanel.classList.add('active');
                document.getElementById('trips-title').textContent = 'Search Results';
                showEmptyState('Select date and route to search trips');
            } else {
                searchPanel.classList.remove('active');
                document.getElementById('trips-title').textContent = "Today's Available Trips";
                loadTodaysTrips();
            }
        }
        
        function loadTodaysTrips() {
            showLoading();
            
            fetch('/api/agent/todays-trips')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayTrips(data.trips);
                    } else {
                        showEmptyState('No trips available today');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showEmptyState('Error loading trips');
                });
        }
        
        function searchTrips() {
            const date = document.getElementById('travel-date').value;
            const origin = document.getElementById('origin-select').value;
            const destination = document.getElementById('destination-select').value;
            
            if (!date || !origin || !destination) {
                toastr.warning('Please fill in all search fields');
                return;
            }

            if (origin === destination) {
                toastr.warning('Origin and destination must be different');
                return;
            }
            
            showLoading();
            
            fetch(`/api/agent/search-trips?date=${date}&origin=${origin}&destination=${destination}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayTrips(data.trips);
                        document.getElementById('trips-title').textContent = `${origin} → ${destination} (${date})`;
                    } else {
                        showEmptyState('No trips found for selected criteria');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showEmptyState('Error searching trips');
                });
        }
        
        function displayTrips(trips) {
            const container = document.getElementById('trips-list');
            
            if (!trips || trips.length === 0) {
                showEmptyState('No trips available');
                return;
            }
            
            let html = '';
            trips.forEach(trip => {
                const available = trip.total_seats - trip.booked_seats;
                const seatClass = available > 20 ? 'seats-available' : available > 0 ? 'seats-limited' : 'seats-full';
                const seatText = available > 0 ? `${available} available` : 'Full';
                
                html += `
                    <div class="trip-item">
                        <div>
                            <div class="trip-route">${trip.route.start_point} → ${trip.route.end_point}</div>
                            <div class="trip-bus">${trip.bus.name} (${trip.bus.bus_code})</div>
                        </div>
                        <div class="trip-time">${trip.schedule.start_time}</div>
                        <div class="trip-seats">
                            <span class="seats-badge ${seatClass}">${seatText}</span>
                        </div>
                        <div class="trip-fare">₱${trip.fare}</div>
                        <div>
                            <button class="book-btn" ${available === 0 ? 'disabled' : ''} 
                                    onclick="openBookingModal(${trip.id})">
                                ${available === 0 ? 'Full' : 'Book'}
                            </button>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function showLoading() {
            document.getElementById('trips-list').innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <div>Loading trips...</div>
                </div>
            `;
        }
        
        function showEmptyState(message) {
            document.getElementById('trips-list').innerHTML = `
                <div class="empty-state">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <div>${message}</div>
                </div>
            `;
        }
        
        function openBookingModal(tripId) {
            selectedTrip = tripId;
            document.getElementById('booking-modal').classList.add('active');
            loadBookingForm();
        }
        
        function closeModal() {
            document.getElementById('booking-modal').classList.remove('active');
            selectedTrip = null;
        }
        
        function loadBookingForm() {
            document.getElementById('modal-content').innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <div>Loading booking form...</div>
                </div>
            `;
            
            // Load trip details and show booking form
            fetch(`/api/agent/trip-details/${selectedTrip}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showBookingForm(data.trip);
                    } else {
                        throw new Error(data.message);
                    }
                })
                .catch(error => {
                    document.getElementById('modal-content').innerHTML = `
                        <div style="text-align: center; color: #ef4444; padding: 2rem;">
                            Error loading trip details: ${error.message}
                        </div>
                    `;
                });
        }
        
        function showBookingForm(trip) {
            document.getElementById('modal-content').innerHTML = `
                <div style="margin-bottom: 1.5rem; padding: 1rem; background: #f8fafc; border-radius: 8px;">
                    <h3 style="margin-bottom: 0.5rem; color: #1f2937;">${trip.route.start_point} → ${trip.route.end_point}</h3>
                    <p style="color: #6b7280; margin-bottom: 0.5rem;">${trip.bus.name} (${trip.bus.bus_code})</p>
                    <p style="color: #6b7280;">Departure: ${trip.schedule.start_time} | Fare: ₱${trip.fare}</p>
                </div>
                
                <form id="booking-form" style="display: grid; gap: 1rem;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label>First Name *</label>
                            <input type="text" id="first-name" required>
                        </div>
                        <div class="form-group">
                            <label>Last Name *</label>
                            <input type="text" id="last-name" required>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label>Mobile Number *</label>
                            <input type="tel" id="mobile" required>
                        </div>
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" id="email">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Address *</label>
                        <input type="text" id="address" required>
                    </div>
                    
                    <div class="form-group">
                        <label>Payment Method</label>
                        <select id="payment-method">
                            <option value="cash">💵 Cash</option>
                            <option value="gcash">📱 GCash</option>
                            <option value="maya">💳 Maya</option>
                        </select>
                    </div>
                    
                    <button type="button" onclick="proceedToSeats()"
                            style="background: linear-gradient(135deg, #FCB404 0%, #E6A200 100%); color: white; padding: 1rem; border: none; border-radius: 8px; font-weight: 500; cursor: pointer; margin-top: 1rem;">
                        Continue to Seat Selection
                    </button>
                </form>
            `;
        }
        
        function proceedToSeats() {
            // Validate form
            const firstName = document.getElementById('first-name').value.trim();
            const lastName = document.getElementById('last-name').value.trim();
            const mobile = document.getElementById('mobile').value.trim();
            const address = document.getElementById('address').value.trim();
            
            if (!firstName || !lastName || !mobile || !address) {
                toastr.warning('Please fill in all required fields');
                return;
            }
            
            // Load seat selection
            loadSeatSelection();
        }
        
        function loadSeatSelection() {
            document.getElementById('modal-content').innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <div>Loading seat map...</div>
                </div>
            `;
            
            fetch(`/api/agent/trip-seats/${selectedTrip}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSeatSelection(data.seats, data.bookedSeats);
                    } else {
                        throw new Error(data.message);
                    }
                })
                .catch(error => {
                    document.getElementById('modal-content').innerHTML = `
                        <div style="text-align: center; color: #ef4444; padding: 2rem;">
                            Error loading seats: ${error.message}
                        </div>
                    `;
                });
        }
        
        let selectedSeats = [];
        
        function showSeatSelection(totalSeats, bookedSeats) {
            let seatMap = '<div style="text-align: center; margin-bottom: 1rem;"><h3>Select Seats</h3></div>';
            seatMap += '<div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 0.5rem; max-width: 300px; margin: 0 auto 2rem;">';
            
            for (let i = 1; i <= totalSeats; i++) {
                const isBooked = bookedSeats.includes(i.toString());
                const seatClass = isBooked ? 'booked' : 'available';
                const seatStyle = isBooked
                    ? 'background: #EF4444; color: white; cursor: not-allowed;'
                    : 'background: #14B8A6; color: white; cursor: pointer;';
                
                seatMap += `
                    <div onclick="${isBooked ? '' : 'toggleSeat(' + i + ', this)'}" 
                         style="padding: 0.75rem; border-radius: 6px; text-align: center; font-weight: bold; transition: all 0.3s; ${seatStyle}"
                         data-seat="${i}">
                        ${i}
                    </div>
                `;
            }
            
            seatMap += '</div>';
            
            seatMap += `
                <div style="display: flex; justify-content: center; gap: 1rem; margin-bottom: 2rem; font-size: 0.9rem;">
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <div style="width: 20px; height: 20px; background: #14B8A6; border-radius: 4px;"></div>
                        <span>Available</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <div style="width: 20px; height: 20px; background: #FCB404; border-radius: 4px;"></div>
                        <span>Selected</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <div style="width: 20px; height: 20px; background: #EF4444; border-radius: 4px;"></div>
                        <span>Booked</span>
                    </div>
                </div>
                
                <div style="text-align: center;">
                    <div id="selected-seats-info" style="margin-bottom: 1rem; color: #6b7280;">No seats selected</div>
                    <button onclick="completeBooking()" id="complete-btn" disabled
                            style="background: linear-gradient(135deg, #14B8A6 0%, #0F766E 100%); color: white; padding: 1rem 2rem; border: none; border-radius: 8px; font-weight: 500; cursor: pointer;">
                        Complete Booking
                    </button>
                </div>
            `;
            
            document.getElementById('modal-content').innerHTML = seatMap;
        }
        
        function toggleSeat(seatNumber, element) {
            if (selectedSeats.includes(seatNumber)) {
                // Deselect
                selectedSeats = selectedSeats.filter(s => s !== seatNumber);
                element.style.background = '#14B8A6';
            } else {
                // Select
                selectedSeats.push(seatNumber);
                element.style.background = '#FCB404';
            }
            
            updateSeatInfo();
        }
        
        function updateSeatInfo() {
            const info = document.getElementById('selected-seats-info');
            const btn = document.getElementById('complete-btn');
            
            if (selectedSeats.length === 0) {
                info.textContent = 'No seats selected';
                btn.disabled = true;
                btn.style.background = '#6B7280';
            } else {
                info.textContent = `Selected seats: ${selectedSeats.join(', ')} (${selectedSeats.length} seat${selectedSeats.length > 1 ? 's' : ''})`;
                btn.disabled = false;
                btn.style.background = 'linear-gradient(135deg, #14B8A6 0%, #0F766E 100%)';
            }
        }
        
        function completeBooking() {
            if (selectedSeats.length === 0) {
                toastr.warning('Please select at least one seat');
                return;
            }
            
            const bookingData = {
                trip_id: selectedTrip,
                seats: selectedSeats,
                passenger: {
                    first_name: document.getElementById('first-name').value,
                    last_name: document.getElementById('last-name').value,
                    email: document.getElementById('email').value,
                    mobile: document.getElementById('mobile').value,
                    address: document.getElementById('address').value
                },
                payment_method: document.getElementById('payment-method').value,
                booking_type: currentTab
            };
            
            document.getElementById('modal-content').innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <div>Processing booking...</div>
                </div>
            `;
            
            fetch('/api/agent/process-walk-in-booking', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(bookingData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    toastr.success(`Booking successful! Ticket ID: ${data.ticket_id} | Seats: ${data.seats.join(', ')} | Total: ₱${data.total_amount}`, 'Booking Completed');
                    closeModal();
                    // Refresh the trips list to update seat availability
                    if (currentTab === 'today') {
                        loadTodaysTrips();
                    } else {
                        searchTrips();
                    }
                } else {
                    throw new Error(data.message);
                }
            })
            .catch(error => {
                toastr.error(`Booking failed: ${error.message}`, 'Booking Error');
                closeModal();
            });
        }
        
        // Close modal when clicking outside
        document.getElementById('booking-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
