<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bus Ticket - {{ $ticket->payments->first()->invoice_number ?? 'N/A' }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .ticket {
            background: white;
            max-width: 400px;
            margin: 0 auto;
            border: 2px dashed #333;
            border-radius: 10px;
            overflow: hidden;
        }
        .ticket-header {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .ticket-title {
            font-size: 14px;
            opacity: 0.9;
        }
        .ticket-body {
            padding: 20px;
        }
        .ticket-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px dotted #ddd;
        }
        .ticket-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .label {
            font-weight: bold;
            color: #555;
        }
        .value {
            color: #333;
        }
        .seat-number {
            background: #3b82f6;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 18px;
            text-align: center;
            margin: 15px 0;
        }
        .qr-section {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-top: 1px dashed #ddd;
        }
        .invoice-number {
            font-family: monospace;
            background: #f1f5f9;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
        }
        .print-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 20px auto;
            display: block;
        }
        .print-button:hover {
            background: #2563eb;
        }
        @media print {
            body {
                background: white;
                padding: 0;
            }
            .print-button {
                display: none;
            }
            .ticket {
                border: 2px solid #333;
                max-width: none;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="ticket">
        <div class="ticket-header">
            <div class="company-name">🚌 Bus Reservation</div>
            <div class="ticket-title">Bus Ticket</div>
        </div>
        
        <div class="ticket-body">
            <div class="ticket-row">
                <span class="label">Invoice #:</span>
                <span class="value invoice-number">{{ $ticket->payments->first()->invoice_number ?? 'N/A' }}</span>
            </div>
            
            <div class="ticket-row">
                <span class="label">Passenger:</span>
                <span class="value">
                    @php
                        $payment = $ticket->payments->first();
                        $passengerName = $payment && $payment->passenger_name ? $payment->passenger_name : $ticket->user->name;
                    @endphp
                    {{ $passengerName }}
                </span>
            </div>
            
            <div class="ticket-row">
                <span class="label">Route:</span>
                <span class="value">{{ $ticket->trip->route->start_point }} → {{ $ticket->trip->route->end_point }}</span>
            </div>
            
            <div class="ticket-row">
                <span class="label">Bus:</span>
                <span class="value">{{ $ticket->trip->bus->name }}</span>
            </div>
            
            <div class="ticket-row">
                <span class="label">Schedule:</span>
                <span class="value">{{ $ticket->trip->schedule->start_time }} - {{ $ticket->trip->schedule->end_time }}</span>
            </div>
            
            <div class="seat-number">
                SEAT {{ $ticket->seat_number }}
            </div>
            
            <div class="ticket-row">
                <span class="label">Booking Type:</span>
                <span class="value">{{ $ticket->booking_type === 'walk_in' ? 'Walk-in' : 'Online' }}</span>
            </div>
            
            <div class="ticket-row">
                <span class="label">Payment:</span>
                <span class="value">{{ strtoupper($ticket->payments->first()->payment_method ?? 'N/A') }}</span>
            </div>
            
            <div class="ticket-row">
                <span class="label">Amount:</span>
                <span class="value">₱{{ number_format($ticket->price, 2) }}</span>
            </div>
            
            <div class="ticket-row">
                <span class="label">Booking Date:</span>
                <span class="value">{{ $ticket->created_at->format('M d, Y H:i') }}</span>
            </div>
            
            @if($ticket->user->phone)
            <div class="ticket-row">
                <span class="label">Contact:</span>
                <span class="value">{{ $ticket->user->phone }}</span>
            </div>
            @endif
        </div>
        
        <div class="qr-section">
            <div style="font-size: 12px; color: #666; margin-bottom: 10px;">
                Ticket ID: {{ $ticket->id }}
            </div>
            <div style="font-size: 10px; color: #999;">
                Please present this ticket when boarding
            </div>
        </div>
    </div>
    
    <button class="print-button" onclick="window.print()">🖨️ Print Ticket</button>
    
    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
