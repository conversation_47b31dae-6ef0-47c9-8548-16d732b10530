<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt - {{ $payment->invoice_number }}</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-size: 14px;
        }
        .receipt {
            background: white;
            max-width: 350px;
            margin: 0 auto;
            border: 1px solid #333;
            padding: 0;
        }
        .receipt-header {
            text-align: center;
            padding: 15px;
            border-bottom: 1px dashed #333;
        }
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .receipt-title {
            font-size: 12px;
            margin-bottom: 10px;
        }
        .receipt-body {
            padding: 15px;
        }
        .receipt-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .receipt-row.total {
            border-top: 1px dashed #333;
            padding-top: 5px;
            margin-top: 10px;
            font-weight: bold;
        }
        .receipt-row.cash {
            border-top: 1px solid #333;
            padding-top: 5px;
            margin-top: 5px;
        }
        .label {
            flex: 1;
        }
        .value {
            text-align: right;
            min-width: 80px;
        }
        .center {
            text-align: center;
        }
        .receipt-footer {
            text-align: center;
            padding: 15px;
            border-top: 1px dashed #333;
            font-size: 11px;
        }
        .print-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 20px auto;
            display: block;
        }
        .print-button:hover {
            background: #2563eb;
        }
        @media print {
            body {
                background: white;
                padding: 0;
            }
            .print-button {
                display: none;
            }
            .receipt {
                max-width: none;
                margin: 0;
                border: 1px solid #333;
            }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <div class="receipt-header">
            <div class="company-name">🚌 BUS RESERVATION</div>
            <div class="receipt-title">OFFICIAL RECEIPT</div>
            <div style="font-size: 10px;">{{ now()->format('M d, Y H:i:s') }}</div>
        </div>
        
        <div class="receipt-body">
            <div class="receipt-row">
                <span class="label">Receipt #:</span>
                <span class="value">{{ $payment->invoice_number }}</span>
            </div>
            
            <div class="receipt-row">
                <span class="label">Customer:</span>
                <span class="value">
                    @php
                        $passengerName = $payment && $payment->passenger_name ? $payment->passenger_name : $ticket->user->name;
                    @endphp
                    {{ $passengerName }}
                </span>
            </div>
            
            @if($ticket->user->phone)
            <div class="receipt-row">
                <span class="label">Phone:</span>
                <span class="value">{{ $ticket->user->phone }}</span>
            </div>
            @endif
            
            <div style="margin: 10px 0; border-bottom: 1px dashed #333; padding-bottom: 5px;">
                <div class="center" style="font-weight: bold;">TRIP DETAILS</div>
            </div>
            
            <div class="receipt-row">
                <span class="label">Route:</span>
                <span class="value">{{ $ticket->trip->route->start_point }} → {{ $ticket->trip->route->end_point }}</span>
            </div>
            
            <div class="receipt-row">
                <span class="label">Bus:</span>
                <span class="value">{{ $ticket->trip->bus->name }}</span>
            </div>
            
            <div class="receipt-row">
                <span class="label">Schedule:</span>
                <span class="value">{{ $ticket->trip->schedule->start_time }}</span>
            </div>
            
            <div class="receipt-row">
                <span class="label">Seat:</span>
                <span class="value">{{ $ticket->seat_number }}</span>
            </div>
            
            <div class="receipt-row">
                <span class="label">Booking:</span>
                <span class="value">{{ $ticket->booking_type === 'walk_in' ? 'Walk-in' : 'Online' }}</span>
            </div>
            
            <div style="margin: 10px 0; border-bottom: 1px dashed #333; padding-bottom: 5px;">
                <div class="center" style="font-weight: bold;">PAYMENT DETAILS</div>
            </div>
            
            <div class="receipt-row">
                <span class="label">Fare:</span>
                <span class="value">₱{{ number_format($ticket->price, 2) }}</span>
            </div>
            
            <div class="receipt-row total">
                <span class="label">TOTAL AMOUNT:</span>
                <span class="value">₱{{ number_format($payment->amount, 2) }}</span>
            </div>
            
            @php
                $paymentDetails = json_decode($payment->payment_details, true);
                $isCashPayment = $payment->payment_method === 'cash' && isset($paymentDetails['cash_received']);
            @endphp
            
            @if($isCashPayment)
            <div class="receipt-row cash">
                <span class="label">Cash Received:</span>
                <span class="value">₱{{ number_format($paymentDetails['cash_received'], 2) }}</span>
            </div>
            
            <div class="receipt-row">
                <span class="label">Change:</span>
                <span class="value">₱{{ number_format($paymentDetails['change_amount'] ?? 0, 2) }}</span>
            </div>
            @endif
            
            <div class="receipt-row">
                <span class="label">Payment:</span>
                <span class="value">{{ strtoupper($payment->payment_method) }}</span>
            </div>
            
            <div class="receipt-row">
                <span class="label">Status:</span>
                <span class="value">{{ strtoupper($payment->status) }}</span>
            </div>
        </div>
        
        <div class="receipt-footer">
            <div>Thank you for choosing our service!</div>
            <div style="margin-top: 5px;">Please keep this receipt for your records</div>
            <div style="margin-top: 10px; font-size: 10px;">
                Ticket ID: {{ $ticket->id }} | Agent: {{ Auth::user()->name }}
            </div>
        </div>
    </div>
    
    <button class="print-button" onclick="window.print()">🖨️ Print Receipt</button>
    
    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
