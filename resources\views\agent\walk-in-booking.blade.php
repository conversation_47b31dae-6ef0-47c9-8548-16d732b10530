<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Walk-in Booking - {{ $trip->route->start_point }} → {{ $trip->route->end_point }}
            </h2>
            <a href="{{ route('agent.dashboard') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
                Back to Dashboard
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- Trip Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Trip Information</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Trip ID:</span>
                                <span class="font-semibold">#{{ $trip->id }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Route:</span>
                                <span class="font-semibold">{{ $trip->route->start_point }} → {{ $trip->route->end_point }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Bus:</span>
                                <span class="font-semibold">{{ $trip->bus->name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Schedule:</span>
                                <span class="font-semibold">{{ $trip->schedule->start_time }} - {{ $trip->schedule->end_time }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Total Seats:</span>
                                <span class="font-semibold">{{ $trip->bus->seat }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Available Seats:</span>
                                <span class="font-semibold text-green-600" id="seats-left">{{ $trip->bus->seat - count($bookedSeats) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Information Form -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Customer Information</h3>
                        <form id="walk-in-booking-form">
                            @csrf
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Customer Name *</label>
                                    <input type="text" name="customer_name" id="customer_name" required
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                                    <input type="tel" name="customer_phone" id="customer_phone" required
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Email (Optional)</label>
                                    <input type="email" name="customer_email" id="customer_email"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">ID Number (Optional)</label>
                                    <input type="text" name="customer_id_number" id="customer_id_number"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="Driver's License, Passport, etc.">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Number of Seats</label>
                                    <select id="seat-count" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                        <option value="1">1 Seat</option>
                                        <option value="2">2 Seats</option>
                                        <option value="3">3 Seats</option>
                                        <option value="4">4 Seats</option>
                                        <option value="5">5 Seats</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Payment Method *</label>
                                    <select name="payment_method" id="payment_method" required
                                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">Select Payment Method</option>
                                        <option value="cash">Cash (Walk-in)</option>
                                        <optgroup label="QR and e-Wallets">
                                            <option value="gcash">GCash</option>
                                            <option value="maya">Maya</option>
                                            <option value="qrph">QRPh</option>
                                        </optgroup>
                                        <optgroup label="Credit/Debit Cards">
                                            <option value="visa">Visa</option>
                                            <option value="master">MasterCard</option>
                                            <option value="gpay">Google Pay</option>
                                        </optgroup>
                                    </select>
                                </div>

                                <div id="payment-reference-div" class="hidden">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Payment Reference</label>
                                    <input type="text" name="payment_reference" id="payment_reference"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="Transaction ID, Reference Number, etc.">
                                </div>

                                <!-- Cash Payment Section -->
                                <div id="cash-payment-section" class="hidden">
                                    <div class="bg-green-50 p-4 rounded-md border border-green-200">
                                        <h4 class="font-semibold text-green-800 mb-3">Cash Payment</h4>

                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Total Amount</label>
                                                <input type="text" id="total-amount-display" readonly
                                                       class="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-100 font-semibold text-lg">
                                            </div>

                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Cash Received *</label>
                                                <input type="number" name="cash_received" id="cash_received" step="0.01" min="0"
                                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 text-lg"
                                                       placeholder="0.00">
                                            </div>
                                        </div>

                                        <div class="mt-4 p-3 bg-white rounded border">
                                            <div class="flex justify-between items-center">
                                                <span class="text-lg font-semibold text-gray-700">Change:</span>
                                                <span id="change-amount" class="text-2xl font-bold text-green-600">₱0.00</span>
                                            </div>
                                            <div id="change-status" class="text-sm mt-1"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Seat Selection -->
            <div class="mt-8 bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">Select Seats</h3>
                        <button onclick="refreshSeats()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm">
                            Refresh Seats
                        </button>
                    </div>

                    <!-- Selected Seats Display -->
                    <div id="selected-seats-display" class="mb-4 hidden">
                        <h4 class="font-semibold text-gray-800 mb-2">Selected Seats:</h4>
                        <div id="selected-seats-list" class="flex flex-wrap gap-2"></div>
                    </div>

                    <!-- Seat Map -->
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <div class="text-center mb-4">
                            <div class="inline-block bg-gray-800 text-white px-4 py-2 rounded-t-lg">
                                🚌 Driver
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-4 gap-2 max-w-md mx-auto" id="seat-map">
                            @for($i = 1; $i <= $trip->bus->seat; $i++)
                                <button type="button" 
                                        class="seat-btn w-12 h-12 border-2 rounded text-sm font-semibold transition-colors
                                               {{ in_array($i, $bookedSeats) ? 'seat-booked cursor-not-allowed bg-red-500 text-white border-red-600' : 'seat-available hover:seat-selected bg-green-100 text-green-800 border-green-300 hover:bg-yellow-200 hover:border-yellow-400' }}"
                                        data-seat="{{ $i }}"
                                        {{ in_array($i, $bookedSeats) ? 'disabled' : '' }}>
                                    {{ $i }}
                                </button>
                            @endfor
                        </div>

                        <div class="flex justify-center gap-6 mt-6 text-sm">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-green-100 border border-green-300 rounded mr-2"></div>
                                <span>Available</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-yellow-200 border border-yellow-400 rounded mr-2"></div>
                                <span>Selected</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-red-500 border border-red-600 rounded mr-2"></div>
                                <span>Booked</span>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Summary -->
                    <div class="mt-6 p-4 bg-gray-50 rounded-md">
                        <div class="flex justify-between text-sm mb-2">
                            <span>Price per seat:</span>
                            <span class="base-price">₱100.00</span>
                        </div>
                        <div class="flex justify-between text-sm mb-2">
                            <span>Selected seats:</span>
                            <span id="selected-count">0</span>
                        </div>
                        <div class="flex justify-between font-semibold text-lg border-t pt-2">
                            <span>Total:</span>
                            <span class="total-price">₱0.00</span>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex gap-4 mt-6">
                        <button type="button" id="confirm-booking-btn" 
                                class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded font-semibold disabled:bg-gray-400 disabled:cursor-not-allowed" 
                                disabled onclick="confirmBooking()">
                            Confirm Walk-in Booking
                        </button>
                        <button type="button" onclick="clearSelection()" 
                                class="bg-gray-600 hover:bg-gray-700 text-white py-3 px-6 rounded">
                            Clear Selection
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <input type="hidden" name="seat_numbers" id="selected-seats-input">

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        let selectedSeats = [];
        let maxSeats = 1;
        let seatPrice = 100;

        // Initialize
        $(document).ready(function() {
            // Get fare information
            $.get(`/fares/route/{{ $trip->route_id }}`)
                .done(function(data) {
                    if (data.success) {
                        seatPrice = parseFloat(data.fare.base_fare);
                        updatePriceDisplay();
                    }
                });

            // Payment method change handler
            $('#payment_method').on('change', function() {
                const method = $(this).val();
                if (method === 'cash') {
                    $('#payment-reference-div').addClass('hidden');
                    $('#payment_reference').prop('required', false);
                    $('#cash-payment-section').removeClass('hidden');
                    $('#cash_received').prop('required', true);
                    updateCashPaymentDisplay();
                } else if (method && method !== 'cash') {
                    $('#payment-reference-div').removeClass('hidden');
                    $('#payment_reference').prop('required', true);
                    $('#cash-payment-section').addClass('hidden');
                    $('#cash_received').prop('required', false);
                } else {
                    $('#payment-reference-div').addClass('hidden');
                    $('#cash-payment-section').addClass('hidden');
                    $('#payment_reference').prop('required', false);
                    $('#cash_received').prop('required', false);
                }
            });

            // Cash received input handler
            $('#cash_received').on('input', function() {
                calculateChange();
            });

            // Seat count change handler
            $('#seat-count').on('change', function() {
                maxSeats = parseInt($(this).val());
                if (selectedSeats.length > maxSeats) {
                    const excessSeats = selectedSeats.splice(maxSeats);
                    excessSeats.forEach(seat => {
                        $(`.seat-btn[data-seat="${seat}"]`).removeClass('seat-selected');
                    });
                    updateSelectedSeatsDisplay();
                }
            });

            // Seat selection
            $('.seat-btn').on('click', function() {
                if ($(this).hasClass('seat-booked')) return;
                
                const seatNum = parseInt($(this).data('seat'));
                const isSelected = $(this).hasClass('seat-selected');
                
                if (isSelected) {
                    // Deselect seat
                    $(this).removeClass('seat-selected');
                    selectedSeats = selectedSeats.filter(seat => seat !== seatNum);
                } else {
                    // Select seat if under limit
                    if (selectedSeats.length < maxSeats) {
                        $(this).addClass('seat-selected');
                        selectedSeats.push(seatNum);
                    } else {
                        Swal.fire({
                            icon: 'warning',
                            title: 'Seat Limit Reached',
                            text: `You can only select ${maxSeats} seat(s). Please deselect a seat first or change the number of seats.`,
                            confirmButtonColor: '#3b82f6'
                        });
                        return;
                    }
                }
                
                updateSelectedSeatsDisplay();
            });
        });

        function updateSelectedSeatsDisplay() {
            const display = $('#selected-seats-display');
            const list = $('#selected-seats-list');
            
            if (selectedSeats.length > 0) {
                display.removeClass('hidden');
                list.empty();
                selectedSeats.forEach(seat => {
                    list.append(`<span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">Seat ${seat}</span>`);
                });
            } else {
                display.addClass('hidden');
            }
            
            $('#selected-seats-input').val(selectedSeats.join(','));
            $('#selected-count').text(selectedSeats.length);
            $('#confirm-booking-btn').prop('disabled', selectedSeats.length === 0);
            updatePriceDisplay();
        }

        function updatePriceDisplay() {
            const totalPrice = selectedSeats.length * seatPrice;
            $('.base-price').text('₱' + seatPrice.toFixed(2));
            $('.total-price').text('₱' + totalPrice.toFixed(2));
            updateCashPaymentDisplay();
        }

        function updateCashPaymentDisplay() {
            const totalPrice = selectedSeats.length * seatPrice;
            $('#total-amount-display').val('₱' + totalPrice.toFixed(2));
            calculateChange();
        }

        function calculateChange() {
            const totalPrice = selectedSeats.length * seatPrice;
            const cashReceived = parseFloat($('#cash_received').val()) || 0;
            const change = cashReceived - totalPrice;

            $('#change-amount').text('₱' + Math.max(0, change).toFixed(2));

            const statusDiv = $('#change-status');
            if (cashReceived === 0) {
                statusDiv.text('').removeClass('text-red-600 text-green-600 text-yellow-600');
            } else if (change < 0) {
                statusDiv.text('Insufficient payment: ₱' + Math.abs(change).toFixed(2) + ' short')
                         .removeClass('text-green-600 text-yellow-600').addClass('text-red-600');
            } else if (change === 0) {
                statusDiv.text('Exact payment')
                         .removeClass('text-red-600 text-yellow-600').addClass('text-green-600');
            } else {
                statusDiv.text('Change to give: ₱' + change.toFixed(2))
                         .removeClass('text-red-600 text-green-600').addClass('text-yellow-600');
            }
        }

        function refreshSeats() {
            $.get(`/agent/available-seats/{{ $trip->id }}`)
                .done(function(data) {
                    $('.seat-btn').each(function() {
                        const seatNum = parseInt($(this).data('seat'));
                        const isBooked = data.booked_seats.includes(seatNum);
                        
                        if (isBooked) {
                            $(this).removeClass('seat-available hover:seat-selected bg-green-100 text-green-800 border-green-300 hover:bg-yellow-200 hover:border-yellow-400')
                                   .addClass('seat-booked cursor-not-allowed bg-red-500 text-white border-red-600')
                                   .prop('disabled', true);
                        } else {
                            $(this).removeClass('seat-booked cursor-not-allowed bg-red-500 text-white border-red-600')
                                   .addClass('seat-available hover:seat-selected bg-green-100 text-green-800 border-green-300 hover:bg-yellow-200 hover:border-yellow-400')
                                   .prop('disabled', false);
                        }
                    });
                    
                    $('#seats-left').text(data.seats_left);
                });
        }

        function clearSelection() {
            selectedSeats = [];
            $('.seat-btn').removeClass('seat-selected');
            updateSelectedSeatsDisplay();
        }

        function confirmBooking() {
            // Validate form
            const form = $('#walk-in-booking-form')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            if (selectedSeats.length === 0) {
                Swal.fire({
                    icon: 'warning',
                    title: 'No Seats Selected',
                    text: 'Please select at least one seat.',
                    confirmButtonColor: '#3b82f6'
                });
                return;
            }

            // Validate cash payment if cash is selected
            const paymentMethod = $('#payment_method').val();
            if (paymentMethod === 'cash') {
                const totalPrice = selectedSeats.length * seatPrice;
                const cashReceived = parseFloat($('#cash_received').val()) || 0;

                if (cashReceived < totalPrice) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Insufficient Payment',
                        text: `Cash received (₱${cashReceived.toFixed(2)}) is less than total amount (₱${totalPrice.toFixed(2)})`,
                        confirmButtonColor: '#ef4444'
                    });
                    return;
                }
            }

            const formData = new FormData(form);
            formData.append('seat_numbers', selectedSeats.join(','));

            // Add cash payment data if cash is selected
            if (paymentMethod === 'cash') {
                const totalPrice = selectedSeats.length * seatPrice;
                const cashReceived = parseFloat($('#cash_received').val()) || 0;
                const change = cashReceived - totalPrice;

                formData.append('cash_received', cashReceived);
                formData.append('change_amount', change);
                formData.append('total_amount', totalPrice);
            }

            $.ajax({
                url: `/agent/walk-in-booking/{{ $trip->id }}`,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    let receiptHtml = `
                        <div class="text-left bg-gray-50 p-4 rounded-lg mb-4">
                            <h4 class="font-bold text-lg mb-2">🧾 RECEIPT</h4>
                            <p><strong>Customer:</strong> ${response.customer_name}</p>
                            <p><strong>Invoice:</strong> ${response.invoice_numbers.join(', ')}</p>
                            <p><strong>Seats:</strong> ${response.seats_booked.join(', ')}</p>
                            <p><strong>Total Amount:</strong> ₱${response.total_amount.toFixed(2)}</p>
                    `;

                    if (response.payment_method === 'cash' && response.cash_received) {
                        receiptHtml += `
                            <hr class="my-2">
                            <p><strong>Cash Received:</strong> ₱${response.cash_received.toFixed(2)}</p>
                            <p><strong>Change:</strong> ₱${response.change_amount.toFixed(2)}</p>
                        `;
                    }

                    receiptHtml += `</div>`;

                    Swal.fire({
                        icon: 'success',
                        title: 'Booking Successful!',
                        html: receiptHtml + `<p class="text-sm text-gray-600 mt-2">Booking completed successfully!</p>`,
                        confirmButtonColor: '#10b981',
                        width: '500px'
                    }).then(() => {
                        // Reset form and refresh
                        form.reset();
                        clearSelection();
                        refreshSeats();
                        $('#cash_received').val('');
                        calculateChange();
                    });
                },
                error: function(xhr) {
                    let errorMessage = 'An error occurred while processing the booking.';
                    
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }
                    
                    Swal.fire({
                        icon: 'error',
                        title: 'Booking Failed',
                        text: errorMessage,
                        confirmButtonColor: '#ef4444'
                    });
                }
            });
        }
    </script>
</x-app-layout>
