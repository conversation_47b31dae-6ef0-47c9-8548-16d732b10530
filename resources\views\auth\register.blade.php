<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="Create your account - GL Bus Reservation System">
    <meta name="keywords" content="register, sign up, create account, GL Bus">

    <title>Register - {{ config('app.name') }}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ asset('images/gl-logo.png') }}">
    <link rel="shortcut icon" type="image/png" href="{{ asset('images/gl-logo.png') }}">
    <link rel="apple-touch-icon" href="{{ asset('images/gl-logo.png') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <style>
        :root {
            /* GL Bus Color Palette */
            --primary-gold: #FCB404;
            --primary-gold-dark: #E6A200;
            --secondary-blue: #1E3A8A;
            --secondary-blue-light: #3B82F6;
            --accent-green: #10B981;
            --accent-purple: #8B5CF6;
            --accent-orange: #F97316;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --gradient-hero: linear-gradient(135deg, var(--primary-gold) 0%, var(--accent-orange) 50%, var(--secondary-blue) 100%);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Figtree', sans-serif;
            background: var(--gradient-hero);
            min-height: 100vh;
        }

        .form-container {
            background: white;
            border-radius: 1rem;
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            max-width: 500px;
            margin: 0 auto;
        }

        .form-header {
            background: var(--gradient-hero);
            padding: 2rem;
            text-align: center;
            color: white;
        }

        .form-body {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--gray-200);
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--gray-50);
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-gold);
            background: white;
            box-shadow: 0 0 0 3px rgba(252, 180, 4, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--gray-200);
            border-radius: 0.5rem;
            font-size: 1rem;
            background: var(--gray-50);
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-select:focus {
            outline: none;
            border-color: var(--primary-gold);
            background: white;
            box-shadow: 0 0 0 3px rgba(252, 180, 4, 0.1);
        }

        .btn-primary {
            width: 100%;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-dark));
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(252, 180, 4, 0.4);
        }

        .error-message {
            color: #dc2626;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }

        .link {
            color: var(--primary-gold);
            text-decoration: none;
            font-weight: 500;
        }

        .link:hover {
            text-decoration: underline;
        }

        .text-center {
            text-align: center;
        }

        .text-sm {
            font-size: 0.875rem;
        }

        .mt-4 {
            margin-top: 1rem;
        }

        .grid-cols-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        @media (max-width: 640px) {
            .grid-cols-2 {
                grid-template-columns: 1fr;
            }

            .form-container {
                margin: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-lg">
            <div class="form-container">
                <!-- Header -->
                <div class="form-header">
                    <h1 class="text-3xl font-bold mb-2">🚌 Create Account</h1>
                    <p class="opacity-90">Join thousands of satisfied customers</p>
                </div>

                <!-- Form -->
                <div class="form-body">
                    <form method="POST" action="{{ route('register') }}">
                        @csrf

                        <!-- Name -->
                        <div class="form-group">
                            <label for="name" class="form-label">Full Name *</label>
                            <input id="name" class="form-input" type="text" name="name" value="{{ old('name') }}"
                                   required autofocus autocomplete="name" placeholder="Enter your full name" />
                            @error('name')
                                <div class="error-message">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Email & Phone Row -->
                        <div class="grid-cols-2">
                            <div class="form-group">
                                <label for="email" class="form-label">Email Address *</label>
                                <input id="email" class="form-input" type="email" name="email" value="{{ old('email') }}"
                                       required autocomplete="username" placeholder="<EMAIL>" />
                                @error('email')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input id="phone" class="form-input" type="tel" name="phone" value="{{ old('phone') }}"
                                       autocomplete="tel" placeholder="09123456789" />
                                @error('phone')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Gender -->
                        <div class="form-group">
                            <label for="Gender" class="form-label">Gender *</label>
                            <select name="Gender" id="Gender" class="form-select" required>
                                <option value="">Select Gender</option>
                                <option value="male" {{ old('Gender') == 'male' ? 'selected' : '' }}>Male</option>
                                <option value="female" {{ old('Gender') == 'female' ? 'selected' : '' }}>Female</option>
                            </select>
                            @error('Gender')
                                <div class="error-message">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Password Row -->
                        <div class="grid-cols-2">
                            <div class="form-group">
                                <label for="password" class="form-label">Password *</label>
                                <input id="password" class="form-input" type="password" name="password"
                                       required autocomplete="new-password" placeholder="Enter password" />
                                @error('password')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label for="password_confirmation" class="form-label">Confirm Password *</label>
                                <input id="password_confirmation" class="form-input" type="password"
                                       name="password_confirmation" required autocomplete="new-password"
                                       placeholder="Confirm password" />
                                @error('password_confirmation')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-group">
                            <button type="submit" class="btn-primary">
                                🚀 Create My Account
                            </button>
                        </div>

                        <!-- Login Link -->
                        <div class="text-center mt-4">
                            <p class="text-sm text-gray-600">
                                Already have an account?
                                <a href="{{ route('login') }}" class="link">Login here</a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Back to Home -->
            <div class="text-center mt-6">
                <a href="{{ route('welcome') }}" class="text-white/80 hover:text-white text-sm">
                    ← Back to Home
                </a>
            </div>
        </div>
    </div>
</body>
</html>