@extends('layouts.app')

@section('content')
<style>
/* Print styles */
@media print {
    .no-print { display: none !important; }
    body { background: white !important; }
    .receipt-container {
        max-width: none !important;
        margin: 0 !important;
        box-shadow: none !important;
        border: none !important;
    }
    .page-break { page-break-after: always; }
}

/* Receipt styles */
.receipt-container {
    max-width: 600px;
    margin: 0 auto;
    background: white;
    border: 2px solid #ddd;
    font-family: 'Courier New', monospace;
}

.receipt-header {
    background: linear-gradient(135deg, #FCB404 0%, #E6A200 100%);
    color: white;
    text-align: center;
    padding: 20px;
    border-bottom: 3px dashed #ddd;
}

.receipt-body {
    padding: 20px;
}

.receipt-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px dotted #ddd;
}

.receipt-row:last-child {
    border-bottom: none;
}

.receipt-label {
    font-weight: bold;
    color: #333;
}

.receipt-value {
    color: #666;
    text-align: right;
}

.receipt-total {
    background: #f8f9fa;
    padding: 15px;
    margin: 20px 0;
    border: 2px solid #FCB404;
    border-radius: 8px;
}

.receipt-footer {
    background: #f8f9fa;
    padding: 15px;
    text-align: center;
    border-top: 3px dashed #ddd;
    font-size: 12px;
    color: #666;
}

.qr-placeholder {
    width: 80px;
    height: 80px;
    background: #f0f0f0;
    border: 2px dashed #ccc;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 10px;
    color: #999;
}

.ticket-perforations {
    background-image: radial-gradient(circle, transparent 3px, white 3px);
    background-size: 12px 12px;
    background-position: 6px 0;
    height: 12px;
    border-top: 1px dashed #ddd;
    border-bottom: 1px dashed #ddd;
}

@media screen {
    .receipt-container {
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
}
</style>
<div class="min-h-screen bg-gray-100 py-8">
    <div class="max-w-4xl mx-auto px-4">

        <!-- Action Buttons (No Print) -->
        <div class="no-print mb-6 text-center">
            <button onclick="window.print()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg mr-3">
                <i class="fas fa-print mr-2"></i>Print Receipt
            </button>
            <button onclick="downloadPDF()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg mr-3">
                <i class="fas fa-download mr-2"></i>Download PDF
            </button>
            <a href="{{ route('booking.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg">
                <i class="fas fa-arrow-left mr-2"></i>New Booking
            </a>
        </div>

        <!-- Receipt Container -->
        <div class="receipt-container mx-auto shadow-lg">
            <!-- Receipt Header -->
            <div class="receipt-header">
                <h1 class="text-2xl font-bold mb-2">🚌 GL BUS LINES</h1>
                <p class="text-lg">OFFICIAL RECEIPT</p>
                <p class="text-sm opacity-90">Bus Ticket Booking Confirmation</p>
            </div>

            <!-- Receipt Body -->
            <div class="receipt-body">
                <!-- Booking Reference -->
                <div class="text-center mb-6">
                    <div class="text-2xl font-bold text-gray-800 mb-2">
                        BOOKING #{{ str_pad($payment->id, 6, '0', STR_PAD_LEFT) }}
                    </div>
                    <div class="text-sm text-gray-600">
                        {{ now()->format('F d, Y \a\t g:i A') }}
                    </div>
                </div>

                <!-- Passenger Information -->
                <div class="mb-6">
                    <h3 class="font-bold text-lg mb-3 text-center border-b-2 border-dashed border-gray-300 pb-2">
                        PASSENGER DETAILS
                    </h3>
                    <div class="receipt-row">
                        <span class="receipt-label">Name:</span>
                        <span class="receipt-value">{{ $payment->passenger_name }}</span>
                    </div>
                    <div class="receipt-row">
                        <span class="receipt-label">Email:</span>
                        <span class="receipt-value">{{ $payment->passenger_email }}</span>
                    </div>
                    <div class="receipt-row">
                        <span class="receipt-label">Mobile:</span>
                        <span class="receipt-value">{{ $payment->passenger_mobile }}</span>
                    </div>
                </div>

                <!-- Trip Information -->
                @if($payment->booking_type === 'round_trip' && $payment->booking_details)
                    @php
                        $bookingDetails = json_decode($payment->booking_details, true);
                        $outboundTrip = \App\Models\Trip::with(['route', 'schedule', 'bus'])->find($bookingDetails['outbound_trip_id']);
                        $returnTrip = \App\Models\Trip::with(['route', 'schedule', 'bus'])->find($bookingDetails['return_trip_id']);
                    @endphp

                    <!-- Outbound Trip -->
                    <div class="mb-6">
                        <h3 class="font-bold text-lg mb-3 text-center border-b-2 border-dashed border-blue-300 pb-2 text-blue-800">
                            🛫 OUTBOUND TRIP
                        </h3>
                        <div class="receipt-row">
                            <span class="receipt-label">Route:</span>
                            <span class="receipt-value">{{ $outboundTrip->route->start_point }} → {{ $outboundTrip->route->end_point }}</span>
                        </div>
                        <div class="receipt-row">
                            <span class="receipt-label">Date:</span>
                            <span class="receipt-value">{{ \Carbon\Carbon::parse($outboundTrip->schedule->trip_date)->format('M d, Y (l)') }}</span>
                        </div>
                        <div class="receipt-row">
                            <span class="receipt-label">Departure:</span>
                            <span class="receipt-value">{{ $outboundTrip->schedule->formatted_start_time }}</span>
                        </div>
                        <div class="receipt-row">
                            <span class="receipt-label">Bus Type:</span>
                            <span class="receipt-value">{{ $outboundTrip->bus->bus_code ?? 'Regular' }}</span>
                        </div>
                        <div class="receipt-row">
                            <span class="receipt-label">Seats:</span>
                            <span class="receipt-value font-bold text-lg">{{ implode(', ', $bookingDetails['outbound_seats']) }}</span>
                        </div>
                    </div>

                    <!-- Perforated Line -->
                    <div class="ticket-perforations my-4"></div>

                    <!-- Return Trip -->
                    <div class="mb-6">
                        <h3 class="font-bold text-lg mb-3 text-center border-b-2 border-dashed border-green-300 pb-2 text-green-800">
                            🛬 RETURN TRIP
                        </h3>
                        <div class="receipt-row">
                            <span class="receipt-label">Route:</span>
                            <span class="receipt-value">{{ $returnTrip->route->start_point }} → {{ $returnTrip->route->end_point }}</span>
                        </div>
                        <div class="receipt-row">
                            <span class="receipt-label">Date:</span>
                            <span class="receipt-value">{{ \Carbon\Carbon::parse($returnTrip->schedule->trip_date)->format('M d, Y (l)') }}</span>
                        </div>
                        <div class="receipt-row">
                            <span class="receipt-label">Departure:</span>
                            <span class="receipt-value">{{ $returnTrip->schedule->formatted_start_time }}</span>
                        </div>
                        <div class="receipt-row">
                            <span class="receipt-label">Bus Type:</span>
                            <span class="receipt-value">{{ $returnTrip->bus->bus_code ?? 'Regular' }}</span>
                        </div>
                        <div class="receipt-row">
                            <span class="receipt-label">Seats:</span>
                            <span class="receipt-value font-bold text-lg">{{ implode(', ', $bookingDetails['return_seats']) }}</span>
                        </div>
                    </div>
                @else
                    <!-- One-way Trip -->
                    <div class="mb-6">
                        <h3 class="font-bold text-lg mb-3 text-center border-b-2 border-dashed border-gray-300 pb-2">
                            TRIP DETAILS
                        </h3>
                        <div class="receipt-row">
                            <span class="receipt-label">Route:</span>
                            <span class="receipt-value">
                                {{ $payment->ticket->trip->route->start_point }} → {{ $payment->ticket->trip->route->end_point }}
                            </span>
                        </div>
                        <div class="receipt-row">
                            <span class="receipt-label">Date:</span>
                            <span class="receipt-value">
                                {{ \Carbon\Carbon::parse($payment->ticket->trip->schedule->trip_date)->format('M d, Y (l)') }}
                            </span>
                        </div>
                        <div class="receipt-row">
                            <span class="receipt-label">Departure:</span>
                            <span class="receipt-value">
                                {{ $payment->ticket->trip->schedule->formatted_start_time }}
                            </span>
                        </div>
                        <div class="receipt-row">
                            <span class="receipt-label">Bus Type:</span>
                            <span class="receipt-value">{{ $payment->ticket->trip->bus->bus_code ?? 'Regular' }}</span>
                        </div>
                        <div class="receipt-row">
                            <span class="receipt-label">Seat Number:</span>
                            <span class="receipt-value font-bold text-lg">{{ $payment->ticket->seat_number }}</span>
                        </div>
                    </div>
                @endif

                <!-- Perforated Line -->
                <div class="ticket-perforations my-4"></div>

                <!-- Payment Information -->
                <div class="mb-6">
                    <h3 class="font-bold text-lg mb-3 text-center border-b-2 border-dashed border-gray-300 pb-2">
                        PAYMENT DETAILS
                    </h3>
                    <div class="receipt-row">
                        <span class="receipt-label">Fare Amount:</span>
                        <span class="receipt-value">₱{{ number_format($payment->amount, 2) }}</span>
                    </div>
                    <div class="receipt-row">
                        <span class="receipt-label">Service Fee:</span>
                        <span class="receipt-value">₱0.00</span>
                    </div>
                    <div class="receipt-row">
                        <span class="receipt-label">Payment Method:</span>
                        <span class="receipt-value">{{ strtoupper($payment->payment_method) }}</span>
                    </div>
                    @if($payment->reference_number)
                    <div class="receipt-row">
                        <span class="receipt-label">Reference #:</span>
                        <span class="receipt-value">{{ $payment->reference_number }}</span>
                    </div>
                    @endif
                    <div class="receipt-row">
                        <span class="receipt-label">Status:</span>
                        <span class="receipt-value">
                            @if($payment->status === 'paid')
                                ✅ PAID
                            @else
                                ⏳ PENDING
                            @endif
                        </span>
                    </div>
                </div>

                <!-- Total Amount -->
                <div class="receipt-total">
                    <div class="flex justify-between items-center">
                        <span class="text-xl font-bold">TOTAL AMOUNT:</span>
                        <span class="text-2xl font-bold text-green-600">₱{{ number_format($payment->amount, 2) }}</span>
                    </div>
                </div>

                <!-- QR Code Placeholder -->
                <div class="text-center mb-6">
                    <div class="qr-placeholder">
                        <div class="text-center">
                            <div>📱</div>
                            <div>QR CODE</div>
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">Booking Reference: #{{ str_pad($payment->id, 6, '0', STR_PAD_LEFT) }}</p>
            </div>

            <!-- Receipt Footer -->
            <div class="receipt-footer">
                <div class="mb-4">
                    <h4 class="font-bold mb-2">IMPORTANT REMINDERS:</h4>
                    <div class="text-left space-y-1">
                        <p>• Please arrive at the terminal 30 minutes before departure</p>
                        <p>• Present this receipt and valid ID during boarding</p>
                        <p>• This ticket is non-transferable and non-refundable</p>
                        <p>• Keep this receipt for your records</p>
                    </div>
                </div>

                <div class="border-t border-dashed border-gray-400 pt-3 mt-3">
                    <p class="font-bold">GL BUS LINES</p>
                    <p>📞 Customer Service: (02) 8123-4567</p>
                    <p>📧 Email: <EMAIL></p>
                    <p>🌐 Website: www.glbus.com</p>
                </div>

                <div class="border-t border-dashed border-gray-400 pt-3 mt-3">
                    <p class="text-xs">
                        Receipt generated on {{ now()->format('F d, Y \a\t g:i A') }}<br>
                        Thank you for choosing GL Bus Lines!
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
function downloadPDF() {
    // Simple PDF download simulation
    const bookingRef = '#{{ str_pad($payment->id, 6, "0", STR_PAD_LEFT) }}';
    const filename = `GL_Bus_Receipt_${bookingRef.replace('#', '')}.pdf`;

    // For now, just print - in a real app, you'd generate a PDF
    toastr.info('PDF download feature coming soon! For now, please use Print.');
    setTimeout(() => {
        window.print();
    }, 1000);
}

// Auto-focus on print button for accessibility
document.addEventListener('DOMContentLoaded', function() {
    // Show success message
    toastr.success('🎉 Booking confirmed successfully!', 'Success!', {
        timeOut: 3000,
        progressBar: true
    });
});
</script>
@endsection
