@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-6xl mx-auto px-4">
        <!-- Demo Header -->
        <div class="bg-white rounded-lg shadow-sm border mb-8">
            <div class="p-8 text-center">
                <h1 class="text-3xl font-bold text-gray-900 mb-4">🚌 Enhanced Booking System Demo</h1>
                <p class="text-lg text-gray-600 mb-6">
                    Inspired by Victory Liner's professional booking interface, featuring a modern step-by-step process
                </p>
                <a href="{{ route('booking.index') }}" 
                   class="inline-flex items-center px-6 py-3 bg-red-600 text-white font-semibold rounded-lg hover:bg-red-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Try the Enhanced Booking System
                </a>
            </div>
        </div>

        <!-- Features Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Step 1: Date & Route</h3>
                <p class="text-sm text-gray-600">Interactive calendar with weekly navigation and route selection</p>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-6">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Step 2: Passenger Info</h3>
                <p class="text-sm text-gray-600">Comprehensive form with validation and email confirmation</p>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-6">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Step 3: Seat Selection</h3>
                <p class="text-sm text-gray-600">Interactive seat map with different seat types and status indicators</p>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-6">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Step 4: Summary</h3>
                <p class="text-sm text-gray-600">Detailed fare breakdown and booking confirmation</p>
            </div>
        </div>

        <!-- Key Features -->
        <div class="bg-white rounded-lg shadow-sm border mb-8">
            <div class="p-6 border-b">
                <h2 class="text-xl font-semibold text-gray-900">Key Features</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">User Experience</h3>
                        <ul class="space-y-3 text-sm text-gray-600">
                            <li class="flex items-start">
                                <svg class="w-4 h-4 mt-0.5 mr-3 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Step-by-step progress indicator
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 mt-0.5 mr-3 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Session timer with 15-minute expiry
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 mt-0.5 mr-3 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Interactive calendar with weekly navigation
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 mt-0.5 mr-3 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Real-time form validation
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 mt-0.5 mr-3 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Professional Victory Liner-inspired design
                            </li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Technical Features</h3>
                        <ul class="space-y-3 text-sm text-gray-600">
                            <li class="flex items-start">
                                <svg class="w-4 h-4 mt-0.5 mr-3 text-blue-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                AJAX-powered trip search
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 mt-0.5 mr-3 text-blue-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Real-time seat availability checking
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 mt-0.5 mr-3 text-blue-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Multiple payment method support
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 mt-0.5 mr-3 text-blue-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Database transaction safety
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 mt-0.5 mr-3 text-blue-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Comprehensive booking confirmation
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Seat Map Legend -->
        <div class="bg-white rounded-lg shadow-sm border mb-8">
            <div class="p-6 border-b">
                <h2 class="text-xl font-semibold text-gray-900">Seat Selection Features</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
                    <div class="flex flex-col items-center">
                        <div class="w-12 h-12 bg-yellow-400 rounded border-2 border-yellow-500 mb-2 flex items-center justify-center font-semibold">1</div>
                        <span class="text-sm font-medium">Priority Seats</span>
                        <span class="text-xs text-gray-500">Front seats</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-12 h-12 bg-red-500 text-white rounded border-2 border-red-600 mb-2 flex items-center justify-center font-semibold">X</div>
                        <span class="text-sm font-medium">Booked Seats</span>
                        <span class="text-xs text-gray-500">Unavailable</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-12 h-12 bg-green-500 text-white rounded border-2 border-green-600 mb-2 flex items-center justify-center font-semibold">✓</div>
                        <span class="text-sm font-medium">Selected Seats</span>
                        <span class="text-xs text-gray-500">Your choice</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-12 h-12 bg-gray-200 text-gray-800 rounded border-2 border-gray-300 mb-2 flex items-center justify-center font-semibold">○</div>
                        <span class="text-sm font-medium">Available Seats</span>
                        <span class="text-xs text-gray-500">Click to select</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-12 h-12 bg-orange-400 rounded border-2 border-orange-500 mb-2 flex items-center justify-center font-semibold">S</div>
                        <span class="text-sm font-medium">Saved Seats</span>
                        <span class="text-xs text-gray-500">Reserved</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Comparison -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="p-6 border-b">
                <h2 class="text-xl font-semibold text-gray-900">Enhanced vs Legacy Booking</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-lg font-semibold text-green-600 mb-4">✅ Enhanced Booking System</h3>
                        <ul class="space-y-2 text-sm text-gray-600">
                            <li>• Step-by-step guided process</li>
                            <li>• Professional Victory Liner-inspired design</li>
                            <li>• Interactive calendar date picker</li>
                            <li>• Comprehensive passenger information form</li>
                            <li>• Visual seat map with status indicators</li>
                            <li>• Detailed fare breakdown</li>
                            <li>• Session timer for urgency</li>
                            <li>• Mobile-responsive design</li>
                            <li>• Real-time validation</li>
                            <li>• Professional confirmation page</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold text-gray-500 mb-4">📋 Legacy Booking System</h3>
                        <ul class="space-y-2 text-sm text-gray-600">
                            <li>• Single-page booking modal</li>
                            <li>• Basic trip listing</li>
                            <li>• Simple seat selection</li>
                            <li>• Minimal passenger information</li>
                            <li>• Basic confirmation</li>
                            <li>• Limited visual feedback</li>
                            <li>• No progress tracking</li>
                            <li>• Basic styling</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="text-center mt-8">
            <a href="{{ route('booking.index') }}" 
               class="inline-flex items-center px-8 py-4 bg-red-600 text-white font-semibold text-lg rounded-lg hover:bg-red-700 transition-colors shadow-lg">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                Experience the Enhanced Booking System
            </a>
        </div>
    </div>
</div>
@endsection
