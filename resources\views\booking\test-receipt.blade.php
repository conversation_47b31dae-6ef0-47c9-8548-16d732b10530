@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-sm border p-8">
            <h1 class="text-2xl font-bold text-gray-900 mb-6">🧾 Receipt Location Test</h1>
            
            <div class="space-y-4">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="font-semibold text-blue-900">📍 After Booking Completion:</h3>
                    <p class="text-blue-800 mt-2">
                        When you complete a booking, you will be redirected to:<br>
                        <code class="bg-blue-100 px-2 py-1 rounded">/booking-confirmation/{booking_id}</code>
                    </p>
                </div>
                
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 class="font-semibold text-green-900">✅ What the Receipt Contains:</h3>
                    <ul class="text-green-800 mt-2 space-y-1">
                        <li>• Booking confirmation with reference number</li>
                        <li>• Complete trip details (route, date, time)</li>
                        <li>• Passenger information</li>
                        <li>• Payment summary and breakdown</li>
                        <li>• QR code for boarding</li>
                        <li>• Print and download options</li>
                    </ul>
                </div>
                
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h3 class="font-semibold text-yellow-900">🔗 Direct Access:</h3>
                    <p class="text-yellow-800 mt-2">
                        You can also access receipts directly if you have the booking ID:<br>
                        <code class="bg-yellow-100 px-2 py-1 rounded">http://localhost/GL_BUS/public/booking-confirmation/1</code>
                    </p>
                </div>
                
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h3 class="font-semibold text-purple-900">🎯 Test the Flow:</h3>
                    <ol class="text-purple-800 mt-2 space-y-1">
                        <li>1. Go to the booking page (Home)</li>
                        <li>2. Select route: Tabuk → Baguio</li>
                        <li>3. Pick a date and search for trips</li>
                        <li>4. Complete the booking process</li>
                        <li>5. You'll be redirected to the receipt page automatically</li>
                    </ol>
                </div>
            </div>
            
            <div class="mt-8 text-center">
                <a href="{{ route('home') }}" class="inline-flex items-center px-6 py-3 text-white rounded-lg transition-colors" style="background-color: #FCB404;">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Start Booking Process
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
