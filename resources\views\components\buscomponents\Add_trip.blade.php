@php
    $routes = $data['routes'];
    $buses = $data['buses'];
    $schedules = $data['schedules'];
@endphp

<div class="p-4 bg-white shadow-md rounded-lg">
    <form method="POST" class="flex flex-col gap-4" id="{{ $formid }}">
        @csrf

       
        <div class="flex flex-col gap-2">
            <x-input-label for="routes" :value="__('Select Route')" />
            <select id="routes" name="route_id" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                @foreach ($routes as $route)
                    <option value="{{ $route->id }}">
                        {{ $route->start_point }} to {{ $route->end_point }}
                    </option>
                @endforeach
            </select>
            <x-input-error class="mt-2" :messages="$errors->get('route_id')" />
        </div>

        <div class="flex flex-col gap-2">
            <x-input-label for="buses" :value="__('Select Bus')" />
            <select id="buses" name="bus_id" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                @foreach ($buses as $bus)
                    <option value="{{ $bus->id }}">
                        {{ $bus->name }} - Seats: {{ $bus->seat }}
                    </option>
                @endforeach
            </select>
            <x-input-error class="mt-2" :messages="$errors->get('bus_id')" />
        </div>

        <div class="flex flex-col gap-2">
            <x-input-label for="schedules" :value="__('Select Schedule')" />
            <select id="schedules" name="schedule_id" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                @foreach ($schedules as $schedule)
                    <option value="{{ $schedule->id }}">
                        {{ $schedule->formatted_schedule }}
                    </option>
                @endforeach
            </select>
            <x-input-error class="mt-2" :messages="$errors->get('schedule_id')" />
        </div>

        <div class="flex items-center justify-end mt-4">
            <x-primary-button type="submit">
                {{ __('Submit') }}
            </x-primary-button>
        </div>
    </form>
</div>
