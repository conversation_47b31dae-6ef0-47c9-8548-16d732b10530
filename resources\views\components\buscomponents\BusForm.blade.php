
@php
$data = json_decode($data);
$id=$data[count($data)-1]
@endphp
<div class="p-4 bg-white shadow-md rounded-lg">
    <form  method="POST" class="flex flex-col gap-4" id="{{ $id }}">
        @csrf

     
       @for ($i = 1; $i <= $data[0]; $i++)
       <div class="flex flex-col gap-2">
           <x-input-label for="{{ $data[$i] }}" :value="__($data[$i])" />
   
           <x-text-input 
               id="{{ $data[$i] }}" 
               name="{{ $data[$i] }}" 
               type="text" 
               class="mt-1 block w-full" 
               :value="old($data[$i])" 
                autofocus autocomplete="{{ $data[$i] }}" 
           />
   
           <x-input-error class="mt-2" :messages="$errors->get($data[$i])" />
       </div>
   @endfor
   
      
      
        
        <div class="flex items-center justify-end mt-4" >
            <x-primary-button type="submit" >
                {{ __('Save') }}
            </x-primary-button>
        </div>
    </form>
       
</div>
