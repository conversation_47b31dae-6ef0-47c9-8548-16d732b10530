<div id="seat-selection-modal-{{ $trip->id }}" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-lg relative">
        <button class="absolute top-2 right-2 text-gray-500 hover:text-gray-700 text-2xl" onclick="$('#seat-selection-modal-{{ $trip->id }}').addClass('hidden')">&times;</button>
        <h2 class="text-xl font-bold mb-4">Select Your Seat - {{ $trip->bus->name }}</h2>

        <!-- Bus info -->
        <div class="bg-blue-50 p-3 rounded-lg mb-4">
            <p class="text-sm text-blue-800">
                <strong>Route:</strong> {{ $trip->route->start_point }} → {{ $trip->route->end_point }}<br>
                <strong>Schedule:</strong> {{ $trip->schedule->formatted_schedule }}
            </p>
        </div>

        <!-- Loading indicator -->
        <div id="seat-loading-{{ $trip->id }}" class="text-center py-4 hidden">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p class="mt-2 text-gray-600">Refreshing seat availability...</p>
        </div>

        <!-- Seat map container -->
        <div id="seat-map-{{ $trip->id }}" class="flex flex-col gap-2 mb-4">
            @php $seats = $trip->bus->seat; @endphp
            @for ($row = 0; $row < ceil($seats / 4); $row++)
                <div class="flex gap-2 justify-center items-center">
                    @for ($col = 1; $col <= 2; $col++)
                        @php $seatNum = $row * 4 + $col; @endphp
                        @if ($seatNum <= $seats)
                            @php
                                $isBooked = $trip->tickets()
                                    ->where('seat_number', $seatNum)
                                    ->whereHas('payments', function($q) {
                                        $q->where('status', '!=', 'cancelled');
                                    })
                                    ->exists();
                            @endphp
                            <button type="button" title="Seat {{ $seatNum }}" class="seat-btn w-12 h-12 rounded-lg border-2 font-semibold transition-all duration-200 {{ $isBooked ? 'seat-booked cursor-not-allowed bg-red-500 text-white border-red-600' : 'seat-available hover:seat-selected bg-green-100 text-green-800 border-green-300 hover:bg-yellow-200 hover:border-yellow-400' }}" data-seat="{{ $seatNum }}" {{ $isBooked ? 'disabled' : '' }}>
                                {{ $seatNum }}
                            </button>
                        @else
                            <span class="w-12 h-12"></span>
                        @endif
                    @endfor

                    <!-- Aisle space -->
                    <span class="w-6 text-center text-xs text-gray-400">||</span>

                    @for ($col = 3; $col <= 4; $col++)
                        @php $seatNum = $row * 4 + $col; @endphp
                        @if ($seatNum <= $seats)
                            @php
                                $isBooked = $trip->tickets()
                                    ->where('seat_number', $seatNum)
                                    ->whereHas('payments', function($q) {
                                        $q->where('status', '!=', 'cancelled');
                                    })
                                    ->exists();
                            @endphp
                            <button type="button" title="Seat {{ $seatNum }}" class="seat-btn w-12 h-12 rounded-lg border-2 font-semibold transition-all duration-200 {{ $isBooked ? 'seat-booked cursor-not-allowed bg-red-500 text-white border-red-600' : 'seat-available hover:seat-selected bg-green-100 text-green-800 border-green-300 hover:bg-yellow-200 hover:border-yellow-400' }}" data-seat="{{ $seatNum }}" {{ $isBooked ? 'disabled' : '' }}>
                                {{ $seatNum }}
                            </button>
                        @else
                            <span class="w-12 h-12"></span>
                        @endif
                    @endfor
                </div>
            @endfor
        </div>
        <!-- Selected Seats Display -->
        <div id="selected-seats-display-{{ $trip->id }}" class="mb-4 hidden">
            <h4 class="font-semibold text-gray-800 mb-2">Selected Seats:</h4>
            <div id="selected-seats-list-{{ $trip->id }}" class="flex flex-wrap gap-2"></div>
        </div>

        <form method="POST" action="{{ route('book.seat', $trip->id) }}" id="seat-book-form-{{ $trip->id }}">
            @csrf
            <input type="hidden" name="seat_numbers" id="selected-seats-{{ $trip->id }}">

            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Number of Seats</label>
                <select id="seat-count-{{ $trip->id }}" class="w-full border border-gray-300 rounded-md px-3 py-2 mb-3">
                    <option value="1">1 Seat</option>
                    <option value="2">2 Seats</option>
                    <option value="3">3 Seats</option>
                    <option value="4">4 Seats</option>
                    <option value="5">5 Seats</option>
                </select>
                <p class="text-xs text-gray-500 mb-4">Select how many seats you want to book, then click on the seats in the map above</p>
            </div>

            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                <div class="space-y-2">
                    <!-- QR and e-Wallets Section -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <h4 class="text-sm font-medium text-blue-900 mb-2 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                            QR and e-Wallets
                        </h4>
                        <div class="grid grid-cols-1 gap-2">
                            <!-- Maya -->
                            <label class="flex items-center p-2 border border-gray-200 rounded cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="payment_method_{{ $trip->id }}" value="maya" class="sr-only payment-radio-{{ $trip->id }}">
                                <div class="flex items-center w-full">
                                    <div class="w-6 h-6 bg-green-500 rounded flex items-center justify-center mr-2">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-900">Pay with Maya</div>
                                        <div class="text-xs text-gray-500">wallet • credit</div>
                                    </div>
                                    <div class="w-3 h-3 border border-gray-300 rounded-full payment-indicator-{{ $trip->id }}"></div>
                                </div>
                            </label>

                            <!-- QRPh -->
                            <label class="flex items-center p-2 border border-gray-200 rounded cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="payment_method_{{ $trip->id }}" value="qrph" class="sr-only payment-radio-{{ $trip->id }}">
                                <div class="flex items-center w-full">
                                    <div class="w-6 h-6 bg-blue-500 rounded flex items-center justify-center mr-2">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-900">Pay with QRPh</div>
                                        <div class="text-xs text-gray-500">• other banks</div>
                                    </div>
                                    <div class="w-3 h-3 border border-gray-300 rounded-full payment-indicator-{{ $trip->id }}"></div>
                                </div>
                            </label>

                            <!-- GCash -->
                            <label class="flex items-center p-2 border border-gray-200 rounded cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="payment_method_{{ $trip->id }}" value="gcash" class="sr-only payment-radio-{{ $trip->id }}">
                                <div class="flex items-center w-full">
                                    <div class="w-6 h-6 bg-blue-600 rounded flex items-center justify-center mr-2">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-900">Pay with GCash</div>
                                        <div class="text-xs text-gray-500">Mobile wallet</div>
                                    </div>
                                    <div class="w-3 h-3 border border-gray-300 rounded-full payment-indicator-{{ $trip->id }}"></div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Hidden select for backward compatibility -->
                <select name="payment_method" id="payment-method-{{ $trip->id }}" class="hidden" required>
                    <option value="">Select Payment Method</option>
                    <option value="maya">Maya</option>
                    <option value="qrph">QRPh</option>
                    <option value="gcash">GCash</option>
                </select>
            </div>

            <div id="payment-details-{{ $trip->id }}" class="mt-3 hidden">
                <!-- GL Bus Account Details -->
                <div id="payment-account-{{ $trip->id }}" class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                    <h4 class="font-medium text-blue-900 mb-2 flex items-center text-sm">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Send Payment To:
                    </h4>
                    <div id="account-info-{{ $trip->id }}" class="text-xs space-y-1">
                        <!-- Account details will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Payment Reference Input -->
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Payment Reference Number
                    <span class="text-gray-500">(Optional for cash payments)</span>
                </label>
                <input type="text" name="payment_reference" id="payment-reference-{{ $trip->id }}"
                       class="w-full border border-gray-300 rounded-md px-3 py-2"
                       placeholder="Enter payment reference number (optional)">
                <p class="text-xs text-gray-500 mt-1">
                    <strong>Note:</strong> For digital payments, you'll be redirected to the payment gateway. Reference number is optional for cash payments.
                </p>
            </div>

            <div class="mt-4 p-3 bg-gray-50 rounded-md">
                <div class="flex justify-between text-sm">
                    <span>Price per seat:</span>
                    <span class="base-price-{{ $trip->id }}">₱100.00</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span>Selected seats:</span>
                    <span id="selected-count-{{ $trip->id }}">0</span>
                </div>
                <div class="flex justify-between font-semibold text-lg border-t pt-2 mt-2">
                    <span>Total:</span>
                    <span class="total-price-{{ $trip->id }}">₱0.00</span>
                </div>
            </div>
            
            <div class="flex gap-2 mt-4">
                <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 rounded" disabled id="confirm-seat-btn-{{ $trip->id }}">Confirm Booking</button>
                <button type="button" class="bg-purple-600 hover:bg-purple-700 text-white py-2 px-2 rounded text-xs" onclick="forceTest({{ $trip->id }})">Force</button>
                <button type="button" class="bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded text-sm" onclick="testBooking({{ $trip->id }})">Test</button>
                <button type="button" class="bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-3 rounded text-sm" onclick="debugBooking({{ $trip->id }})">Debug</button>
            </div>
        </form>
        <!-- Legend -->
        <div class="mt-4 flex gap-4 text-sm justify-center">
            <div class="flex items-center gap-1">
                <span class="w-4 h-4 bg-green-100 border-2 border-green-300 rounded inline-block"></span>
                <span>Available</span>
            </div>
            <div class="flex items-center gap-1">
                <span class="w-4 h-4 bg-yellow-200 border-2 border-yellow-400 rounded inline-block"></span>
                <span>Selected</span>
            </div>
            <div class="flex items-center gap-1">
                <span class="w-4 h-4 bg-red-500 border-2 border-red-600 rounded inline-block"></span>
                <span>Booked</span>
            </div>
        </div>

        <!-- Refresh button -->
        <button type="button" id="refresh-seats-{{ $trip->id }}" class="mt-2 text-blue-600 hover:text-blue-800 text-sm underline">
            🔄 Refresh Seat Availability
        </button>
    </div>
</div>

<style>
.seat-selected {
    background-color: #fef3c7 !important;
    border-color: #f59e0b !important;
    color: #92400e !important;
}
</style>

<script>
$(function() {
    // Make variables global for this trip
    window['selectedSeats_{{ $trip->id }}'] = [];
    window['maxSeats_{{ $trip->id }}'] = 1;
    window['seatPrice_{{ $trip->id }}'] = 100; // Default price, will be updated from server

    let selectedSeats = window['selectedSeats_{{ $trip->id }}'];
    let maxSeats = window['maxSeats_{{ $trip->id }}'];
    let seatPrice = window['seatPrice_{{ $trip->id }}'];

    // Get fare information for this route
    $.get('{{ route("fares.route", $trip->route_id) }}')
        .done(function(data) {
            if (data.success) {
                seatPrice = parseFloat(data.fare.base_fare);
                updatePriceDisplay();
            }
        })
        .fail(function() {
            console.log('Using default price');
        });

    // Function to refresh seat availability
    function refreshSeats() {
        $('#seat-loading-{{ $trip->id }}').removeClass('hidden');
        $.get('{{ route("available.seats", $trip->id) }}')
            .done(function(data) {
                // Update seat buttons based on current availability
                $('#seat-selection-modal-{{ $trip->id }} .seat-btn').each(function() {
                    const seatNum = $(this).data('seat');
                    const isBooked = data.booked_seats.includes(seatNum);

                    if (isBooked) {
                        $(this).removeClass('seat-available hover:seat-selected bg-green-100 text-green-800 border-green-300 hover:bg-yellow-200 hover:border-yellow-400')
                               .addClass('seat-booked cursor-not-allowed bg-red-500 text-white border-red-600')
                               .prop('disabled', true);
                    } else {
                        $(this).removeClass('seat-booked cursor-not-allowed bg-red-500 text-white border-red-600')
                               .addClass('seat-available hover:seat-selected bg-green-100 text-green-800 border-green-300 hover:bg-yellow-200 hover:border-yellow-400')
                               .prop('disabled', false);
                    }
                });

                // Update seats left counter
                $("#seats-left{{ $trip->id }}").text(data.seats_left + ' seats left');
            })
            .fail(function() {
                alert('Failed to refresh seat availability. Please try again.');
            })
            .always(function() {
                $('#seat-loading-{{ $trip->id }}').addClass('hidden');
            });
    }

    // Update price display
    function updatePriceDisplay() {
        const totalPrice = selectedSeats.length * seatPrice;
        $('.base-price-{{ $trip->id }}').text('₱' + seatPrice.toFixed(2));
        $('.total-price-{{ $trip->id }}').text('₱' + totalPrice.toFixed(2));
    }

    // Update selected seats display
    function updateSelectedSeatsDisplay() {
        const display = $('#selected-seats-display-{{ $trip->id }}');
        const list = $('#selected-seats-list-{{ $trip->id }}');

        console.log('Updating display with seats:', selectedSeats);

        if (selectedSeats.length > 0) {
            display.removeClass('hidden');
            list.empty();
            selectedSeats.forEach(seat => {
                list.append(`<span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">Seat ${seat}</span>`);
            });
        } else {
            display.addClass('hidden');
        }

        // Force update the hidden input
        const seatInput = $('#selected-seats-{{ $trip->id }}');
        const seatValue = selectedSeats.join(',');
        seatInput.val(seatValue);

        console.log('Hidden input value set to:', seatValue);
        console.log('Hidden input actual value:', seatInput.val());

        $('#selected-count-{{ $trip->id }}').text(selectedSeats.length);
        $('#confirm-seat-btn-{{ $trip->id }}').prop('disabled', selectedSeats.length === 0);
        updatePriceDisplay();
    }

    // Seat count change handler
    $('#seat-count-{{ $trip->id }}').on('change', function() {
        maxSeats = parseInt($(this).val());
        window['maxSeats_{{ $trip->id }}'] = maxSeats;
        // Clear selection if exceeds new limit
        if (selectedSeats.length > maxSeats) {
            // Remove excess selections
            const excessSeats = selectedSeats.splice(maxSeats);
            excessSeats.forEach(seat => {
                $(`#seat-selection-modal-{{ $trip->id }} .seat-btn[data-seat="${seat}"]`).removeClass('seat-selected');
            });
            window['selectedSeats_{{ $trip->id }}'] = selectedSeats;
            updateSelectedSeatsDisplay();
        }
    });

    // Refresh seats button
    $('#refresh-seats-{{ $trip->id }}').on('click', refreshSeats);

    // Seat selection
    $('#seat-selection-modal-{{ $trip->id }} .seat-btn').on('click', function() {
        console.log('Seat clicked:', $(this).data('seat'));

        if ($(this).hasClass('seat-booked')) {
            console.log('Seat is booked, cannot select');
            return;
        }

        const seatNum = $(this).data('seat');
        const isSelected = $(this).hasClass('seat-selected');

        console.log('Seat number:', seatNum, 'Is selected:', isSelected, 'Max seats:', maxSeats);

        if (isSelected) {
            // Deselect seat
            console.log('Deselecting seat:', seatNum);
            $(this).removeClass('seat-selected');
            selectedSeats = selectedSeats.filter(seat => seat !== seatNum);
            window['selectedSeats_{{ $trip->id }}'] = selectedSeats;
        } else {
            // Select seat if under limit
            if (selectedSeats.length < maxSeats) {
                console.log('Selecting seat:', seatNum);
                $(this).addClass('seat-selected');
                selectedSeats.push(seatNum);
                window['selectedSeats_{{ $trip->id }}'] = selectedSeats;
            } else {
                console.log('Seat limit reached:', maxSeats);
                Swal.fire({
                    icon: 'warning',
                    title: 'Seat Limit Reached',
                    text: `You can only select ${maxSeats} seat(s). Please deselect a seat first or change the number of seats.`,
                    confirmButtonColor: '#3b82f6'
                });
                return;
            }
        }

        console.log('Selected seats after click:', selectedSeats);
        console.log('Global selectedSeats:', window['selectedSeats_{{ $trip->id }}']);
        updateSelectedSeatsDisplay();
    });
    // Payment method radio button handlers
    $('.payment-radio-{{ $trip->id }}').on('change', function() {
        const method = $(this).val();

        // Update hidden select for backward compatibility
        $('#payment-method-{{ $trip->id }}').val(method);

        // Update visual indicators
        $('.payment-indicator-{{ $trip->id }}').removeClass('bg-blue-500 border-blue-500').addClass('border-gray-300').empty();

        // Mark selected option
        $(this).closest('label').find('.payment-indicator-{{ $trip->id }}')
            .removeClass('border-gray-300')
            .addClass('bg-blue-500 border-blue-500')
            .html('<div class="w-1 h-1 bg-white rounded-full mx-auto"></div>');

        // Show/hide payment details and account info for digital payments
        if (method === 'gcash' || method === 'maya' || method === 'qrph') {
            $('#payment-details-{{ $trip->id }}').removeClass('hidden');
            // For gateway payments, hide reference input since payment will be processed through gateway
            $('#payment-reference-{{ $trip->id }}').closest('div').addClass('hidden');
            $('#payment-reference-{{ $trip->id }}').prop('required', false);

            // Show account details based on payment method
            let accountInfo = '';
            if (method === 'gcash') {
                accountInfo = `
                    <div class="bg-white p-2 rounded border">
                        <div class="flex items-center mb-1">
                            <div class="w-4 h-4 bg-blue-600 rounded flex items-center justify-center mr-1">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <strong class="text-blue-900 text-xs">GCash Account</strong>
                        </div>
                        <div class="text-xs space-y-1">
                            <div><strong>Number:</strong> ***********</div>
                            <div><strong>Name:</strong> GL Bus Transport Corp</div>
                        </div>
                    </div>
                `;
            } else if (method === 'maya') {
                accountInfo = `
                    <div class="bg-white p-2 rounded border">
                        <div class="flex items-center mb-1">
                            <div class="w-4 h-4 bg-green-500 rounded flex items-center justify-center mr-1">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                            </div>
                            <strong class="text-green-900 text-xs">Maya Account</strong>
                        </div>
                        <div class="text-xs space-y-1">
                            <div><strong>Number:</strong> ***********</div>
                            <div><strong>Name:</strong> GL Bus Transport Corp</div>
                        </div>
                    </div>
                `;
            } else if (method === 'qrph') {
                accountInfo = `
                    <div class="bg-white p-2 rounded border">
                        <div class="flex items-center mb-1">
                            <div class="w-4 h-4 bg-purple-500 rounded flex items-center justify-center mr-1">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
                                </svg>
                            </div>
                            <strong class="text-purple-900 text-xs">QRPh Payment</strong>
                        </div>
                        <div class="text-xs space-y-1">
                            <div><strong>Account:</strong> GL Bus Transport Corp</div>
                            <div><strong>Bank:</strong> BPI - **********</div>
                        </div>
                    </div>
                `;
            }
            $(`#account-info-{{ $trip->id }}`).html(accountInfo);
        } else if (method === 'cash') {
            $('#payment-details-{{ $trip->id }}').removeClass('hidden');
            // For cash payments, show reference input (optional)
            $('#payment-reference-{{ $trip->id }}').closest('div').removeClass('hidden');
            $('#payment-reference-{{ $trip->id }}').attr('placeholder', 'Enter cash payment reference (optional)');
            $('#payment-reference-{{ $trip->id }}').prop('required', false);

            // Show cash payment info
            $(`#account-info-{{ $trip->id }}`).html(`
                <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                    <div class="flex items-center mb-2">
                        <div class="w-4 h-4 bg-green-600 rounded flex items-center justify-center mr-1">
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <strong class="text-green-900 text-xs">Cash Payment</strong>
                    </div>
                    <div class="text-xs text-green-800">
                        <p>Pay in cash at the terminal or to the conductor.</p>
                        <p class="mt-1"><strong>Note:</strong> Please arrive early for cash payments.</p>
                    </div>
                </div>
            `);
        } else {
            $('#payment-details-{{ $trip->id }}').addClass('hidden');
            $('#payment-reference-{{ $trip->id }}').prop('required', false);
        }
    });

    // Form submission
    $('#seat-book-form-{{ $trip->id }}').on('submit', function(e) {
        e.preventDefault();

        console.log('Form submission started');
        console.log('User authenticated:', {{ Auth::check() ? 'true' : 'false' }});
        console.log('User ID:', {{ Auth::id() ?? 'null' }});

        if (selectedSeats.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'No Seats Selected',
                text: 'Please select at least one seat before booking.',
                confirmButtonColor: '#3b82f6'
            });
            return;
        }

        const paymentMethod = $('#payment-method-{{ $trip->id }}').val();
        if (!paymentMethod) {
            Swal.fire({
                icon: 'warning',
                title: 'Payment Method Required',
                text: 'Please select a payment method.',
                confirmButtonColor: '#3b82f6'
            });
            return;
        }

        // Disable submit button to prevent double submission
        $('#confirm-seat-btn-{{ $trip->id }}').prop('disabled', true).text('Processing...');

        var form = $(this);
        var formData = form.serialize();
        console.log('Form data:', formData);
        console.log('Form action:', form.attr('action'));
        console.log('Selected seats:', selectedSeats);

        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: formData,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            beforeSend: function() {
                console.log('AJAX request starting...');
            },
            success: function(response) {
                console.log('AJAX Success:', response);
                $('#seat-selection-modal-{{ $trip->id }}').addClass('hidden');

                const seatsText = response.seats_booked.length > 1
                    ? `Seats ${response.seats_booked.join(', ')} booked successfully!`
                    : `Seat ${response.seats_booked[0]} booked successfully!`;

                // Check if payment gateway redirect is required
                if (response.payment_gateway && response.payment_gateway.redirect_required) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Booking Created!',
                        html: `
                            <p><strong>${seatsText}</strong></p>
                            <p>Total Amount: <strong>₱${response.total_amount.toFixed(2)}</strong></p>
                            <p>Payment Method: <strong>${paymentMethod.toUpperCase()}</strong></p>
                            <p class="text-sm text-blue-600 mt-2">Redirecting to payment gateway...</p>
                        `,
                        confirmButtonColor: '#10b981',
                        timer: 2000,
                        timerProgressBar: true
                    }).then(() => {
                        // Redirect to payment gateway
                        window.location.href = response.redirect_url;
                    });
                } else if (response.payment_gateway && response.payment_gateway.show_qr) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Booking Created!',
                        html: `
                            <p><strong>${seatsText}</strong></p>
                            <p>Total Amount: <strong>₱${response.total_amount.toFixed(2)}</strong></p>
                            <p>Payment Method: <strong>${paymentMethod.toUpperCase()}</strong></p>
                            <p class="text-sm text-blue-600 mt-2">Please scan the QR code to complete payment</p>
                        `,
                        confirmButtonColor: '#10b981'
                    }).then(() => {
                        // Redirect to QR payment page
                        window.location.href = response.redirect_url || `/booking-confirmation/${response.booking_id}`;
                    });
                } else {
                    // Regular booking success (cash payments)
                    Swal.fire({
                        icon: 'success',
                        title: 'Booking Successful!',
                        html: `
                            <p><strong>${seatsText}</strong></p>
                            <p>Total Amount: <strong>₱${response.total_amount.toFixed(2)}</strong></p>
                            <p>Payment Method: <strong>${paymentMethod.toUpperCase()}</strong></p>
                            <p class="text-sm text-gray-600 mt-2">Invoice numbers: ${response.invoice_numbers.join(', ')}</p>
                            <p class="text-sm text-gray-600">Please save your invoice numbers for reference.</p>
                        `,
                        confirmButtonColor: '#10b981'
                    }).then(() => {
                        // Update seats left and reload to show updated seat map
                        $("#seats-left{{ $trip->id }}").text(response.seats_left + ' seats left');
                        location.reload();
                    });
                }
            },
            error: function(xhr) {
                console.log('=== BOOKING ERROR DETAILS ===');
                console.log('AJAX Error:', xhr);
                console.log('Status:', xhr.status);
                console.log('Status Text:', xhr.statusText);
                console.log('Response Text:', xhr.responseText);
                console.log('Response JSON:', xhr.responseJSON);
                console.log('Ready State:', xhr.readyState);
                console.log('=== END ERROR DETAILS ===');

                let errorMessage = 'An error occurred while booking the seat.';

                if (xhr.status === 409 && xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                } else if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                    // Validation errors
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                } else if (xhr.status === 500) {
                    errorMessage = 'Server error occurred. Please try again. Check console for details.';
                } else if (xhr.status === 0) {
                    errorMessage = 'Network error. Please check your connection.';
                } else if (xhr.status === 419) {
                    errorMessage = 'CSRF token mismatch. Please refresh the page.';
                } else if (xhr.status === 401) {
                    errorMessage = 'Authentication required. Please login again.';
                } else if (xhr.status === 403) {
                    errorMessage = 'Access forbidden. Check permissions.';
                } else {
                    errorMessage = `HTTP ${xhr.status}: ${xhr.statusText || 'Unknown error'}`;
                }

                Swal.fire({
                    icon: 'error',
                    title: 'Booking Failed',
                    text: errorMessage,
                    confirmButtonColor: '#ef4444'
                });

                // Refresh seats to show current availability
                refreshSeats();
            },
            complete: function() {
                // Re-enable submit button
                $('#confirm-seat-btn-{{ $trip->id }}').prop('disabled', false).text('Confirm Booking');
            }
        });
    });

    // Force test function - manually set form values
    window.forceTest = function(tripId) {
        console.log('Force test - manually setting form values');

        // Manually set form values
        $('#selected-seats-{{ $trip->id }}').val('1,2');
        $('#payment-method-{{ $trip->id }}').val('cash');

        console.log('Forced seat numbers:', $('#selected-seats-{{ $trip->id }}').val());
        console.log('Forced payment method:', $('#payment-method-{{ $trip->id }}').val());
        console.log('Form serialized after force:', $('#seat-book-form-{{ $trip->id }}').serialize());

        // Enable the confirm button
        $('#confirm-seat-btn-{{ $trip->id }}').prop('disabled', false);

        Swal.fire({
            icon: 'info',
            title: 'Force Test Complete',
            text: 'Form values manually set. Check console and try booking now.',
            confirmButtonColor: '#3b82f6'
        });
    };

    // Test function
    window.testBooking = function(tripId) {
        console.log('Test booking for trip:', tripId);

        $.ajax({
            url: `/test-booking/${tripId}`,
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                console.log('Test response:', response);
                Swal.fire({
                    icon: 'success',
                    title: 'Test Successful!',
                    text: response.message,
                    confirmButtonColor: '#10b981'
                });
            },
            error: function(xhr) {
                console.log('Test error:', xhr);
                Swal.fire({
                    icon: 'error',
                    title: 'Test Failed!',
                    text: xhr.responseJSON?.error || 'Test failed',
                    confirmButtonColor: '#ef4444'
                });
            }
        });
    };

    // Debug function
    window.debugBooking = function(tripId) {
        console.log('Debug booking for trip:', tripId);
        console.log('Selected seats array (local):', selectedSeats);
        console.log('Selected seats array (global):', window['selectedSeats_{{ $trip->id }}']);
        console.log('Payment method value:', $('#payment-method-{{ $trip->id }}').val());
        console.log('Form serialized:', $('#seat-book-form-{{ $trip->id }}').serialize());

        // Use the actual form data like the real booking does
        var formData = $('#seat-book-form-{{ $trip->id }}').serialize();

        $.ajax({
            url: `/debug-booking/${tripId}`,
            method: 'POST',
            data: formData,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                console.log('Debug response:', response);
                alert('Debug info logged to console');
            },
            error: function(xhr) {
                console.log('Debug error:', xhr);
                alert('Debug error logged to console');
            }
        });
    };
});
</script>
