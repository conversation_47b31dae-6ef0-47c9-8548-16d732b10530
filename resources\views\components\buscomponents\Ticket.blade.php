@php
    $payment = $ticket->payments->first();
    $paymentStatus = $payment ? $payment->status : 'pending';
    $invoiceNumber = $payment ? $payment->invoice_number : null;
@endphp

<div class="bg-white shadow-md rounded-lg p-4 border-l-4 {{ $paymentStatus === 'paid' ? 'border-green-500' : ($paymentStatus === 'cancelled' ? 'border-red-500' : 'border-yellow-500') }}" id="ticket{{ $ticket->id }}">
    <!-- Payment Status Badge -->
    <div class="flex justify-between items-start mb-3">
        <div class="flex items-center gap-2">
            <span class="px-2 py-1 text-xs font-semibold rounded-full {{ $paymentStatus === 'paid' ? 'bg-green-100 text-green-800' : ($paymentStatus === 'cancelled' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800') }}">
                {{ strtoupper($paymentStatus) }}
            </span>
            @if($invoiceNumber)
                <span class="text-xs text-gray-500">{{ $invoiceNumber }}</span>
            @endif
        </div>
        @if($ticket->seat_number)
            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded">
                Seat {{ $ticket->seat_number }}
            </span>
        @endif
    </div>

    <!-- Ticket Details -->
    <div class="space-y-2 mb-4">
        <p>
            <span class="font-semibold text-gray-700">Bus:</span>
            <span class="text-gray-900">{{ $ticket->trip->bus->name }}</span>
        </p>
        <p>
            <span class="font-semibold text-gray-700">Route:</span>
            <span class="text-gray-900">{{ $ticket->trip->route->start_point }} → {{ $ticket->trip->route->end_point }}</span>
        </p>
        <p>
            <span class="font-semibold text-gray-700">Schedule:</span>
            <span class="text-gray-900">{{ $ticket->trip->schedule->formatted_schedule }}</span>
        </p>
        <p>
            <span class="font-semibold text-gray-700">Price:</span>
            <span class="text-lg font-bold text-green-600">₱{{ number_format($ticket->price, 2) }}</span>
        </p>
        @if($payment && $payment->payment_method)
            <p>
                <span class="font-semibold text-gray-700">Payment Method:</span>
                <span class="text-gray-900">{{ strtoupper($payment->payment_method) }}</span>
            </p>
        @endif
        @if($payment && $payment->payment_date)
            <p>
                <span class="font-semibold text-gray-700">Payment Date:</span>
                <span class="text-gray-900">{{ $payment->payment_date->format('M d, Y g:i A') }}</span>
            </p>
        @endif
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-wrap gap-2">
        @if($paymentStatus === 'pending')
            <button class="px-3 py-2 bg-red-500 hover:bg-red-600 text-white text-sm rounded-lg transition-colors" id="cancel-payment{{ $ticket->id }}">
                Cancel Booking
            </button>
            <button class="px-3 py-2 bg-green-500 hover:bg-green-600 text-white text-sm rounded-lg transition-colors" id="complete-payment{{ $ticket->id }}">
                Complete Payment
            </button>
        @endif

        @if($invoiceNumber)
            <button class="px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-lg transition-colors" id="view-invoice{{ $ticket->id }}">
                📄 View Invoice
            </button>
        @endif

        @if($paymentStatus === 'paid')
            <button class="px-3 py-2 bg-purple-500 hover:bg-purple-600 text-white text-sm rounded-lg transition-colors" id="download-receipt{{ $ticket->id }}">
                📥 Download Receipt
            </button>
        @endif
    </div>
</div>

<script>
$(document).ready(function() {
    // Cancel payment
    $('#cancel-payment{{ $ticket->id }}').click(function() {
        Swal.fire({
            title: 'Cancel Booking?',
            text: 'Are you sure you want to cancel this booking? This action cannot be undone.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, cancel it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '{{ route('cancel.ticket', $ticket) }}',
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        $('#ticket{{ $ticket->id }}').fadeOut(300, function() {
                            $(this).remove();
                        });
                        Swal.fire({
                            icon: 'success',
                            title: 'Cancelled!',
                            text: response.message || 'Your booking has been cancelled.',
                            confirmButtonColor: '#10b981'
                        });
                    },
                    error: function(xhr, status, error) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'An error occurred while cancelling your booking.',
                            confirmButtonColor: '#ef4444'
                        });
                    }
                });
            }
        });
    });

    // Complete payment
    $('#complete-payment{{ $ticket->id }}').click(function() {
        Swal.fire({
            title: 'Complete Payment',
            text: 'Confirm that you have completed the payment for this booking.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#10b981',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, payment completed!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '{{ route('complete.payment', $ticket) }}',
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Payment Confirmed!',
                            text: response.message || 'Your payment has been confirmed.',
                            confirmButtonColor: '#10b981'
                        }).then(() => {
                            location.reload(); // Reload to show updated ticket status
                        });
                    },
                    error: function(xhr, status, error) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'An error occurred while processing your payment.',
                            confirmButtonColor: '#ef4444'
                        });
                    }
                });
            }
        });
    });

    // View invoice
    $('#view-invoice{{ $ticket->id }}').click(function() {
        window.open('{{ route('view.invoice', $ticket) }}', '_blank');
    });

    // Download receipt
    $('#download-receipt{{ $ticket->id }}').click(function() {
        $.get('{{ route('get.receipt', $ticket) }}')
            .done(function(response) {
                if (response.success) {
                    const receipt = response.receipt;
                    Swal.fire({
                        title: 'Payment Receipt',
                        html: `
                            <div class="text-left">
                                <p><strong>Invoice:</strong> ${receipt.invoice_number}</p>
                                <p><strong>Passenger:</strong> ${receipt.passenger_name}</p>
                                <p><strong>Bus:</strong> ${receipt.bus_name}</p>
                                <p><strong>Route:</strong> ${receipt.route}</p>
                                <p><strong>Schedule:</strong> ${receipt.schedule}</p>
                                <p><strong>Seat:</strong> ${receipt.seat_number}</p>
                                <p><strong>Amount:</strong> ₱${parseFloat(receipt.amount).toFixed(2)}</p>
                                <p><strong>Payment Method:</strong> ${receipt.payment_method.toUpperCase()}</p>
                                <p><strong>Payment Date:</strong> ${new Date(receipt.payment_date).toLocaleDateString()}</p>
                            </div>
                        `,
                        confirmButtonText: 'Print Receipt',
                        showCancelButton: true,
                        cancelButtonText: 'Close',
                        confirmButtonColor: '#3b82f6'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.open('{{ route('view.invoice', $ticket) }}', '_blank');
                        }
                    });
                }
            })
            .fail(function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Unable to load receipt. Please try again.',
                    confirmButtonColor: '#ef4444'
                });
            });
    });
});

                
</script>