<style>
    #bookingModal{{ $trip->id }} {
    z-index: 9999!important; 
    background-color: rgba(0, 0, 0, 0.5); 
}
#updateModal{{ $trip->id }}{
    z-index: 9999!important; 
    background-color: rgba(0, 0, 0, 0.5); 
}
#map-{{ $trip->id }} {
    z-index: 1!important; 

}


 .leaflet-routing-container {
    display: none !important;
  
}

</style>

<div class="bg-white shadow-lg rounded-xl p-6 relative border border-gray-200 hover:shadow-xl transition-all duration-300" id="trip{{ $trip->id }}">
    @can('admin')
    <div class="absolute top-4 right-4 flex space-x-2">
        <button class="p-2 text-yellow-600 hover:text-yellow-800 hover:bg-yellow-50 rounded-lg transition-all" id="update-trip{{ $trip->id }}" title="Edit Trip">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
        </button>
        <button class="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-all" id="delete-trip{{ $trip->id }}" title="Delete Trip">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    </div>
    @endcan

    <!-- Bus Header -->
    <div class="flex items-center mb-4">
        <div class="p-3 bg-blue-100 rounded-lg">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"></path>
            </svg>
        </div>
        <div class="ml-4 flex-1">
            <div class="flex items-center gap-3 mb-1">
                <h3 class="text-xl font-bold text-gray-900" id="bus-name{{ $trip->id }}">{{ $trip->bus->name }}</h3>
                @if($trip->bus->bus_code)
                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full">{{ $trip->bus->bus_code }}</span>
                @endif
            </div>
            <p class="text-gray-600 text-sm">{{ $trip->bus->seat }} seats available</p>
            <div class="flex items-center gap-4 mt-2 text-sm text-gray-500">
                @if($trip->bus->driver_name)
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        Driver: {{ $trip->bus->driver_name }}
                    </div>
                @endif
                @if($trip->bus->conductor_name)
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        Conductor: {{ $trip->bus->conductor_name }}
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Route Information -->
    <div class="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-4 mb-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="text-center">
                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <p class="text-sm font-semibold text-gray-800 mt-1">{{ $trip->route->start_point }}</p>
                </div>
                <div class="flex-1 flex items-center">
                    <div class="h-0.5 bg-gradient-to-r from-blue-500 to-green-500 flex-1"></div>
                    <svg class="w-5 h-5 text-gray-400 mx-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"></path>
                    </svg>
                    <div class="h-0.5 bg-gradient-to-r from-blue-500 to-green-500 flex-1"></div>
                </div>
                <div class="text-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <p class="text-sm font-semibold text-gray-800 mt-1">{{ $trip->route->end_point }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Schedule and Availability -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div class="bg-gray-50 rounded-lg p-3">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                    <p class="text-xs text-gray-500">Schedule</p>
                    <p class="font-semibold text-gray-800" id="schedule{{ $trip->id }}">
                        @if($trip->schedule->trip_date)
                            {{ $trip->schedule->full_schedule }}
                        @else
                            {{ $trip->schedule->formatted_schedule }}
                        @endif
                    </p>
                </div>
            </div>
        </div>
        <div class="bg-green-50 rounded-lg p-3">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <div>
                    <p class="text-xs text-gray-500">Available Seats</p>
                    <p class="font-bold text-green-600" id="seats-left{{ $trip->id }}">
                        {{ $trip->bus->seat - $trip->tickets()->CountTicket($trip->id) }} seats left
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Map -->
    <div class="mb-4">
        <div id="map-{{ $trip->id }}" class="w-full h-48 rounded-lg border border-gray-200"></div>
        <div class="flex justify-between items-center mt-2">
            <p class="text-sm text-gray-600 flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                </svg>
                Distance: <span id="distance-{{ $trip->id }}" class="font-medium">Calculating...</span>
            </p>
            <p class="text-sm text-gray-600 flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
                Fare: <span id="priceValue{{ $trip->id }}" class="font-medium text-green-600">₱150.00</span>
            </p>
        </div>
    </div>

    <!-- Book Now Button -->
    <button class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
      id="book-now{{ $trip->id }}"
      onclick="$('#seat-selection-modal-{{ $trip->id }}').removeClass('hidden')">
      <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5zM5 14a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1v-3a2 2 0 00-2-2H5z"></path>
      </svg>
      Book Your Seat Now
    </button>

    @include('components.buscomponents.SeatSelection', ['trip' => $trip])
</div>



<!-- Removed old Confirm Booking modal. Seat selection modal is now the only booking method. -->
<script>
$(function() {
    // After seat booking, reload to update seat status
    $(document).on('submit', '[id^=seat-book-form-]', function(e) {
        var form = this;
        setTimeout(function() {
            window.location.reload();
        }, 800); // Give backend time to process
    });
});
</script>
@can('admin')
<div id="updateModal{{ $trip->id }}" class="fixed inset-0 bg-gray-500 bg-opacity-50 flex items-center justify-center hidden">
    <div class="bg-white p-6 rounded-lg w-1/3">
        <h3 class="text-xl font-semibold mb-4">Update Trip Details</h3>
        
        <form id="update-trip-form{{ $trip->id }}">
            <div class="mb-4">
                <label for="bus-name{{ $trip->id }}" class="block text-sm font-medium text-gray-700">Bus Name</label>
                <select id="Selectbus-name{{ $trip->id }}" class="mt-1 block w-full border border-gray-300 rounded-lg p-2">
                    @foreach ($busOptions as $bus)
                        <option value="{{ $bus->id }}" {{ $bus->id == $trip->bus->id ? 'selected' : '' }}>
                            {{ $bus->name }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div class="mb-4">
                <label for="route{{ $trip->id }}" class="block text-sm font-medium text-gray-700">Route</label>
                <select id="Selectroute{{ $trip->id }}" class="mt-1 block w-full border border-gray-300 rounded-lg p-2">
                    @foreach ($routeOptions as $route)
                        <option value="{{ $route->id }}" {{ $route->id == $trip->route->id ? 'selected' : '' }}>
                            {{ $route->start_point }} → {{ $route->end_point }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div class="mb-4">
                <label for="schedule{{ $trip->id }}" class="block text-sm font-medium text-gray-700">Schedule</label>
                <select id="Selectschedule{{ $trip->id }}" class="mt-1 block w-full border border-gray-300 rounded-lg p-2">
                    @foreach ($scheduleOptions as $schedule)
                        <option value="{{ $schedule->id }}" {{ $schedule->id == $trip->schedule->id ? 'selected' : '' }}>
                            {{ $schedule->formatted_schedule }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div class="flex space-x-2">
                <button type="button" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700" id="submit-update{{ $trip->id }}">Update Trip</button>
                <button type="button" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700" id="close-update-modal{{ $trip->id }}">Cancel</button>
            </div>
        </form>
    </div>
</div>
@endcan

<script>

    $(document).ready(function () {
        $("#book-now{{ $trip->id }}").on("click", function () {
            $("#bookingModal{{ $trip->id }}").removeClass("hidden");
          
            setTimeout(() => {
                initializeMap("modal-map-{{ $trip->id }}");
            }, 500);
        });

        $("#cancelModal{{ $trip->id }}").on("click", function () {
            $("#bookingModal{{ $trip->id }}").addClass("hidden");
        });

        $('#update-trip{{ $trip->id }}').click(function() {
            $('#updateModal{{ $trip->id }}').removeClass('hidden'); 
        });

        $('#close-update-modal{{ $trip->id }}').click(function() {
            $('#updateModal{{ $trip->id }}').addClass('hidden'); 
        });

        $("#confirmBooking{{ $trip->id }}").on("click", function () {
            console.log({{ $trip->id }}, $("#priceValue{{ $trip->id }}").text());
            $.ajax({
                url: "{{ route('book.trip') }}",
                type: "POST",
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                data: {
                    'trip_id': {{ $trip->id }},
                    'user_id': "{{ auth()->user()->id }}",
                    'price': parseFloat($("#priceValue{{ $trip->id }}").text())
                },
                success: function (response) {
                    showAlert('success', 'Success', response.message);
                        if (window.location.href.includes('searchResult')) {
                                window.location.href = '/';
                            }
                    $("#bookingModal{{ $trip->id }}").addClass("hidden");
                    const ticketHTML = `
                        <div class="bg-white shadow-md rounded-lg p-4" id="ticket${response.data.id}">
                            <p><span class="font-semibold">Bus:</span> ${response.data.trip.bus.name}</p>
                            <p><span class="font-semibold">Route:</span> ${response.data.trip.route.start_point} → ${response.data.trip.route.end_point}</p>
                            <p><span class="font-semibold">Schedule:</span> ${response.data.trip.schedule.start_time} - ${response.data.trip.schedule.end_time}</p>
                            <p><span class="font-semibold">Price:</span> $${response.data.price}</p>
                            <div class="mt-4 flex space-x-2">
                                <button class="px-4 py-2 bg-red-500 text-white rounded-lg cancel-payment" data-id="${response.data.id}">Cancel</button>
                                <button class="px-4 py-2 bg-green-500 text-white rounded-lg complete-payment" data-id="${response.data.id}">Complete Payment</button>
                            </div>
                        </div>
                    `;
                    $("#ticket-list").append(ticketHTML);

                    $(document).on("click", ".cancel-payment", function() {
                        const ticketId = $(this).data('id'); 
                        $.ajax({
                            url: `cancel_ticket/${ticketId}`,
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            data: {
                                'ticket_id': ticketId
                            },
                            success: function(response) {
                                $(`#ticket${ticketId}`).remove(); 
                                showAlert('success', 'Success', response.message);
                            },
                            error: function(xhr, status, error) {
                                showAlert('error', 'Error', 'An error occurred while processing your request.');
                            }
                        });
                    });

                    $(document).on("click", ".complete-payment", function() {
                        const ticketId = $(this).data('id'); 
                        $.ajax({
                            url: `complete-payment/${ticketId}`,
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            data: {
                                'ticket_id': ticketId
                            },
                            success: function(response) {
                                $(`#ticket${ticketId}`).remove(); 
                                showAlert('success', 'Success', response.message);
                            },
                            error: function(xhr, status, error) {
                                showAlert('error', 'Error', 'An error occurred while processing your request.');
                            }
                        });
                    });
                },
                error: function (xhr) {
                    $("#bookingModal{{ $trip->id }}").addClass("hidden");
                    showAlert('error', 'Error', xhr.responseJSON.message);
                    if (window.location.href.includes('searchResult')) {
                                window.location.href = '{{ route('home') }}';
                            }
                }
            });
        });
        $("#delete-trip{{ $trip->id }}").on("click", function () {
            $.ajax({
                url: "{{ route('delete_trip', $trip) }}",
                type: "DELETE",
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function (response) {
                    $("#trip{{ $trip->id }}").remove();
                    $(`#ticket${response.data.id}`).remove();
                    showAlert('success', 'Success', response.message);
                },
                error: function (xhr) {
                    showAlert('error', 'Error', xhr.responseJSON.message);
                }
            })
        });
        function initializeMap(mapId) {
            // Center map between Tabuk City and Baguio, Philippines
            var map = window.L.map(mapId).setView([17.2, 121.7], 8); // Luzon, PH
            window.L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
                attribution: "&copy; OpenStreetMap contributors"
            }).addTo(map);

            var startPoint = "{{ $trip->route->start_point }}";
            var endPoint = "{{ $trip->route->end_point }}";

            // Always mark Tabuk City and Baguio
            const tabukCoords = [17.4108, 121.4406]; // Tabuk City, PH
            const baguioCoords = [16.4023, 120.5960]; // Baguio City, PH
            window.L.marker(tabukCoords).addTo(map).bindPopup("Tabuk City, PH");
            window.L.marker(baguioCoords).addTo(map).bindPopup("Baguio City, PH");

            let startLatLng, endLatLng;

            function getCoordinates(location, callback) {
                fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${location}&countrycodes=ph&limit=1`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.length > 0) {
                            callback([parseFloat(data[0].lat), parseFloat(data[0].lon)]);
                        } else {
                            console.log('No coordinates found for:', location);
                            // Use default coordinates for Philippines
                            if (location.toLowerCase().includes('tabuk')) {
                                callback([17.4108, 121.4406]); // Tabuk City
                            } else if (location.toLowerCase().includes('baguio')) {
                                callback([16.4023, 120.5960]); // Baguio City
                            } else {
                                callback([14.5995, 120.9842]); // Manila as fallback
                            }
                        }
                    })
                    .catch(error => {
                        console.log('Geocoding error:', error);
                        // Use default coordinates
                        if (location.toLowerCase().includes('tabuk')) {
                            callback([17.4108, 121.4406]); // Tabuk City
                        } else if (location.toLowerCase().includes('baguio')) {
                            callback([16.4023, 120.5960]); // Baguio City
                        } else {
                            callback([14.5995, 120.9842]); // Manila as fallback
                        }
                    });
            }

            getCoordinates(startPoint, function (coords) {
                startLatLng = coords;
                window.L.marker(startLatLng).addTo(map).bindPopup("Start: " + startPoint);
                map.setView(startLatLng, 10);
                drawRoute();
            });

            getCoordinates(endPoint, function (coords) {
                endLatLng = coords;
                window.L.marker(endLatLng).addTo(map).bindPopup("End: " + endPoint);
                drawRoute();
            });

            function drawRoute() {
                if (!startLatLng || !endLatLng) return;

                try {
                    var routeControl = window.L.Routing.control({
                        waypoints: [
                            window.L.latLng(startLatLng[0], startLatLng[1]),
                            window.L.latLng(endLatLng[0], endLatLng[1])
                        ],
                        routeWhileDragging: false,
                        createMarker: function () { return null; },
                        show: false,
                        router: window.L.Routing.osrmv1({
                            serviceUrl: 'https://router.project-osrm.org/route/v1',
                            profile: 'driving'
                        })
                    }).addTo(map);

                    routeControl.on('routesfound', function (e) {
                        let distance_km = e.routes[0].summary.totalDistance / 1000;
                        const distanceElement = document.getElementById("distance-{{ $trip->id }}");
                        if (distanceElement) {
                            distanceElement.innerText = distance_km.toFixed(2) + " km";
                        }

                        // Use fare data instead of hardcoded prices
                        fetch('/fares/route/{{ $trip->route_id }}')
                            .then(response => response.json())
                            .then(data => {
                                const priceElement = document.getElementById("priceValue{{ $trip->id }}");
                                if (priceElement) {
                                    if (data.success && data.fare) {
                                        const fare = data.fare.base_fare;
                                        priceElement.innerText = "₱" + fare;
                                    } else {
                                        priceElement.innerText = "₱150.00";
                                    }
                                }
                            })
                            .catch(error => {
                                console.log('Fare fetch error:', error);
                                const priceElement = document.getElementById("priceValue{{ $trip->id }}");
                                if (priceElement) {
                                    priceElement.innerText = "₱150.00";
                                }
                            });
                    });

                    routeControl.on('routingerror', function (e) {
                        console.log('Routing error:', e);
                        const distanceElement = document.getElementById("distance-{{ $trip->id }}");
                        if (distanceElement) {
                            distanceElement.innerText = "Distance unavailable";
                        }

                        // Still fetch fare data
                        fetch('/fares/route/{{ $trip->route_id }}')
                            .then(response => response.json())
                            .then(data => {
                                const priceElement = document.getElementById("priceValue{{ $trip->id }}");
                                if (priceElement) {
                                    if (data.success && data.fare) {
                                        const fare = data.fare.base_fare;
                                        priceElement.innerText = "₱" + fare;
                                    } else {
                                        priceElement.innerText = "₱150.00";
                                    }
                                }
                            })
                            .catch(error => {
                                console.log('Fare fetch error:', error);
                                const priceElement = document.getElementById("priceValue{{ $trip->id }}");
                                if (priceElement) {
                                    priceElement.innerText = "₱150.00";
                                }
                            });
                    });
                } catch (error) {
                    console.log('Map initialization error:', error);
                    const distanceElement = document.getElementById("distance-{{ $trip->id }}");
                    const priceElement = document.getElementById("priceValue{{ $trip->id }}");

                    if (distanceElement) {
                        distanceElement.innerText = "Distance unavailable";
                    }
                    if (priceElement) {
                        priceElement.innerText = "₱150.00";
                    }
                }
            }
        }

        initializeMap("map-{{ $trip->id }}");

        $('#submit-update{{ $trip->id }}').click(function(e) {
            e.preventDefault();

            $.ajax({
                url: "{{ route('edit_trip',$trip) }}",
                method: "PATCH",
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                data: {
                    'bus_id': $('#Selectbus-name{{ $trip->id }}').val(),
                    'route_id': $('#Selectroute{{ $trip->id }}').val(),
                    'schedule_id': $('#Selectschedule{{ $trip->id }}').val(),
                },
                success: function(response) {
                    console.log(response);
                    $("#updateModal{{ $trip->id }}").addClass('hidden');
                    $("#bus-name{{ $trip->id }}").text(response.data.bus.name);
                    $("#route{{ $trip->id }}").text(response.data.route.start_point + ' → ' + response.data.route.end_point);
                    $("#schedule{{ $trip->id }}").text(response.data.schedule.start_time + ' - ' + response.data.schedule.end_time);
                    $("#seats-left{{ $trip->id }}").text(response.data.bus.seat + ' seats left');
                    showAlert('success', 'Success', response.message);
                },
                error: function(xhr) {
                    $("#updateModal{{ $trip->id }}").addClass('hidden');
                    showAlert('error', 'Error', 'An error occurred while processing your request.');
                    console.log(xhr.responseText);
                }
            });
        });
    });
</script>

