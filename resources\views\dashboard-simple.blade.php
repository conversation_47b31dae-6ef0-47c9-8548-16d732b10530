@extends('layouts.app')

@section('content')
<style>
/* Enhanced GL Bus Color Palette */
:root {
    --primary-gold: #FCB404;
    --primary-gold-dark: #E6A200;
    --secondary-blue: #1E3A8A;
    --secondary-blue-light: #3B82F6;
    --accent-green: #10B981;
    --accent-purple: #8B5CF6;
    --accent-orange: #F97316;
    --accent-teal: #14B8A6;
}

.gradient-primary {
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
}

.gradient-blue {
    background: linear-gradient(135deg, var(--secondary-blue) 0%, var(--secondary-blue-light) 100%);
}

.gradient-green {
    background: linear-gradient(135deg, var(--accent-green) 0%, #059669 100%);
}

.gradient-purple {
    background: linear-gradient(135deg, var(--accent-purple) 0%, #7C3AED 100%);
}

.gradient-orange {
    background: linear-gradient(135deg, var(--accent-orange) 0%, #EA580C 100%);
}

.gradient-teal {
    background: linear-gradient(135deg, var(--accent-teal) 0%, #0F766E 100%);
}

.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: var(--primary-gold);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
}

.btn-primary:hover {
    background: var(--primary-gold-dark);
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .mobile-stack {
        flex-direction: column;
    }

    .mobile-full {
        width: 100%;
    }

    .mobile-text-center {
        text-align: center;
    }

    .mobile-hidden {
        display: none;
    }

    .mobile-p-4 {
        padding: 1rem;
    }
}
</style>
<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border mb-6 overflow-hidden">
            <div class="p-4 sm:p-6 border-b gradient-primary">
                <div class="flex flex-col sm:flex-row items-start sm:items-center">
                    <div class="p-3 bg-white bg-opacity-20 rounded-lg mb-4 sm:mb-0">
                        <svg class="w-6 h-6 sm:w-8 sm:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2zm0 0V9a2 2 0 012-2h4a2 2 0 012 2v10m-6 0a2 2 0 002 2h4a2 2 0 002-2m0 0V5a2 2 0 012-2h4a2 2 0 012 2v14a2 2 0 01-2 2h-4a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="sm:ml-4 text-center sm:text-left">
                        <h1 class="text-xl sm:text-2xl font-bold text-white">🏠 Welcome Back!</h1>
                        <p class="text-yellow-100 text-sm sm:text-base">{{ $data['user']->name }}</p>
                        <p class="text-yellow-200 text-xs sm:text-sm mt-1">{{ now()->format('l, F j, Y') }}</p>
                    </div>
                    <div class="ml-auto mt-4 sm:mt-0 mobile-hidden">
                        <div class="text-right">
                            <p class="text-yellow-100 text-sm">Member since</p>
                            <p class="text-white font-semibold">{{ $data['user']->created_at->format('M Y') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Stats -->
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm border p-4 sm:p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-2 sm:p-3 rounded-full gradient-primary flex-shrink-0">
                        <svg class="w-4 h-4 sm:w-6 sm:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                        <p class="text-xs sm:text-sm font-medium text-gray-600 truncate">Total Bookings</p>
                        <p class="text-lg sm:text-2xl font-bold text-gray-900">{{ $data['total_bookings'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-4 sm:p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-2 sm:p-3 rounded-full gradient-green flex-shrink-0">
                        <svg class="w-4 h-4 sm:w-6 sm:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                        <p class="text-xs sm:text-sm font-medium text-gray-600 truncate">Total Spent</p>
                        <p class="text-lg sm:text-2xl font-bold text-gray-900">₱{{ number_format($data['total_spent'], 2) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-4 sm:p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-2 sm:p-3 rounded-full gradient-blue flex-shrink-0">
                        <svg class="w-4 h-4 sm:w-6 sm:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                        <p class="text-xs sm:text-sm font-medium text-gray-600 truncate">Upcoming</p>
                        <p class="text-lg sm:text-2xl font-bold text-gray-900">{{ $data['upcoming_trips']->count() }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-4 sm:p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-2 sm:p-3 rounded-full gradient-purple flex-shrink-0">
                        <svg class="w-4 h-4 sm:w-6 sm:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                        <p class="text-xs sm:text-sm font-medium text-gray-600 truncate">Profile</p>
                        <p class="text-sm sm:text-base font-bold text-gray-900 truncate">{{ ucfirst($data['user_role']) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm border p-4 sm:p-6 card-hover">
                <div class="flex items-center mb-4">
                    <div class="p-3 rounded-full gradient-primary">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Book a Trip</p>
                        <p class="text-lg font-semibold text-gray-900">Start Booking</p>
                    </div>
                </div>
                <a href="{{ route('home') }}" class="btn-primary w-full justify-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Book Now
                </a>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-4 sm:p-6 card-hover">
                <div class="flex items-center mb-4">
                    <div class="p-3 rounded-full gradient-green">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">My Bookings</p>
                        <p class="text-lg font-semibold text-gray-900">{{ $data['total_bookings'] }} Total</p>
                    </div>
                </div>
                <button onclick="scrollToBookings()" class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    View History
                </button>
            </div>

            <div class="bg-white rounded-lg shadow-sm border p-4 sm:p-6 card-hover sm:col-span-2 lg:col-span-1">
                <div class="flex items-center mb-4">
                    <div class="p-3 rounded-full gradient-blue">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Profile</p>
                        <p class="text-lg font-semibold text-gray-900">Settings</p>
                    </div>
                </div>
                <a href="{{ route('profile.edit') }}" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Profile
                </a>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
            <!-- Upcoming Trips -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-sm border">
                    <div class="p-4 sm:p-6 border-b">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <h2 class="text-lg font-semibold text-gray-900 mb-2 sm:mb-0">🚌 Upcoming Trips</h2>
                            <span class="text-sm text-gray-500">{{ $data['upcoming_trips']->count() }} upcoming</span>
                        </div>
                    </div>
                    <div class="p-4 sm:p-6">
                        @forelse($data['upcoming_trips'] as $trip)
                        <div class="flex flex-col sm:flex-row sm:items-center p-4 border border-gray-200 rounded-lg mb-4 last:mb-0 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center mb-3 sm:mb-0 sm:flex-1">
                                <div class="p-2 gradient-primary rounded-lg mr-3 flex-shrink-0">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"></path>
                                    </svg>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="font-semibold text-gray-900 truncate">{{ $trip->trip->route->start_point }} → {{ $trip->trip->route->end_point }}</p>
                                    <p class="text-sm text-gray-600">{{ $trip->trip->bus->name }} • Seat {{ $trip->seat_number }}</p>
                                </div>
                            </div>
                            <div class="flex flex-col sm:items-end text-left sm:text-right">
                                <p class="text-sm font-medium text-gray-900">{{ $trip->trip->schedule->formatted_trip_date }}</p>
                                <p class="text-sm text-gray-600">{{ $trip->trip->schedule->formatted_start_time }}</p>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-1">
                                    Confirmed
                                </span>
                            </div>
                        </div>
                        @empty
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <p class="text-lg font-medium text-gray-900 mb-2">No upcoming trips</p>
                            <p class="text-gray-500 mb-4">Book your next adventure!</p>
                            <a href="{{ route('home') }}" class="btn-primary">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Book a Trip
                            </a>
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- Available Routes -->
            <div>
                <div class="bg-white rounded-lg shadow-sm border">
                    <div class="p-4 sm:p-6 border-b">
                        <h2 class="text-lg font-semibold text-gray-900">🛣️ Popular Routes</h2>
                    </div>
                    <div class="p-4 sm:p-6">
                        <div class="space-y-3">
                            @foreach($data['available_routes'] as $route)
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="flex items-center min-w-0 flex-1">
                                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2 flex-shrink-0"></div>
                                    <span class="text-sm font-medium text-gray-900 truncate">{{ $route->start_point }}</span>
                                    <svg class="w-4 h-4 mx-2 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                    <div class="w-3 h-3 bg-red-500 rounded-full mr-2 flex-shrink-0"></div>
                                    <span class="text-sm text-gray-600 truncate">{{ $route->end_point }}</span>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        <div class="mt-4">
                            <a href="{{ route('home') }}" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center">
                                View All Routes
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Booking History -->
        <div class="mt-8" id="booking-history">
            <div class="bg-white rounded-lg shadow-sm border">
                <div class="p-4 sm:p-6 border-b">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <h2 class="text-lg font-semibold text-gray-900 mb-2 sm:mb-0">📋 Booking History</h2>
                        <span class="text-sm text-gray-500">{{ $data['my_bookings']->total() }} total bookings</span>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trip</th>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider mobile-hidden">Bus</th>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider mobile-hidden">Seat</th>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @forelse($data['my_bookings'] as $booking)
                            <tr class="hover:bg-gray-50 transition-colors">
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ $booking->trip->route->start_point }} → {{ $booking->trip->route->end_point }}</div>
                                    <div class="text-sm text-gray-500 sm:hidden">{{ $booking->trip->bus->name }} • Seat {{ $booking->seat_number }}</div>
                                </td>
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900 mobile-hidden">{{ $booking->trip->bus->name }}</td>
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $booking->trip->schedule->formatted_trip_date }}</div>
                                    <div class="text-sm text-gray-500">{{ $booking->trip->schedule->formatted_start_time }}</div>
                                </td>
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900 mobile-hidden">{{ $booking->seat_number }}</td>
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Confirmed
                                    </span>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="5" class="px-4 sm:px-6 py-8 text-center text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <p class="text-lg font-medium text-gray-900 mb-2">No bookings yet</p>
                                    <p class="text-gray-500 mb-4">Start your journey with GL Bus!</p>
                                    <a href="{{ route('home') }}" class="btn-primary">
                                        Book Your First Trip
                                    </a>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($data['my_bookings']->hasPages())
                <div class="px-4 sm:px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Showing {{ $data['my_bookings']->firstItem() }} to {{ $data['my_bookings']->lastItem() }} of {{ $data['my_bookings']->total() }} bookings
                        </div>
                        <div class="pagination-custom">
                            {{ $data['my_bookings']->links('custom-pagination') }}
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<script>
function scrollToBookings() {
    document.getElementById('booking-history').scrollIntoView({
        behavior: 'smooth'
    });
}
</script>
@endsection
