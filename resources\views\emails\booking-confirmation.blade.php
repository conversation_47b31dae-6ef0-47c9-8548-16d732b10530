<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GL Bus - Booking Confirmation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #FCB404;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #FCB404;
            margin-bottom: 10px;
        }
        .booking-ref {
            background-color: #FCB404;
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        .booking-ref h2 {
            margin: 0;
            font-size: 24px;
        }
        .booking-ref p {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }
        .trip-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: bold;
            color: #666;
        }
        .detail-value {
            color: #333;
        }
        .payment-info {
            background-color: #e3f2fd;
            border: 2px solid #2196f3;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .payment-method {
            display: inline-block;
            background-color: #2196f3;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            margin-bottom: 15px;
        }
        .account-details {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .qr-section {
            text-align: center;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .qr-placeholder {
            width: 150px;
            height: 150px;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 10px 10px;
            background-position: 0 0, 0 5px, 5px -5px, -5px 0px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #666;
        }
        .important-notes {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
        .btn {
            display: inline-block;
            background-color: #FCB404;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            font-weight: bold;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
            .detail-row {
                flex-direction: column;
            }
            .detail-label {
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🚌 GL BUS</div>
            <p>Your trusted travel partner</p>
        </div>

        <!-- Booking Reference -->
        <div class="booking-ref">
            <h2>Booking Confirmed!</h2>
            <p>Reference: #{{ str_pad($payment->id, 6, '0', STR_PAD_LEFT) }}</p>
        </div>

        <!-- Trip Details -->
        <div class="trip-details">
            <h3 style="margin-top: 0; color: #FCB404;">🎫 Trip Details</h3>
            @php
                $ticket = is_array($tickets) ? $tickets[0] : $tickets;
                $trip = $ticket->trip;
            @endphp
            
            <div class="detail-row">
                <span class="detail-label">Route:</span>
                <span class="detail-value">{{ $trip->route->start_point }} → {{ $trip->route->end_point }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Bus:</span>
                <span class="detail-value">{{ $trip->bus->name }} ({{ $trip->bus->bus_code }})</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Date:</span>
                <span class="detail-value">{{ $trip->schedule->trip_date ? \Carbon\Carbon::parse($trip->schedule->trip_date)->format('F d, Y') : 'TBD' }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Departure:</span>
                <span class="detail-value">{{ \Carbon\Carbon::parse($trip->schedule->start_time)->format('g:i A') }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Seats:</span>
                <span class="detail-value">
                    @if(is_array($tickets))
                        @foreach($tickets as $ticket)
                            {{ $ticket->seat_number }}{{ !$loop->last ? ', ' : '' }}
                        @endforeach
                    @else
                        {{ $tickets->seat_number }}
                    @endif
                </span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Passenger:</span>
                <span class="detail-value">{{ $payment->passenger_name }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Total Amount:</span>
                <span class="detail-value" style="font-weight: bold; color: #FCB404;">₱{{ number_format($payment->amount, 2) }}</span>
            </div>
        </div>

        <!-- Payment Information -->
        <div class="payment-info">
            <h3 style="margin-top: 0; color: #2196f3;">💳 Payment Information</h3>
            
            @if($payment->payment_method === 'gcash')
                <div class="payment-method">📱 GCash Payment</div>
                <div class="account-details">
                    <strong>Send payment to:</strong><br>
                    <strong>GCash Number:</strong> {{ env('GLBUS_GCASH_NUMBER', '***********') }}<br>
                    <strong>Account Name:</strong> {{ env('GLBUS_GCASH_NAME', 'GL Bus Transport Corp') }}<br>
                    <strong>Amount:</strong> ₱{{ number_format($payment->amount, 2) }}
                </div>
            @elseif($payment->payment_method === 'maya')
                <div class="payment-method">💳 Maya Payment</div>
                <div class="account-details">
                    <strong>Send payment to:</strong><br>
                    <strong>Maya Number:</strong> {{ env('GLBUS_MAYA_NUMBER', '***********') }}<br>
                    <strong>Account Name:</strong> {{ env('GLBUS_MAYA_NAME', 'GL Bus Transport Corp') }}<br>
                    <strong>Amount:</strong> ₱{{ number_format($payment->amount, 2) }}
                </div>
            @elseif($payment->payment_method === 'qrph')
                <div class="payment-method">🔲 QRPh Payment</div>
                <div class="account-details">
                    <strong>Send payment to:</strong><br>
                    <strong>Account Name:</strong> {{ env('GLBUS_QRPH_ACCOUNT', 'GL Bus Transport Corp') }}<br>
                    <strong>Bank Account:</strong> {{ env('GLBUS_BANK_ACCOUNT', 'BPI - **********') }}<br>
                    <strong>Amount:</strong> ₱{{ number_format($payment->amount, 2) }}
                </div>
            @endif

            @if($payment->reference_number)
                <div style="margin-top: 15px;">
                    <strong>Your Reference Number:</strong> {{ $payment->reference_number }}
                </div>
            @endif
        </div>

        <!-- QR Code Section -->
        <div class="qr-section">
            <h3 style="margin-top: 0; color: #FCB404;">📱 Your Boarding Pass</h3>
            <div class="qr-placeholder">
                QR CODE<br>
                #{{ str_pad($payment->id, 6, '0', STR_PAD_LEFT) }}
            </div>
            <p><strong>Show this email or QR code when boarding</strong></p>
        </div>

        <!-- Important Notes -->
        <div class="important-notes">
            <h3 style="margin-top: 0; color: #856404;">⚠️ Important Reminders</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li>Please arrive at the terminal at least <strong>30 minutes before departure</strong></li>
                <li>Bring a valid ID for verification</li>
                <li>This ticket is non-transferable and non-refundable</li>
                <li>Keep your payment reference number for verification</li>
                <li>Contact us immediately if you need to make changes</li>
            </ul>
        </div>

        <!-- Action Buttons -->
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ url('/booking/confirmation/' . $payment->id) }}" class="btn">View Full Details</a>
            <a href="{{ url('/') }}" class="btn" style="background-color: #6c757d;">Book Another Trip</a>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>GL Bus Transport Corporation</strong></p>
            <p>📞 Customer Service: (************* | 📧 <EMAIL></p>
            <p>🌐 Visit us at: www.glbus.com</p>
            <p style="margin-top: 20px; font-size: 12px; color: #999;">
                This is an automated message. Please do not reply to this email.
            </p>
        </div>
    </div>
</body>
</html>
