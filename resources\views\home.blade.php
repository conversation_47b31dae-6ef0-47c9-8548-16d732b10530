@php
$pageTitle = 'Book Your Trip';
@endphp

<x-app-layout>
    <script src="https://code.jquery.com/jquery-3.7.1.slim.js" integrity="sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <style>
        :root {
            --primary-blue: #2563eb;
            --primary-blue-dark: #1d4ed8;
            --secondary-green: #059669;
            --accent-purple: #7c3aed;
            --accent-orange: #ea580c;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Figtree', sans-serif;
            background-color: var(--gray-50);
        }

        .hero-gradient {
            background: var(--gradient-hero);
        }

        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            border: 1px solid var(--gray-200);
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.4);
            color: white;
            text-decoration: none;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-available {
            background: #d1fae5;
            color: #065f46;
        }

        .status-limited {
            background: #fef3c7;
            color: #92400e;
        }

        .status-full {
            background: #fee2e2;
            color: #991b1b;
        }

        @media (max-width: 768px) {
            .grid-cols-1 {
                grid-template-columns: 1fr;
            }
        }
    </style>

    <!-- Hero Section -->
    <div class="hero-gradient text-white py-16 mb-8">
        <div class="max-w-7xl mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">Welcome Back, {{ Auth::user()->name }}!</h1>
            <p class="text-xl mb-8 opacity-90">Book your journey with comfort and convenience</p>
            <div class="flex justify-center space-x-6">
                <div class="bg-white bg-opacity-20 rounded-xl p-6 backdrop-blur-sm">
                    <div class="text-3xl font-bold">{{ $trips->count() }}</div>
                    <div class="text-sm opacity-80">Available Trips</div>
                </div>
                <div class="bg-white bg-opacity-20 rounded-xl p-6 backdrop-blur-sm">
                    <div class="text-3xl font-bold">{{ $tickets->count() }}</div>
                    <div class="text-sm opacity-80">Your Bookings</div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 pb-12">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

            <!-- Your Tickets Section -->
            <div class="lg:col-span-1">
                <div class="card p-6">
                    <div class="flex items-center mb-6">
                        <div class="p-3 bg-blue-100 rounded-xl">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-800 ml-3">Your Tickets</h2>
                    </div>

                    <div class="space-y-4 max-h-96 overflow-y-auto" id="ticket-list">
                        @forelse ($tickets as $ticket)
                            <x-buscomponents.Ticket :ticket="$ticket" />
                        @empty
                            <div class="text-center py-8">
                                <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                                <p class="text-gray-500 font-medium">No tickets found</p>
                                <p class="text-sm text-gray-400 mt-1">Book a trip to see your tickets here</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- Available Trips Section -->
            <div class="lg:col-span-2">
                <div class="card p-6">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center">
                            <div class="p-3 bg-green-100 rounded-xl">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"></path>
                                </svg>
                            </div>
                            <h2 class="text-2xl font-bold text-gray-800 ml-3">Available Trips</h2>
                        </div>
                    </div>

                    <div class="space-y-6 max-h-96 overflow-y-auto">
                        @forelse($trips as $trip)
                            <x-buscomponents.Trip :trip="$trip" />
                        @empty
                            <div class="text-center py-12">
                                <svg class="w-20 h-20 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"></path>
                                </svg>
                                <p class="text-gray-500 text-lg font-medium">No trips available</p>
                                <p class="text-sm text-gray-400 mt-1">Check back later for new trips</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showAlert(icon, title, message) {
            Swal.fire({
                icon: icon,
                title: title,
                text: message,
                showConfirmButton: true,
                confirmButtonColor: '#2563eb'
            });
        }
    </script>
</x-app-layout>