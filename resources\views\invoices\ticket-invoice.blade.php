<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bus Ticket Invoice - {{ $payment->invoice_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }
        .invoice-title {
            font-size: 20px;
            color: #6b7280;
            margin-bottom: 10px;
        }
        .invoice-number {
            font-size: 16px;
            color: #3b82f6;
            font-weight: bold;
        }
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .info-box {
            flex: 1;
            margin-right: 20px;
        }
        .info-box:last-child {
            margin-right: 0;
        }
        .info-title {
            font-weight: bold;
            color: #374151;
            margin-bottom: 10px;
            font-size: 14px;
            text-transform: uppercase;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
        }
        .info-content {
            color: #6b7280;
            line-height: 1.6;
        }
        .ticket-details {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .detail-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
            color: #1f2937;
        }
        .detail-label {
            font-weight: 500;
            color: #374151;
        }
        .detail-value {
            color: #6b7280;
        }
        .payment-info {
            background: #ecfdf5;
            border: 1px solid #10b981;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .payment-status {
            color: #065f46;
            font-weight: bold;
            text-align: center;
        }
        .footer {
            text-align: center;
            color: #6b7280;
            font-size: 12px;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
        @media print {
            body { background: white; }
            .invoice-container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="header">
            <div class="company-name">Bus Reservation System</div>
            <div class="invoice-title">Bus Ticket Invoice</div>
            <div class="invoice-number">{{ $payment->invoice_number }}</div>
        </div>

        <div class="info-section">
            <div class="info-box">
                <div class="info-title">Passenger Information</div>
                <div class="info-content">
                    <strong>{{ $ticket->user->name }}</strong><br>
                    {{ $ticket->user->email }}<br>
                    Ticket ID: #{{ $ticket->id }}
                </div>
            </div>
            <div class="info-box">
                <div class="info-title">Trip Information</div>
                <div class="info-content">
                    <strong>{{ $ticket->trip->bus->name }}</strong><br>
                    {{ $ticket->trip->route->start_point }} → {{ $ticket->trip->route->end_point }}<br>
                    {{ $ticket->trip->schedule->start_time }} - {{ $ticket->trip->schedule->end_time }}
                </div>
            </div>
            <div class="info-box">
                <div class="info-title">Booking Details</div>
                <div class="info-content">
                    Booking Date: {{ $ticket->created_at->format('M d, Y') }}<br>
                    @if($payment->payment_date)
                    Payment Date: {{ $payment->payment_date->format('M d, Y') }}<br>
                    @endif
                    Seat Number: <strong>{{ $ticket->seat_number }}</strong>
                </div>
            </div>
        </div>

        <div class="ticket-details">
            <div class="detail-row">
                <span class="detail-label">Base Fare</span>
                <span class="detail-value">₱{{ number_format($payment->amount, 2) }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Service Fee</span>
                <span class="detail-value">₱0.00</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Total Amount</span>
                <span class="detail-value">₱{{ number_format($payment->amount, 2) }}</span>
            </div>
        </div>

        @if($payment->status === 'paid')
        <div class="payment-info">
            <div class="payment-status">✅ PAYMENT CONFIRMED</div>
            <div style="text-align: center; margin-top: 10px; color: #065f46;">
                Payment Method: {{ strtoupper($payment->payment_method) }}<br>
                @if($payment->payment_details)
                    @php $details = json_decode($payment->payment_details, true); @endphp
                    @if(isset($details['reference']) && $details['reference'])
                        Reference: {{ $details['reference'] }}
                    @endif
                @endif
            </div>
        </div>
        @else
        <div class="payment-info" style="background: #fef3c7; border-color: #f59e0b;">
            <div class="payment-status" style="color: #92400e;">⏳ PAYMENT PENDING</div>
            <div style="text-align: center; margin-top: 10px; color: #92400e;">
                Please complete your payment to confirm your booking.
            </div>
        </div>
        @endif

        <div class="qr-code">
            <div style="border: 2px dashed #d1d5db; padding: 20px; display: inline-block;">
                <div style="font-size: 12px; color: #6b7280; margin-bottom: 10px;">Booking Reference</div>
                <div style="font-family: monospace; font-size: 16px; font-weight: bold;">
                    {{ $payment->invoice_number }}
                </div>
            </div>
        </div>

        <div class="footer">
            <p><strong>Important Notes:</strong></p>
            <p>• Please arrive at the terminal at least 30 minutes before departure time.</p>
            <p>• This ticket is non-transferable and must be presented during boarding.</p>
            <p>• For inquiries, please contact our customer service.</p>
            <p style="margin-top: 20px;">
                Generated on {{ now()->format('F d, Y \a\t g:i A') }}
            </p>
        </div>
    </div>

    <script>
        // Auto-print functionality
        function printInvoice() {
            window.print();
        }
        
        // Add print button if not in print mode
        if (!window.matchMedia('print').matches) {
            document.addEventListener('DOMContentLoaded', function() {
                const printBtn = document.createElement('button');
                printBtn.innerHTML = '🖨️ Print Invoice';
                printBtn.style.cssText = 'position: fixed; top: 20px; right: 20px; padding: 10px 20px; background: #3b82f6; color: white; border: none; border-radius: 5px; cursor: pointer; z-index: 1000;';
                printBtn.onclick = printInvoice;
                document.body.appendChild(printBtn);
            });
        }
    </script>
</body>
</html>
