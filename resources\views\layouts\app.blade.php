<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <meta name="description" content="GL Bus Reservation System - Book your bus tickets online with ease">
        <meta name="keywords" content="bus reservation, online booking, GL Bus, travel, transportation">
        <meta name="author" content="GL Bus Reservation System">

        <title>{{ isset($pageTitle) ? $pageTitle . ' - ' . config('app.name') : config('app.name') }}</title>

        <!-- Favicon -->
        <link rel="icon" type="image/png" href="{{ asset('images/gl-logo.png') }}">
        <link rel="shortcut icon" type="image/png" href="{{ asset('images/gl-logo.png') }}">
        <link rel="apple-touch-icon" href="{{ asset('images/gl-logo.png') }}">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />


        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- jQuery for AJAX functionality -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

        <!-- SweetAlert2 for alerts -->
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

        <!-- Toastr for toast notifications -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

        <!-- Custom CSS Variables -->
        <style>
            :root {
                --main-bg: #f8fafc;
                --accent-blue: #3b82f6;
                --accent-red: #dc2626;
                --primary-color: #FCB404;
                --primary-hover: #E6A200;
                --primary-dark: #CC9600;
            }

            /* Global GL Bus Color Overrides */
            .bg-red-600, .bg-red-500 {
                background-color: #FCB404 !important;
            }
            .bg-red-700 {
                background-color: #E6A200 !important;
            }
            .hover\:bg-red-700:hover {
                background-color: #E6A200 !important;
            }
            .text-red-600 {
                color: #FCB404 !important;
            }
            .border-red-500, .border-red-600 {
                border-color: #FCB404 !important;
            }
            .focus\:ring-red-500:focus {
                --tw-ring-color: #FCB404 !important;
            }
            .focus\:border-red-500:focus {
                border-color: #FCB404 !important;
            }
        </style>

        <!-- Customer Navigation JavaScript -->
        <script>
            function viewMyBookings() {
                // This would typically fetch user's bookings via AJAX
                Swal.fire({
                    title: '📋 My Bookings',
                    html: `
                        <div class="text-left space-y-4">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="font-bold text-gray-800 mb-3">Recent Bookings</h3>

                                <div class="space-y-3">
                                    <div class="bg-white p-3 rounded border-l-4 border-green-400">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <div class="font-medium text-gray-800">Manila → Baguio</div>
                                                <div class="text-sm text-gray-600">Ticket #GL000123</div>
                                                <div class="text-sm text-gray-600">Seat #15 • Aug 6, 2025 8:00 AM</div>
                                            </div>
                                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Confirmed</span>
                                        </div>
                                    </div>

                                    <div class="bg-white p-3 rounded border-l-4 border-blue-400">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <div class="font-medium text-gray-800">Baguio → Manila</div>
                                                <div class="text-sm text-gray-600">Ticket #GL000124</div>
                                                <div class="text-sm text-gray-600">Seat #22 • Aug 8, 2025 2:00 PM</div>
                                            </div>
                                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Upcoming</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4 text-center">
                                    <a href="{{ route('booking.index') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Book Another Trip →
                                    </a>
                                </div>
                            </div>
                        </div>
                    `,
                    showConfirmButton: true,
                    confirmButtonText: 'Close',
                    confirmButtonColor: '#FCB404',
                    width: '500px'
                });
            }
        </script>
    </head>
    <body class="font-sans antialiased" style="background: var(--main-bg);">
        <div class="min-h-screen" style="background: var(--main-bg);">
            @include('layouts.navigation')

            <!-- Page Heading -->
            @isset($header)
                <header class="bg-white dark:bg-gray-800 shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        {{ $header }}
                    </div>
                </header>
            @endisset

            <!-- Page Content -->
            <main>
                @yield('content')
            </main>
            
          
        </div>
        
        <!-- Additional Scripts Stack -->
        @stack('scripts')
        
        <script>
            // Configure toastr globally
            if (typeof toastr !== 'undefined') {
                toastr.options = {
                    "closeButton": true,
                    "debug": false,
                    "newestOnTop": true,
                    "progressBar": true,
                    "positionClass": "toast-top-right",
                    "preventDuplicates": false,
                    "onclick": null,
                    "showDuration": "300",
                    "hideDuration": "1000",
                    "timeOut": "5000",
                    "extendedTimeOut": "1000",
                    "showEasing": "swing",
                    "hideEasing": "linear",
                    "showMethod": "fadeIn",
                    "hideMethod": "fadeOut"
                };
            }
            
            function showAlert(icon, title, message) {
                Swal.fire({
                    icon: icon,
                    title: title,
                    text: message,
                    showConfirmButton: true
                });
            }
        </script>
    </body>
</html>
