<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Payment Gateway - {{ ucfirst($payment_method) }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                    @if($payment_method === 'gcash')
                        <i class="fab fa-google-pay text-blue-600 text-xl"></i>
                    @elseif($payment_method === 'maya')
                        <i class="fas fa-mobile-alt text-green-600 text-xl"></i>
                    @else
                        <i class="fas fa-qrcode text-purple-600 text-xl"></i>
                    @endif
                </div>
                <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                    {{ ucfirst($payment_method) }} Payment
                </h2>
                <p class="mt-2 text-sm text-gray-600">
                    Demo Payment Gateway - GL Bus Booking
                </p>
            </div>

            <!-- Payment Details Card -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="border-b pb-4 mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Payment Details</h3>
                </div>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Merchant:</span>
                        <span class="font-semibold">GL Bus Transport Corp</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Amount:</span>
                        <span class="font-semibold text-lg text-green-600">₱{{ number_format($amount, 2) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Payment Method:</span>
                        <span class="font-semibold">{{ ucfirst($payment_method) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Reference:</span>
                        <span class="font-mono text-sm">{{ $reference }}</span>
                    </div>
                </div>
            </div>

            <!-- Demo Payment Actions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Demo Payment Simulation</h3>
                <p class="text-sm text-gray-600 mb-4">
                    This is a demo payment gateway. In a real implementation, you would be redirected to the actual {{ ucfirst($payment_method) }} payment page.
                </p>
                
                <div class="space-y-3">
                    <button onclick="simulatePayment('success')" 
                            class="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-check-circle mr-2"></i>
                        Simulate Successful Payment
                    </button>
                    
                    <button onclick="simulatePayment('failed')" 
                            class="w-full bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 transition-colors">
                        <i class="fas fa-times-circle mr-2"></i>
                        Simulate Failed Payment
                    </button>
                    
                    <button onclick="simulatePayment('cancelled')" 
                            class="w-full bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-ban mr-2"></i>
                        Simulate Cancelled Payment
                    </button>
                </div>
            </div>

            <!-- Back to Booking -->
            <div class="text-center">
                <a href="{{ route('booking.index') }}" class="text-blue-600 hover:text-blue-800 text-sm">
                    <i class="fas fa-arrow-left mr-1"></i>
                    Back to Booking
                </a>
            </div>
        </div>
    </div>

    <script>
        function simulatePayment(status) {
            const button = event.target;
            const originalText = button.innerHTML;
            
            // Show loading
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
            button.disabled = true;
            
            // Simulate processing delay
            setTimeout(() => {
                // Redirect to callback URL with status
                const callbackUrl = new URL('{{ $callback_url }}');
                callbackUrl.searchParams.set('status', status);
                callbackUrl.searchParams.set('reference', '{{ $reference }}');
                callbackUrl.searchParams.set('transaction_id', 'DEMO_' + Date.now());
                
                window.location.href = callbackUrl.toString();
            }, 2000);
        }
    </script>
</body>
</html>
