<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $pageTitle ?? 'Payment Gateway' }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="max-w-md mx-auto">
            <div class="bg-white rounded-lg shadow-xl overflow-hidden">
                <!-- Header -->
                <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
                    <div class="flex items-center">
                        @if($method === 'gcash')
                            <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm">G</span>
                            </div>
                            <div>
                                <h1 class="text-white text-lg font-semibold">GCash Payment</h1>
                                <p class="text-blue-100 text-sm">Secure payment via GCash</p>
                            </div>
                        @elseif($method === 'maya')
                            <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm">M</span>
                            </div>
                            <div>
                                <h1 class="text-white text-lg font-semibold">Maya Payment</h1>
                                <p class="text-blue-100 text-sm">Secure payment via Maya</p>
                            </div>
                        @else
                            <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm">QR</span>
                            </div>
                            <div>
                                <h1 class="text-white text-lg font-semibold">{{ ucfirst($method) }} Payment</h1>
                                <p class="text-blue-100 text-sm">Secure payment gateway</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Payment Details -->
                <div class="p-6">
                    <div class="bg-gray-50 rounded-lg p-4 mb-6">
                        <h3 class="text-sm font-medium text-gray-700 mb-2">Payment Details</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Amount:</span>
                                <span class="text-sm font-semibold text-gray-900">₱{{ number_format($amount, 2) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Reference:</span>
                                <span class="text-sm font-mono text-gray-900">{{ $ref }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Merchant:</span>
                                <span class="text-sm text-gray-900">GL Bus Lines</span>
                            </div>
                        </div>
                    </div>

                    <!-- Demo Payment Simulation -->
                    <div class="text-center mb-6">
                        <div class="bg-gradient-to-r from-gray-100 to-gray-200 rounded-lg p-8">
                            <div class="w-16 h-16 bg-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                                <i class="fas fa-credit-card text-white text-xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-700 mb-2">Demo Payment Gateway</h3>
                            <p class="text-sm text-gray-600 mb-4">Choose an option to simulate the payment process</p>
                        </div>
                    </div>

                    <!-- Simulate Payment Buttons -->
                    <div class="space-y-3">
                        <button onclick="simulatePaymentSuccess()" 
                            class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
                            <i class="fas fa-check-circle mr-2"></i>
                            Simulate Successful Payment
                        </button>
                        <button onclick="simulatePaymentFailure()" 
                            class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
                            <i class="fas fa-times-circle mr-2"></i>
                            Simulate Payment Failure
                        </button>
                        <button onclick="cancelPayment()" 
                            class="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
                            <i class="fas fa-ban mr-2"></i>
                            Cancel Payment
                        </button>
                    </div>

                    <!-- Instructions -->
                    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                        <h4 class="text-sm font-medium text-blue-800 mb-2">Demo Instructions:</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• This is a demo payment gateway for testing</li>
                            <li>• Click any button above to simulate payment</li>
                            <li>• Successful payment will redirect to confirmation</li>
                            <li>• Failed/cancelled will return to booking</li>
                        </ul>
                    </div>
                </div>

                <!-- Footer -->
                <div class="bg-gray-50 px-6 py-4 border-t">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-shield-alt mr-1"></i>
                            Demo Mode
                        </div>
                        <a href="{{ route('booking.index') }}" class="text-sm text-blue-600 hover:text-blue-800">
                            <i class="fas fa-arrow-left mr-1"></i>
                            Back to Booking
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const paymentRef = '{{ $ref }}';
        const paymentMethod = '{{ $method }}';
        const paymentAmount = {{ $amount }};

        console.log('Payment Gateway Loaded:', {
            ref: paymentRef,
            method: paymentMethod,
            amount: paymentAmount
        });

        function simulatePaymentSuccess() {
            const button = event.target;
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
            button.disabled = true;
            
            setTimeout(() => {
                window.location.href = `/payment/success?ref=${paymentRef}`;
            }, 2000);
        }

        function simulatePaymentFailure() {
            const button = event.target;
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
            button.disabled = true;
            
            setTimeout(() => {
                window.location.href = `/payment/cancel?ref=${paymentRef}`;
            }, 2000);
        }

        function cancelPayment() {
            if (confirm('Are you sure you want to cancel this payment?')) {
                window.location.href = `/payment/cancel?ref=${paymentRef}`;
            }
        }
    </script>
</body>
</html>
