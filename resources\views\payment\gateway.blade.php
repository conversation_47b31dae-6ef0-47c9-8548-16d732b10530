<x-app-layout>
    <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
        <div class="max-w-md mx-auto">
            <div class="bg-white rounded-lg shadow-xl overflow-hidden">
                <!-- Header -->
                <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
                    <div class="flex items-center">
                        @if($method === 'gcash')
                            <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm">G</span>
                            </div>
                            <div>
                                <h1 class="text-white text-lg font-semibold">GCash Payment</h1>
                                <p class="text-blue-100 text-sm">Secure payment via GCash</p>
                            </div>
                        @elseif($method === 'maya')
                            <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm">M</span>
                            </div>
                            <div>
                                <h1 class="text-white text-lg font-semibold">Maya Payment</h1>
                                <p class="text-blue-100 text-sm">Secure payment via Maya</p>
                            </div>
                        @elseif($method === 'qrph')
                            <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm">QR</span>
                            </div>
                            <div>
                                <h1 class="text-white text-lg font-semibold">QRPh Payment</h1>
                                <p class="text-blue-100 text-sm">Scan QR code to pay</p>
                            </div>
                        @elseif(in_array($method, ['visa', 'master']))
                            <div class="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm">💳</span>
                            </div>
                            <div>
                                <h1 class="text-white text-lg font-semibold">{{ strtoupper($method) }} Payment</h1>
                                <p class="text-blue-100 text-sm">Secure card payment</p>
                            </div>
                        @elseif($method === 'gpay')
                            <div class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm">G</span>
                            </div>
                            <div>
                                <h1 class="text-white text-lg font-semibold">Google Pay</h1>
                                <p class="text-blue-100 text-sm">Pay with Google Pay</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Payment Details -->
                <div class="p-6">
                    <div class="bg-gray-50 rounded-lg p-4 mb-6">
                        <h3 class="text-sm font-medium text-gray-700 mb-2">Payment Details</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Amount:</span>
                                <span class="text-sm font-semibold text-gray-900">₱{{ number_format($amount, 2) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Reference:</span>
                                <span class="text-sm font-mono text-gray-900">{{ $ref }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Merchant:</span>
                                <span class="text-sm text-gray-900">GL Bus Lines</span>
                            </div>
                        </div>
                    </div>

                    @if($method === 'qrph')
                        <!-- QR Code Section -->
                        <div class="text-center mb-6">
                            <div class="bg-white border-2 border-gray-200 rounded-lg p-4 inline-block">
                                <div id="qr-code" class="w-48 h-48 bg-gray-100 rounded flex items-center justify-center">
                                    <div class="text-center">
                                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                                        <p class="text-sm text-gray-600">Generating QR Code...</p>
                                    </div>
                                </div>
                            </div>
                            <p class="text-sm text-gray-600 mt-2">Scan this QR code with your banking app</p>
                        </div>
                    @elseif(in_array($method, ['gcash', 'maya', 'visa', 'master', 'gpay']))
                        <!-- Payment Gateway Simulation -->
                        <div class="text-center mb-6">
                            <div class="bg-gradient-to-r from-gray-100 to-gray-200 rounded-lg p-8">
                                <div class="animate-pulse">
                                    <div class="w-16 h-16 bg-blue-300 rounded-full mx-auto mb-4"></div>
                                    <h3 class="text-lg font-semibold text-gray-700 mb-2">Redirecting to {{ strtoupper($method) }}</h3>
                                    <p class="text-sm text-gray-600 mb-4">Please wait while we redirect you to the secure payment gateway...</p>
                                    <div class="w-full bg-gray-300 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full progress-bar" style="width: 0%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Simulate Payment Buttons -->
                        <div class="space-y-3">
                            <button onclick="simulatePaymentSuccess()" 
                                class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
                                ✅ Simulate Successful Payment
                            </button>
                            <button onclick="simulatePaymentFailure()" 
                                class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
                                ❌ Simulate Payment Failure
                            </button>
                            <button onclick="cancelPayment()" 
                                class="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
                                🚫 Cancel Payment
                            </button>
                        </div>
                    @endif

                    <!-- Instructions -->
                    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                        <h4 class="text-sm font-medium text-blue-800 mb-2">Payment Instructions:</h4>
                        @if($method === 'gcash')
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• Open your GCash app</li>
                                <li>• Scan the QR code or enter the amount</li>
                                <li>• Confirm the payment</li>
                                <li>• Wait for confirmation</li>
                            </ul>
                        @elseif($method === 'maya')
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• Open your Maya app</li>
                                <li>• Select "Pay QR" or "Send Money"</li>
                                <li>• Enter the amount and confirm</li>
                                <li>• Wait for confirmation</li>
                            </ul>
                        @elseif($method === 'qrph')
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• Open your banking app</li>
                                <li>• Select "QR Payment" or "InstaPay"</li>
                                <li>• Scan the QR code above</li>
                                <li>• Confirm the payment</li>
                            </ul>
                        @else
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• You will be redirected to secure payment gateway</li>
                                <li>• Enter your payment details</li>
                                <li>• Confirm the payment</li>
                                <li>• Wait for confirmation</li>
                            </ul>
                        @endif
                    </div>
                </div>

                <!-- Footer -->
                <div class="bg-gray-50 px-6 py-4 border-t">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            Secure Payment
                        </div>
                        <button onclick="window.history.back()" class="text-sm text-blue-600 hover:text-blue-800">
                            ← Back to Booking
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const paymentRef = '{{ $ref }}';
        const paymentMethod = '{{ $method }}';
        const paymentAmount = {{ $amount }};

        // Auto-redirect simulation for non-QR payments
        @if(in_array($method, ['gcash', 'maya', 'visa', 'master', 'gpay']))
        let progress = 0;
        const progressBar = document.querySelector('.progress-bar');
        
        const progressInterval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
                progress = 100;
                clearInterval(progressInterval);
            }
            progressBar.style.width = progress + '%';
        }, 200);
        @endif

        // Generate QR code for QRPh
        @if($method === 'qrph')
        generateQRCode();
        @endif

        function generateQRCode() {
            // Simulate QR code generation
            setTimeout(() => {
                document.getElementById('qr-code').innerHTML = `
                    <div class="w-full h-full bg-white border border-gray-300 rounded flex items-center justify-center">
                        <div class="text-center">
                            <div class="text-4xl mb-2">📱</div>
                            <p class="text-xs text-gray-600">QR Code</p>
                            <p class="text-xs text-gray-500">${paymentRef}</p>
                        </div>
                    </div>
                `;
            }, 2000);
        }

        function simulatePaymentSuccess() {
            window.location.href = `/payment/success?ref=${paymentRef}`;
        }

        function simulatePaymentFailure() {
            window.location.href = `/payment/cancel?ref=${paymentRef}`;
        }

        function cancelPayment() {
            if (confirm('Are you sure you want to cancel this payment?')) {
                window.location.href = `/payment/cancel?ref=${paymentRef}`;
            }
        }

        // Check payment status periodically
        const statusInterval = setInterval(() => {
            fetch(`/payment/status?ref=${paymentRef}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.status === 'paid') {
                        clearInterval(statusInterval);
                        window.location.href = `/booking/confirmation/${data.payment_id}`;
                    }
                })
                .catch(error => console.log('Status check error:', error));
        }, 5000);
    </script>
</x-app-layout>
