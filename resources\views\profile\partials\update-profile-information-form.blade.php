<section>
    <header>
        <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
            <svg class="w-5 h-5 mr-2" style="color: #FCB404;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            {{ __('Profile Information') }}
        </h2>

        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            {{ __("Update your personal information for a better booking experience.") }}
        </p>
    </header>

    <form id="send-verification" method="post" action="{{ route('verification.send') }}">
        @csrf
    </form>

    <form method="post" action="{{ route('profile.update') }}" class="mt-6 space-y-6">
        @csrf
        @method('patch')

        <!-- Personal Information Section -->
        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <svg class="w-4 h-4 mr-2" style="color: #FCB404;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Personal Information
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <x-input-label for="name" :value="__('Full Name')" />
                    <x-text-input id="name" name="name" type="text" class="mt-1 block w-full" :value="old('name', $user->name)" required autofocus autocomplete="name" placeholder="Enter your full name" />
                    <x-input-error class="mt-2" :messages="$errors->get('name')" />
                </div>

                <div>
                    <x-input-label for="phone" :value="__('Phone Number')" />
                    <x-text-input id="phone" name="phone" type="tel" class="mt-1 block w-full" :value="old('phone', $user->phone ?? '')" autocomplete="tel" placeholder="+63 ************" />
                    <x-input-error class="mt-2" :messages="$errors->get('phone')" />
                    <p class="mt-1 text-xs text-gray-500">Used for booking confirmations and updates</p>
                </div>

                <div class="md:col-span-2">
                    <x-input-label for="email" :value="__('Email Address')" />
                    <x-text-input id="email" name="email" type="email" class="mt-1 block w-full" :value="old('email', $user->email)" required autocomplete="username" placeholder="<EMAIL>" />
                    <x-input-error class="mt-2" :messages="$errors->get('email')" />

                    @if ($user instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && ! $user->hasVerifiedEmail())
                        <div class="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                            <p class="text-sm text-yellow-800">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                {{ __('Your email address is unverified.') }}

                                <button form="send-verification" class="underline text-yellow-600 hover:text-yellow-800 ml-1">
                                    {{ __('Click here to re-send the verification email.') }}
                                </button>
                            </p>

                            @if (session('status') === 'verification-link-sent')
                                <p class="mt-2 font-medium text-sm text-green-600">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    {{ __('A new verification link has been sent to your email address.') }}
                                </p>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Additional Information Section -->
        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <svg class="w-4 h-4 mr-2" style="color: #FCB404;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                Travel Preferences
            </h3>

            <!-- Available Routes Info -->
            @if(isset($pickupLocations) && $pickupLocations->count() > 0)
                <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <div class="flex items-start">
                        <svg class="w-4 h-4 mt-0.5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-blue-800">Available GL Bus Destinations:</p>
                            <p class="text-sm text-blue-700 mt-1">
                                {{ $pickupLocations->join(' ↔ ') }}
                            </p>
                        </div>
                    </div>
                </div>
            @endif

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <x-input-label for="preferred_pickup" :value="__('Preferred Pickup Location')" />
                    <select id="preferred_pickup" name="preferred_pickup" onchange="toggleCustomPickup()" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm">
                        <option value="">Select preferred pickup location</option>
                        @if(isset($pickupLocations) && $pickupLocations->count() > 0)
                            @foreach($pickupLocations as $location)
                                <option value="{{ $location }}" {{ (old('preferred_pickup', $user->preferred_pickup ?? '') == $location) ? 'selected' : '' }}>
                                    {{ $location }}
                                </option>
                            @endforeach
                            <option value="other" {{ (old('preferred_pickup', $user->preferred_pickup ?? '') == 'other') ? 'selected' : '' }}>Other (Please specify)</option>
                        @else
                            <!-- Fallback options if no routes exist -->
                            <option value="Baguio" {{ (old('preferred_pickup', $user->preferred_pickup ?? '') == 'Baguio') ? 'selected' : '' }}>Baguio</option>
                            <option value="Tabuk" {{ (old('preferred_pickup', $user->preferred_pickup ?? '') == 'Tabuk') ? 'selected' : '' }}>Tabuk</option>
                            <option value="other" {{ (old('preferred_pickup', $user->preferred_pickup ?? '') == 'other') ? 'selected' : '' }}>Other (Please specify)</option>
                        @endif
                    </select>

                    <!-- Custom pickup location input -->
                    <div id="custom-pickup-container" class="mt-2" style="display: none;">
                        <x-text-input id="custom_pickup" name="custom_pickup" type="text" class="block w-full" :value="old('custom_pickup', '')" placeholder="Enter your preferred pickup location" />
                        <p class="mt-1 text-xs text-gray-500">Please specify your preferred pickup location</p>
                    </div>

                    <x-input-error class="mt-2" :messages="$errors->get('preferred_pickup')" />
                    <x-input-error class="mt-2" :messages="$errors->get('custom_pickup')" />
                    <p class="mt-1 text-xs text-gray-500">Choose from available GL Bus destinations or specify your own</p>
                </div>

                <script>
                function toggleCustomPickup() {
                    const select = document.getElementById('preferred_pickup');
                    const customContainer = document.getElementById('custom-pickup-container');
                    const customInput = document.getElementById('custom_pickup');

                    if (select.value === 'other') {
                        customContainer.style.display = 'block';
                        customInput.required = true;
                    } else {
                        customContainer.style.display = 'none';
                        customInput.required = false;
                        customInput.value = '';
                    }
                }

                // Check on page load if "other" is selected
                document.addEventListener('DOMContentLoaded', function() {
                    toggleCustomPickup();
                });
                </script>

                <div>
                    <x-input-label for="emergency_contact" :value="__('Emergency Contact')" />
                    <x-text-input id="emergency_contact" name="emergency_contact" type="tel" class="mt-1 block w-full" :value="old('emergency_contact', $user->emergency_contact ?? '')" placeholder="+63 ************" />
                    <x-input-error class="mt-2" :messages="$errors->get('emergency_contact')" />
                    <p class="mt-1 text-xs text-gray-500">Contact person in case of emergency</p>
                </div>

                <div class="md:col-span-2">
                    <x-input-label for="address" :value="__('Address')" />
                    <textarea id="address" name="address" rows="3" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm" placeholder="Enter your complete address">{{ old('address', $user->address ?? '') }}</textarea>
                    <x-input-error class="mt-2" :messages="$errors->get('address')" />
                </div>
            </div>
        </div>

        <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center gap-4">
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors" style="background-color: #FCB404; hover:background-color: #e6a200; focus:ring-color: #FCB404;">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    {{ __('Save Profile') }}
                </button>

                @if (session('status') === 'profile-updated')
                    <p
                        x-data="{ show: true }"
                        x-show="show"
                        x-transition
                        x-init="setTimeout(() => show = false, 3000)"
                        class="text-sm text-green-600 dark:text-green-400 flex items-center"
                    >
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        {{ __('Profile updated successfully!') }}
                    </p>
                @endif
            </div>

            <div class="text-xs text-gray-500">
                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                Your information is secure and encrypted
            </div>
        </div>
    </form>
</section>
