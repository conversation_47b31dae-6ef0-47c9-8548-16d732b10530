<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 15px;
            font-size: 11px;
            line-height: 1.3;
        }
        .header {
            text-align: center;
            margin-bottom: 25px;
            border-bottom: 2px solid #FCB404;
            padding-bottom: 15px;
        }
        .company-logo {
            font-size: 22px;
            font-weight: bold;
            color: #FCB404;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin: 8px 0;
        }
        .report-period {
            font-size: 13px;
            color: #666;
            margin-bottom: 5px;
        }
        .report-meta {
            font-size: 9px;
            color: #888;
        }
        .summary-section {
            margin: 15px 0;
        }
        .summary-cards {
            display: table;
            width: 100%;
            margin-bottom: 15px;
        }
        .summary-card {
            display: table-cell;
            width: 50%;
            padding: 12px;
            margin: 3px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            text-align: center;
        }
        .summary-card h3 {
            margin: 0 0 5px 0;
            font-size: 12px;
            color: #666;
        }
        .summary-card .value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin: 20px 0 10px 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 3px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
            font-size: 10px;
        }
        th {
            background-color: #FCB404;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .utilization-bar {
            width: 100%;
            height: 15px;
            background-color: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }
        .utilization-fill {
            height: 100%;
            background-color: #28a745;
            transition: width 0.3s ease;
        }
        .utilization-fill.low { background-color: #dc3545; }
        .utilization-fill.medium { background-color: #ffc107; }
        .utilization-fill.high { background-color: #28a745; }
        .footer {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
            font-size: 9px;
            color: #666;
            text-align: center;
        }
        .print-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        @media print {
            .print-controls {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Print Controls -->
    <div class="print-controls">
        <button onclick="window.print()" style="background: #FCB404; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 5px;">
            🖨️ Print/Save as PDF
        </button>
        <button onclick="window.close()" style="background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
            ✖️ Close
        </button>
    </div>
    <!-- Header -->
    <div class="header">
        <div class="company-logo">GL BUS RESERVATION SYSTEM</div>
        <div class="report-title">{{ $title }}</div>
        <div class="report-period">{{ $period }}</div>
        <div class="report-meta">
            Generated on: {{ $generated_at }} | Generated by: {{ $generated_by }}
        </div>
    </div>

    <!-- Summary Section -->
    <div class="summary-section">
        <h2 class="section-title">Fleet Summary</h2>
        <div class="summary-cards">
            <div class="summary-card">
                <h3>Total Buses</h3>
                <div class="value">{{ number_format($total_buses) }}</div>
            </div>
            <div class="summary-card">
                <h3>Active Buses</h3>
                <div class="value">{{ number_format($active_buses) }}</div>
            </div>
        </div>
    </div>

    <!-- Bus Utilization Details -->
    <div class="section">
        <h2 class="section-title">Bus Utilization Details</h2>
        <table>
            <thead>
                <tr>
                    <th>Bus</th>
                    <th>Capacity</th>
                    <th class="text-center">Trips</th>
                    <th class="text-center">Total Seats</th>
                    <th class="text-center">Booked</th>
                    <th class="text-center">Utilization</th>
                    <th class="text-right">Revenue</th>
                </tr>
            </thead>
            <tbody>
                @forelse($buses as $busData)
                <tr>
                    <td>
                        <strong>{{ $busData['bus']->bus_number }}</strong><br>
                        <small>{{ $busData['bus']->bus_type }}</small>
                    </td>
                    <td class="text-center">{{ $busData['bus']->capacity }}</td>
                    <td class="text-center">{{ number_format($busData['total_trips']) }}</td>
                    <td class="text-center">{{ number_format($busData['total_capacity']) }}</td>
                    <td class="text-center">{{ number_format($busData['booked_seats']) }}</td>
                    <td class="text-center">
                        <div style="width: 60px; margin: 0 auto;">
                            <div class="utilization-bar">
                                <div class="utilization-fill {{ $busData['utilization_rate'] < 30 ? 'low' : ($busData['utilization_rate'] < 70 ? 'medium' : 'high') }}" 
                                     style="width: {{ $busData['utilization_rate'] }}%"></div>
                            </div>
                            <small>{{ number_format($busData['utilization_rate'], 1) }}%</small>
                        </div>
                    </td>
                    <td class="text-right">₱{{ number_format($busData['revenue'], 2) }}</td>
                </tr>
                @empty
                <tr>
                    <td colspan="7" class="text-center">No bus utilization data available for this period</td>
                </tr>
                @endforelse
            </tbody>
            <tfoot>
                <tr style="background-color: #FCB404; color: white; font-weight: bold;">
                    <td colspan="2">TOTALS</td>
                    <td class="text-center">{{ number_format($buses->sum('total_trips')) }}</td>
                    <td class="text-center">{{ number_format($buses->sum('total_capacity')) }}</td>
                    <td class="text-center">{{ number_format($buses->sum('booked_seats')) }}</td>
                    <td class="text-center">
                        {{ $buses->sum('total_capacity') > 0 ? number_format(($buses->sum('booked_seats') / $buses->sum('total_capacity')) * 100, 1) : 0 }}%
                    </td>
                    <td class="text-right">₱{{ number_format($buses->sum('revenue'), 2) }}</td>
                </tr>
            </tfoot>
        </table>
    </div>

    <!-- Performance Analysis -->
    <div class="section">
        <h2 class="section-title">Performance Analysis</h2>
        <table>
            <thead>
                <tr>
                    <th>Performance Category</th>
                    <th class="text-center">Number of Buses</th>
                    <th class="text-center">Percentage</th>
                </tr>
            </thead>
            <tbody>
                @php
                    $highPerformers = $buses->where('utilization_rate', '>=', 70)->count();
                    $mediumPerformers = $buses->where('utilization_rate', '>=', 30)->where('utilization_rate', '<', 70)->count();
                    $lowPerformers = $buses->where('utilization_rate', '<', 30)->count();
                    $totalBusesWithData = $buses->count();
                @endphp
                <tr>
                    <td><span style="color: #28a745;">●</span> High Utilization (≥70%)</td>
                    <td class="text-center">{{ $highPerformers }}</td>
                    <td class="text-center">{{ $totalBusesWithData > 0 ? number_format(($highPerformers / $totalBusesWithData) * 100, 1) : 0 }}%</td>
                </tr>
                <tr>
                    <td><span style="color: #ffc107;">●</span> Medium Utilization (30-69%)</td>
                    <td class="text-center">{{ $mediumPerformers }}</td>
                    <td class="text-center">{{ $totalBusesWithData > 0 ? number_format(($mediumPerformers / $totalBusesWithData) * 100, 1) : 0 }}%</td>
                </tr>
                <tr>
                    <td><span style="color: #dc3545;">●</span> Low Utilization (<30%)</td>
                    <td class="text-center">{{ $lowPerformers }}</td>
                    <td class="text-center">{{ $totalBusesWithData > 0 ? number_format(($lowPerformers / $totalBusesWithData) * 100, 1) : 0 }}%</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>This report is generated automatically by GL Bus Reservation System</p>
        <p>For questions or concerns, please contact the system administrator</p>
        <p>Report generated on {{ now()->format('F d, Y h:i A') }}</p>
    </div>
</body>
</html>
