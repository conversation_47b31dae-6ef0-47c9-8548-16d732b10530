<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #FCB404;
            padding-bottom: 20px;
        }
        .company-logo {
            font-size: 24px;
            font-weight: bold;
            color: #FCB404;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin: 10px 0;
        }
        .report-date {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        .report-meta {
            font-size: 10px;
            color: #888;
        }
        .summary-section {
            margin: 20px 0;
        }
        .summary-cards {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .summary-card {
            display: table-cell;
            width: 25%;
            padding: 15px;
            margin: 5px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            text-align: center;
        }
        .summary-card h3 {
            margin: 0 0 5px 0;
            font-size: 14px;
            color: #666;
        }
        .summary-card .value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .sales-value { color: #28a745; }
        .bookings-value { color: #007bff; }
        .pending-value { color: #ffc107; }
        .cancelled-value { color: #dc3545; }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin: 25px 0 15px 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 11px;
        }
        th {
            background-color: #FCB404;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .status-paid { color: #28a745; font-weight: bold; }
        .status-pending { color: #ffc107; font-weight: bold; }
        .status-cancelled { color: #dc3545; font-weight: bold; }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 10px;
            color: #666;
            text-align: center;
        }
        .print-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        @media print {
            .print-controls {
                display: none;
            }
        }
        .payment-methods {
            display: table;
            width: 100%;
            margin: 20px 0;
        }
        .payment-method {
            display: table-cell;
            width: 33.33%;
            padding: 10px;
            text-align: center;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            margin: 2px;
        }
    </style>
</head>
<body>
    <!-- Print Controls -->
    <div class="print-controls">
        <button onclick="window.print()" style="background: #FCB404; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 5px;">
            🖨️ Print/Save as PDF
        </button>
        <button onclick="window.close()" style="background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
            ✖️ Close
        </button>
    </div>
    <!-- Header -->
    <div class="header">
        <div class="company-logo">GL BUS RESERVATION SYSTEM</div>
        <div class="report-title">{{ $title }}</div>
        <div class="report-date">{{ $date }}</div>
        <div class="report-meta">
            Generated on: {{ $generated_at }} | Generated by: {{ $generated_by }}
        </div>
    </div>

    <!-- Summary Section -->
    <div class="summary-section">
        <h2 class="section-title">Daily Sales Summary</h2>
        <div class="summary-cards">
            <div class="summary-card">
                <h3>Total Sales</h3>
                <div class="value sales-value">₱{{ number_format($total_sales, 2) }}</div>
            </div>
            <div class="summary-card">
                <h3>Total Bookings</h3>
                <div class="value bookings-value">{{ number_format($total_bookings) }}</div>
            </div>
            <div class="summary-card">
                <h3>Pending Amount</h3>
                <div class="value pending-value">₱{{ number_format($pending_amount, 2) }}</div>
            </div>
            <div class="summary-card">
                <h3>Cancelled Amount</h3>
                <div class="value cancelled-value">₱{{ number_format($cancelled_amount, 2) }}</div>
            </div>
        </div>
    </div>

    <!-- Payment Methods Breakdown -->
    <div class="section">
        <h2 class="section-title">Payment Methods Breakdown</h2>
        <div class="payment-methods">
            @forelse($payment_methods as $method)
            <div class="payment-method">
                <h4>{{ ucfirst($method->payment_method ?? 'Unknown') }}</h4>
                <p>{{ $method->count }} transactions</p>
                <p><strong>₱{{ number_format($method->total, 2) }}</strong></p>
            </div>
            @empty
            <div class="payment-method">
                <p>No payment data available</p>
            </div>
            @endforelse
        </div>
    </div>

    <!-- Detailed Transactions -->
    <div class="section">
        <h2 class="section-title">Transaction Details</h2>
        <table>
            <thead>
                <tr>
                    <th>Time</th>
                    <th>Ticket ID</th>
                    <th>Customer</th>
                    <th>Route</th>
                    <th>Payment Method</th>
                    <th class="text-center">Status</th>
                    <th class="text-right">Amount</th>
                </tr>
            </thead>
            <tbody>
                @forelse($transactions as $transaction)
                <tr>
                    <td>{{ $transaction->payment_date ? \Carbon\Carbon::parse($transaction->payment_date)->format('h:i A') : 'N/A' }}</td>
                    <td>{{ $transaction->ticket_id }}</td>
                    <td>{{ $transaction->ticket->user->name ?? 'N/A' }}</td>
                    <td>{{ $transaction->ticket->trip->route->start_point ?? 'N/A' }} → {{ $transaction->ticket->trip->route->end_point ?? 'N/A' }}</td>
                    <td>{{ ucfirst($transaction->payment_method ?? 'Unknown') }}</td>
                    <td class="text-center">
                        <span class="status-{{ $transaction->status }}">{{ ucfirst($transaction->status) }}</span>
                    </td>
                    <td class="text-right">₱{{ number_format($transaction->amount, 2) }}</td>
                </tr>
                @empty
                <tr>
                    <td colspan="7" class="text-center">No transactions found for this date</td>
                </tr>
                @endforelse
            </tbody>
            <tfoot>
                <tr style="background-color: #FCB404; color: white; font-weight: bold;">
                    <td colspan="6">TOTAL SALES</td>
                    <td class="text-right">₱{{ number_format($total_sales, 2) }}</td>
                </tr>
            </tfoot>
        </table>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>This report is generated automatically by GL Bus Reservation System</p>
        <p>For questions or concerns, please contact the system administrator</p>
        <p>Report generated on {{ now()->format('F d, Y h:i A') }}</p>
    </div>
</body>
</html>
