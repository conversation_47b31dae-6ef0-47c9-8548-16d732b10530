<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #FCB404;
            padding-bottom: 20px;
        }
        .company-logo {
            font-size: 24px;
            font-weight: bold;
            color: #FCB404;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin: 10px 0;
        }
        .report-period {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        .report-meta {
            font-size: 10px;
            color: #888;
        }
        .summary-section {
            margin: 20px 0;
        }
        .summary-cards {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .summary-card {
            display: table-cell;
            width: 25%;
            padding: 15px;
            margin: 5px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            text-align: center;
        }
        .summary-card h3 {
            margin: 0 0 5px 0;
            font-size: 14px;
            color: #666;
        }
        .summary-card .value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .revenue-value { color: #28a745; }
        .bookings-value { color: #007bff; }
        .pending-value { color: #ffc107; }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin: 25px 0 15px 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #FCB404;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 10px;
            color: #666;
            text-align: center;
        }
        .print-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        @media print {
            .print-controls {
                display: none;
            }
        }
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <!-- Print Controls -->
    <div class="print-controls">
        <button onclick="window.print()" style="background: #FCB404; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 5px;">
            🖨️ Print/Save as PDF
        </button>
        <button onclick="window.close()" style="background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
            ✖️ Close
        </button>
    </div>
    <!-- Header -->
    <div class="header">
        <div class="company-logo">GL BUS RESERVATION SYSTEM</div>
        <div class="report-title">{{ $title }}</div>
        <div class="report-period">{{ $period }}</div>
        <div class="report-meta">
            Generated on: {{ $generated_at }} | Generated by: {{ $generated_by }}
        </div>
    </div>

    <!-- Summary Section -->
    <div class="summary-section">
        <h2 class="section-title">Revenue Summary</h2>
        <div class="summary-cards">
            <div class="summary-card">
                <h3>Total Revenue</h3>
                <div class="value revenue-value">₱{{ number_format($total_revenue, 2) }}</div>
            </div>
            <div class="summary-card">
                <h3>Total Bookings</h3>
                <div class="value bookings-value">{{ number_format($total_bookings) }}</div>
            </div>
            <div class="summary-card">
                <h3>Pending Payments</h3>
                <div class="value pending-value">₱{{ number_format($pending_payments, 2) }}</div>
            </div>
            <div class="summary-card">
                <h3>Average per Booking</h3>
                <div class="value">₱{{ $total_bookings > 0 ? number_format($total_revenue / $total_bookings, 2) : '0.00' }}</div>
            </div>
        </div>
    </div>

    <!-- Daily Revenue Breakdown -->
    <div class="section">
        <h2 class="section-title">Daily Revenue Breakdown</h2>
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th class="text-center">Bookings</th>
                    <th class="text-right">Revenue</th>
                    <th class="text-right">Average per Booking</th>
                </tr>
            </thead>
            <tbody>
                @forelse($daily_revenue as $day)
                <tr>
                    <td>{{ \Carbon\Carbon::parse($day->date)->format('M d, Y') }}</td>
                    <td class="text-center">{{ number_format($day->bookings) }}</td>
                    <td class="text-right">₱{{ number_format($day->revenue, 2) }}</td>
                    <td class="text-right">₱{{ $day->bookings > 0 ? number_format($day->revenue / $day->bookings, 2) : '0.00' }}</td>
                </tr>
                @empty
                <tr>
                    <td colspan="4" class="text-center">No revenue data available for this period</td>
                </tr>
                @endforelse
            </tbody>
            <tfoot>
                <tr style="background-color: #FCB404; color: white; font-weight: bold;">
                    <td>TOTAL</td>
                    <td class="text-center">{{ number_format($total_bookings) }}</td>
                    <td class="text-right">₱{{ number_format($total_revenue, 2) }}</td>
                    <td class="text-right">₱{{ $total_bookings > 0 ? number_format($total_revenue / $total_bookings, 2) : '0.00' }}</td>
                </tr>
            </tfoot>
        </table>
    </div>

    <!-- Top Performing Routes -->
    <div class="section page-break">
        <h2 class="section-title">Top Performing Routes</h2>
        <table>
            <thead>
                <tr>
                    <th>Route</th>
                    <th class="text-center">Trips</th>
                    <th class="text-right">Revenue</th>
                    <th class="text-right">Avg per Trip</th>
                </tr>
            </thead>
            <tbody>
                @forelse($top_routes as $route)
                <tr>
                    <td>{{ $route->start_point }} → {{ $route->end_point }}</td>
                    <td class="text-center">{{ $route->trips_count }}</td>
                    <td class="text-right">₱{{ number_format($route->trips->sum('revenue') ?? 0, 2) }}</td>
                    <td class="text-right">₱{{ $route->trips_count > 0 ? number_format(($route->trips->sum('revenue') ?? 0) / $route->trips_count, 2) : '0.00' }}</td>
                </tr>
                @empty
                <tr>
                    <td colspan="4" class="text-center">No route data available for this period</td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>This report is generated automatically by GL Bus Reservation System</p>
        <p>For questions or concerns, please contact the system administrator</p>
    </div>
</body>
</html>
