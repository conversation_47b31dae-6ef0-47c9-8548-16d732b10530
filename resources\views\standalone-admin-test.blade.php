<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Standalone Admin Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-4">Standalone Admin Users Test</h1>
        
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-lg font-semibold mb-4">Authentication Status</h2>
            @auth
                <p class="text-green-600">✅ Logged in as: {{ Auth::user()->name }}</p>
                <p class="text-green-600">✅ Email: {{ Auth::user()->email }}</p>
                @if(Auth::user()->roles->first())
                    <p class="text-green-600">✅ Role: {{ Auth::user()->roles->first()->type }}</p>
                @else
                    <p class="text-red-600">❌ No role assigned</p>
                @endif
                
                @if(Auth::user()->roles->first() && Auth::user()->roles->first()->type === 'admin')
                    <p class="text-green-600">✅ Admin access granted</p>
                @else
                    <p class="text-red-600">❌ Admin access denied</p>
                @endif
            @else
                <p class="text-red-600">❌ Not logged in</p>
                <a href="/login" class="text-blue-600 underline">Click here to login</a>
            @endauth
        </div>

        @auth
        @if(Auth::user()->roles->first() && Auth::user()->roles->first()->type === 'admin')
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-lg font-semibold mb-4">Data Summary</h2>
            <p>Users count: {{ $users->count() }}</p>
            <p>Roles count: {{ $roles->count() }}</p>
        </div>

        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-lg font-semibold mb-4">Admin Buttons Test</h2>
            
            <div class="space-x-4 mb-4">
                <button id="quickCreateAgentBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                    Quick Create Agent
                </button>
                <button id="createUserBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                    Create User
                </button>
                <button onclick="testInlineClick()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded">
                    Test Inline Click
                </button>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-lg font-semibold mb-4">Debug Console</h2>
            <div id="debugOutput" class="bg-gray-100 p-4 rounded min-h-32 font-mono text-sm whitespace-pre-wrap"></div>
            <button id="clearDebug" class="mt-2 bg-gray-500 text-white px-3 py-1 rounded text-sm">Clear</button>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-4">Users List</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Role</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($users as $user)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $user->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $user->email }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($user->roles->count() > 0)
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            {{ ucfirst($user->roles->first()->type) }}
                                        </span>
                                    @else
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                            No Role
                                        </span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        @endif
        @endauth
    </div>

    <!-- Simple Modal -->
    <div id="testModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
                <h3 class="text-lg font-semibold mb-4">Test Modal - Agent Creation</h3>
                <p>This modal opened successfully! Agent button is working.</p>
                <button id="closeTestModal" class="mt-4 bg-gray-500 text-white px-4 py-2 rounded">Close</button>
            </div>
        </div>
    </div>

    <script>
        let debugCounter = 0;

        function log(message) {
            debugCounter++;
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${debugCounter}] ${timestamp}: ${message}`;
            console.log(logMessage);
            
            const output = document.getElementById('debugOutput');
            if (output) {
                output.textContent += logMessage + '\n';
                output.scrollTop = output.scrollHeight;
            }
        }

        function testInlineClick() {
            log('Inline click function called');
            alert('Inline click works!');
        }

        // Immediate log
        log('=== SCRIPT STARTED ===');
        log('Page URL: ' + window.location.href);
        log('Document ready state: ' + document.readyState);

        // Test if basic JS works
        try {
            log('JavaScript is working');
        } catch (e) {
            console.error('JavaScript error:', e);
        }

        document.addEventListener('DOMContentLoaded', function() {
            log('=== DOM CONTENT LOADED ===');

            // Find elements
            const agentBtn = document.getElementById('quickCreateAgentBtn');
            const userBtn = document.getElementById('createUserBtn');
            const modal = document.getElementById('testModal');
            const closeBtn = document.getElementById('closeTestModal');
            const clearBtn = document.getElementById('clearDebug');

            log('Looking for elements...');
            log('Agent button found: ' + (agentBtn ? 'YES' : 'NO'));
            log('User button found: ' + (userBtn ? 'YES' : 'NO'));
            log('Modal found: ' + (modal ? 'YES' : 'NO'));
            log('Close button found: ' + (closeBtn ? 'YES' : 'NO'));

            // Add event listeners with detailed logging
            if (agentBtn) {
                log('Adding click listener to agent button...');
                agentBtn.addEventListener('click', function(event) {
                    log('=== AGENT BUTTON CLICKED ===');
                    log('Event object: ' + typeof event);
                    log('Button element: ' + agentBtn.tagName);
                    
                    if (modal) {
                        log('Opening modal...');
                        modal.classList.remove('hidden');
                        log('Modal classes after remove hidden: ' + modal.className);
                    } else {
                        log('ERROR: Modal not found!');
                    }
                });
                log('Agent button listener added successfully');
            } else {
                log('ERROR: Agent button not found!');
            }

            if (userBtn) {
                log('Adding click listener to user button...');
                userBtn.addEventListener('click', function(event) {
                    log('=== USER BUTTON CLICKED ===');
                    log('Event object: ' + typeof event);
                    
                    try {
                        log('Calling Swal.fire...');
                        Swal.fire({
                            title: 'User Button Works!',
                            text: 'This confirms the user button is functional.',
                            icon: 'success'
                        });
                        log('Swal.fire called successfully');
                    } catch (e) {
                        log('ERROR calling Swal.fire: ' + e.message);
                    }
                });
                log('User button listener added successfully');
            } else {
                log('ERROR: User button not found!');
            }

            if (closeBtn) {
                closeBtn.addEventListener('click', function() {
                    log('Close button clicked');
                    if (modal) {
                        modal.classList.add('hidden');
                        log('Modal closed');
                    }
                });
            }

            if (clearBtn) {
                clearBtn.addEventListener('click', function() {
                    const output = document.getElementById('debugOutput');
                    if (output) {
                        output.textContent = '';
                        debugCounter = 0;
                    }
                    log('Debug console cleared');
                });
            }

            log('=== ALL EVENT LISTENERS ADDED ===');
            log('Setup complete. Try clicking buttons now.');
        });

        // Also log when window loads
        window.addEventListener('load', function() {
            log('=== WINDOW LOADED ===');
        });
    </script>
</body>
</html>
