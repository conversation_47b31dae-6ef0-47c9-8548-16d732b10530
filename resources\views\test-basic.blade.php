<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Test Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8">
        <div class="bg-white rounded-lg shadow p-6">
            <h1 class="text-2xl font-bold mb-4">🎫 Walk-in Booking Test</h1>
            
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-2">API Test</h2>
                <button onclick="testAPI()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Test Today's Trips API
                </button>
                <div id="api-result" class="mt-4 p-4 bg-gray-50 rounded hidden">
                    <!-- API results will show here -->
                </div>
            </div>

            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-2">Available Trips</h2>
                <div id="trips-container" class="space-y-4">
                    <div class="text-center py-4">
                        <button onclick="loadTrips()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                            Load Trips
                        </button>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-2">Debug Info</h2>
                <div id="debug-info" class="text-sm text-gray-600">
                    <p>Current URL: <span id="current-url"></span></p>
                    <p>API Base URL: <span id="api-base"></span></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Set debug info
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('api-base').textContent = window.location.origin;

        function testAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.classList.remove('hidden');
            resultDiv.innerHTML = '<div class="text-blue-600">Testing API...</div>';

            fetch('/api/agent/todays-trips')
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);
                    return response.json();
                })
                .then(data => {
                    console.log('API Response:', data);
                    resultDiv.innerHTML = `
                        <div class="text-green-600 font-semibold">API Test Successful!</div>
                        <pre class="mt-2 text-xs bg-white p-2 rounded border overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                    `;
                })
                .catch(error => {
                    console.error('API Error:', error);
                    resultDiv.innerHTML = `
                        <div class="text-red-600 font-semibold">API Test Failed!</div>
                        <div class="mt-2 text-sm">Error: ${error.message}</div>
                        <div class="mt-2 text-xs">Check browser console for more details</div>
                    `;
                });
        }

        function loadTrips() {
            const container = document.getElementById('trips-container');
            container.innerHTML = '<div class="text-center py-4 text-blue-600">Loading trips...</div>';

            fetch('/api/agent/todays-trips')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.trips) {
                        displayTrips(data.trips);
                    } else {
                        container.innerHTML = '<div class="text-red-600 text-center py-4">No trips data received</div>';
                    }
                })
                .catch(error => {
                    console.error('Error loading trips:', error);
                    container.innerHTML = '<div class="text-red-600 text-center py-4">Error loading trips: ' + error.message + '</div>';
                });
        }

        function displayTrips(trips) {
            const container = document.getElementById('trips-container');
            
            if (!trips || trips.length === 0) {
                container.innerHTML = '<div class="text-gray-600 text-center py-4">No trips available today</div>';
                return;
            }

            let html = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">';
            
            trips.forEach(trip => {
                const availableSeats = trip.total_seats - trip.booked_seats;
                
                html += `
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <h4 class="font-semibold text-gray-800">${trip.route.start_point} → ${trip.route.end_point}</h4>
                                <p class="text-sm text-gray-600">${trip.bus.name} (${trip.bus.bus_code})</p>
                            </div>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                Trip #${trip.id}
                            </span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Departure:</span>
                                <span class="font-medium">${trip.schedule.start_time}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Available Seats:</span>
                                <span class="font-medium text-green-600">${availableSeats}/${trip.total_seats}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Fare:</span>
                                <span class="font-medium text-green-600">₱${trip.fare}</span>
                            </div>
                        </div>
                        <button onclick="selectTrip(${trip.id})" class="w-full mt-3 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded text-sm transition-colors">
                            Select Trip
                        </button>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        function selectTrip(tripId) {
            alert('Trip ' + tripId + ' selected! (This is just a demo)');
        }

        // Auto-load trips when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded, testing API...');
            testAPI();
        });
    </script>
</body>
</html>
