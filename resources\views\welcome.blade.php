<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="GL Bus Reservation System - Book your bus tickets online with ease. Safe, reliable, and convenient transportation.">
    <meta name="keywords" content="bus reservation, online booking, GL Bus, travel, transportation, tickets">
    <meta name="author" content="GL Bus Reservation System">

    <title>Welcome - {{ config('app.name') }}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ asset('images/gl-logo.png') }}">
    <link rel="shortcut icon" type="image/png" href="{{ asset('images/gl-logo.png') }}">
    <link rel="apple-touch-icon" href="{{ asset('images/gl-logo.png') }}">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        :root {
            /* GL Bus Color Palette */
            --primary-gold: #FCB404;
            --primary-gold-dark: #E6A200;
            --primary-gold-light: #FFD54F;
            --secondary-blue: #1E3A8A;
            --secondary-blue-light: #3B82F6;
            --accent-green: #10B981;
            --accent-purple: #8B5CF6;
            --accent-orange: #F97316;
            --accent-teal: #14B8A6;
            --accent-red: #EF4444;

            /* Neutral Colors */
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;

            /* Gradients */
            --gradient-primary: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-dark));
            --gradient-secondary: linear-gradient(135deg, var(--secondary-blue), var(--secondary-blue-light));
            --gradient-accent: linear-gradient(135deg, var(--accent-purple), var(--accent-orange));
            --gradient-hero: linear-gradient(135deg, var(--primary-gold) 0%, var(--accent-orange) 50%, var(--secondary-blue) 100%);

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

            /* Typography */
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 1.875rem;
            --font-size-4xl: 2.25rem;

            /* Spacing */
            --spacing-1: 0.25rem;
            --spacing-2: 0.5rem;
            --spacing-3: 0.75rem;
            --spacing-4: 1rem;
            --spacing-6: 1.5rem;
            --spacing-8: 2rem;
            --spacing-12: 3rem;
            --spacing-16: 4rem;

            /* Border Radius */
            --radius-sm: 0.25rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
            --radius-xl: 0.75rem;
            --radius-2xl: 1rem;
        }

        /* Base Styles */
        body {
            font-family: 'Figtree', sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
        }

        /* Hero Background */
        .hero-bg {
            background: var(--gradient-hero);
            min-height: 100vh;
        }

        /* Glass Card Effect */
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
        }

        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-3) var(--spacing-6);
            font-size: var(--font-size-base);
            font-weight: 600;
            border-radius: var(--radius-lg);
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            outline: none;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(252, 180, 4, 0.4);
        }

        .btn-secondary {
            background: var(--gradient-secondary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(30, 58, 138, 0.4);
        }

        .btn-outline {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn-outline:hover {
            background: white;
            color: var(--gray-800);
            transform: translateY(-2px);
        }

        /* Typography */
        .text-hero {
            font-size: var(--font-size-4xl);
            font-weight: 800;
            line-height: 1.2;
        }

        .text-subtitle {
            font-size: var(--font-size-xl);
            font-weight: 400;
            opacity: 0.9;
        }

        .text-body {
            font-size: var(--font-size-base);
            line-height: 1.7;
        }

        /* Card Styles */
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-xl);
            padding: var(--spacing-8);
            text-align: center;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
            background: rgba(255, 255, 255, 0.15);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .text-hero {
                font-size: var(--font-size-3xl);
            }

            .btn {
                padding: var(--spacing-4) var(--spacing-6);
                font-size: var(--font-size-sm);
            }
        }
    </style>
</head>
<body class="antialiased">
    <div class="min-h-screen hero-bg">
        <!-- Navigation -->
        <nav class="bg-white/10 backdrop-blur-md border-b border-white/20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 flex items-center">
                            <img src="{{ asset('images/gl-logo.png') }}" alt="GL Bus Logo" class="h-10 w-10 rounded-full border-2 border-white/30 bg-white/10 backdrop-blur-sm mr-3" />
                            <h1 class="text-2xl font-bold text-white">GL Bus Reservation</h1>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        @auth
                            <a href="{{ route('home') }}" class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium">
                                Dashboard
                            </a>
                            <form method="POST" action="{{ route('logout') }}" class="inline">
                                @csrf
                                <button type="submit" class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium">
                                    Logout
                                </button>
                            </form>
                        @else
                            <a href="{{ route('login') }}" class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium">
                                Login
                            </a>
                            <a href="{{ route('register') }}" class="btn-secondary text-white px-4 py-2 rounded-md text-sm font-medium">
                                Register
                            </a>
                        @endauth
                    </div>
                </div>
            </div>
        </nav>

        <!-- Hero Section -->
        <div class="relative overflow-hidden">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
                <div class="text-center">
                    <h1 class="text-hero text-white mb-6">
                        Book Your Bus Seat
                        <span class="block text-blue-200 mt-2">Anytime, Anywhere</span>
                    </h1>
                    <p class="text-subtitle text-white mb-12 max-w-4xl mx-auto">
                        Experience hassle-free bus booking with our modern reservation system.
                        Choose your seats, pay securely, and travel comfortably with confidence.
                    </p>

                    @auth
                        <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                            <a href="{{ route('home') }}" class="btn btn-primary text-lg px-10 py-4">
                                🎫 Book Your Trip Now
                            </a>
                            <a href="{{ route('home') }}" class="btn btn-outline text-lg px-10 py-4">
                                📋 View My Bookings
                            </a>
                        </div>
                    @else
                        <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                            <a href="{{ route('register') }}" class="btn btn-primary text-lg px-10 py-4">
                                🚀 Get Started - Register Now
                            </a>
                            <a href="{{ route('login') }}" class="btn btn-outline text-lg px-10 py-4">
                                🔑 Already Have Account? Login
                            </a>
                        </div>

                        <!-- Quick Info -->
                        <div class="mt-12 text-center">
                            <p class="text-white/70 text-sm mb-4">Join thousands of satisfied customers</p>
                            <div class="flex justify-center items-center gap-8 text-white/60 text-sm">
                                <div class="flex items-center gap-2">
                                    <span class="text-green-400">✓</span>
                                    <span>Instant Booking</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="text-green-400">✓</span>
                                    <span>Secure Payment</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="text-green-400">✓</span>
                                    <span>24/7 Support</span>
                                </div>
                            </div>
                        </div>
                    @endauth
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl font-bold text-white mb-6">Why Choose Our Service?</h2>
                    <p class="text-subtitle text-white/80 max-w-2xl mx-auto">
                        Modern features designed for a seamless and secure booking experience
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- Feature 1 -->
                    <div class="feature-card">
                        <div class="text-5xl mb-6">🎯</div>
                        <h3 class="text-xl font-bold text-white mb-4">Smart Seat Selection</h3>
                        <p class="text-white/80 text-body">
                            Choose your preferred seats with our interactive seat map.
                            See real-time availability and select up to 5 seats at once.
                        </p>
                    </div>

                    <!-- Feature 2 -->
                    <div class="feature-card">
                        <div class="text-5xl mb-6">💳</div>
                        <h3 class="text-xl font-bold text-white mb-4">Secure Payments</h3>
                        <p class="text-white/80 text-body">
                            Multiple payment options including GCash, credit cards, and cash.
                            All transactions are encrypted and secure.
                        </p>
                    </div>

                    <!-- Feature 3 -->
                    <div class="feature-card">
                        <div class="text-5xl mb-6">📱</div>
                        <h3 class="text-xl font-bold text-white mb-4">24/7 Availability</h3>
                        <p class="text-white/80 text-body">
                            Book anytime online or visit our terminal for personal assistance.
                            Customer support available round the clock.
                        </p>
                    </div>
                </div>

                <!-- Additional Features -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mt-16">
                    <div class="text-center">
                        <div class="text-3xl mb-3">⚡</div>
                        <h4 class="text-white font-semibold mb-2">Instant Confirmation</h4>
                        <p class="text-white/70 text-sm">Get your ticket immediately</p>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl mb-3">🎫</div>
                        <h4 class="text-white font-semibold mb-2">Digital Tickets</h4>
                        <p class="text-white/70 text-sm">No need to print anything</p>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl mb-3">🔄</div>
                        <h4 class="text-white font-semibold mb-2">Easy Cancellation</h4>
                        <p class="text-white/70 text-sm">Flexible booking policies</p>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl mb-3">📞</div>
                        <h4 class="text-white font-semibold mb-2">Customer Support</h4>
                        <p class="text-white/70 text-sm">Help when you need it</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- CTA Section -->
        @guest
        <div class="py-16">
            <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
                <div class="glass-card p-8 rounded-2xl">
                    <h2 class="text-3xl font-bold text-white mb-4">Ready to Start Your Journey?</h2>
                    <p class="text-white/80 text-lg mb-6">Join thousands of satisfied customers who trust our service</p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ route('register') }}" class="btn-secondary text-white px-8 py-4 rounded-lg text-lg font-semibold">
                            📝 Create Account
                        </a>
                        <a href="{{ route('login') }}" class="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-gray-800 transition-all">
                            🔐 Already Have Account?
                        </a>
                    </div>
                </div>
            </div>
        </div>
        @endguest

        <!-- Footer -->
        <footer class="bg-black/20 backdrop-blur-md border-t border-white/20 py-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <p class="text-white/60">&copy; {{ date('Y') }} Bus Reservation System. All rights reserved.</p>
                    <p class="text-white/40 text-sm mt-2">Safe travels, comfortable journeys</p>
                </div>
            </div>
        </footer>
    </div>
</body>
</html>
