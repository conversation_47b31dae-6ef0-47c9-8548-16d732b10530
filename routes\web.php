<?php

use App\Http\Controllers\ProfileController;
use App\Models\Bus;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\BusController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\TripController;
use App\Http\Controllers\TicketController;

// Public welcome page
Route::get('/', function () {
    return view('welcome');
})->name('welcome');

// Get fare for route (public - no auth required)
Route::get('/fares/route/{route}', [\App\Http\Controllers\FareController::class, 'getFareForRoute'])->name('fares.route');

Route::get('/dashboard', function () {
    $user = Auth::user();

    // Load user with roles to avoid N+1 queries
    $userWithRole = \App\Models\User::with('roles')->find($user->id);

    // Get user's primary role
    $userRole = $userWithRole->roles->first();
    $roleType = $userRole ? $userRole->type : 'customer';

    $data = [
        'user' => $userWithRole,
        'user_role' => $roleType,
        'my_bookings' => \App\Models\Ticket::with('trip.route', 'trip.schedule', 'trip.bus')
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(5),
        'upcoming_trips' => \App\Models\Ticket::with('trip.route', 'trip.schedule', 'trip.bus')
            ->where('user_id', $user->id)
            ->whereHas('trip.schedule', function($q) {
                $q->where('trip_date', '>=', now()->toDateString());
            })
            ->orderBy('created_at', 'desc')
            ->take(3)
            ->get(),
        'total_bookings' => \App\Models\Ticket::where('user_id', $user->id)->count(),
        'total_spent' => \App\Models\Ticket::where('user_id', $user->id)->sum('price'),
        'available_routes' => \App\Models\Route::take(6)->get(),
    ];
    return view('dashboard-simple', compact('data'));
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    // Enhanced Booking System as Home
    Route::get('/home', [App\Http\Controllers\BookingController::class, 'index'])->name('home');
    Route::get('/legacy-home', [HomeController::class, 'index'])->name('legacy.home');
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Test route for debugging profile
    Route::get('/profile-test', function() {
        return view('profile.edit', ['user' => auth()->user()]);
    })->name('profile.test');

    // Debug route to check authentication
    Route::get('/debug-auth', function() {
        return response()->json([
            'authenticated' => auth()->check(),
            'user' => auth()->user(),
            'session' => session()->all()
        ]);
    })->name('debug.auth');

    // Enhanced Booking System Routes
    Route::get('/booking', [App\Http\Controllers\BookingController::class, 'index'])->name('booking.index');
    Route::get('/booking/demo', function() { return view('booking.demo', ['pageTitle' => 'Enhanced Booking Demo']); })->name('booking.demo');
    Route::get('/booking/test-receipt', function() { return view('booking.test-receipt', ['pageTitle' => 'Receipt Location Test']); })->name('booking.test-receipt');
    Route::get('/test-layout', function() { return view('test-layout'); })->name('test.layout');
    Route::get('/admin-simple', function() {
        $data=[
            'users' => \App\Models\User::with('roles')->paginate(10),
            'routes' => \App\Models\Route::paginate(10),
            'schedules' => \App\Models\Schedule::paginate(10),
            'buses' => \App\Models\Bus::paginate(10),
            'trips' => \App\Models\Trip::with('bus','route','schedule')->paginate(10),
            'available_trips' => \App\Models\Trip::with('bus','route','schedule')->available()->paginate(10),
            'expired_trips' => \App\Models\Trip::with('bus','route','schedule')->expired()->paginate(10),
            'tickets' => \App\Models\Ticket::with('trip')->paginate(10)
        ];
        return view('admin-simple', compact('data'));
    })->name('admin.simple');
    Route::get('/api/search-trips', [App\Http\Controllers\BookingController::class, 'searchTrips'])->name('api.search-trips');
    Route::get('/api/trip-details/{trip}', [App\Http\Controllers\BookingController::class, 'getTripDetails'])->name('api.trip-details');
    Route::post('/api/process-booking', [App\Http\Controllers\BookingController::class, 'processBooking'])->name('api.process-booking');
    Route::get('/booking-confirmation/{booking}', [App\Http\Controllers\BookingController::class, 'showConfirmation'])->name('booking.confirmation');

    // Agent API Routes (temporarily without auth for testing)
    Route::get('/api/agent/todays-trips', [App\Http\Controllers\AgentController::class, 'getTodaysTrips'])->name('api.agent.todays-trips');
    Route::get('/api/agent/search-trips', [App\Http\Controllers\AgentController::class, 'searchTrips'])->name('api.agent.search-trips');
    Route::get('/api/agent/trip-seats/{trip}', [App\Http\Controllers\AgentController::class, 'getTripSeats'])->name('api.agent.trip-seats');
    Route::get('/api/agent/trip-details/{trip}', [App\Http\Controllers\AgentController::class, 'getTripDetailsForAgent'])->name('api.agent.trip-details');
    Route::post('/api/agent/process-walk-in-booking', [App\Http\Controllers\AgentController::class, 'processWalkInBookingNew'])->name('api.agent.process-walk-in-booking');
    Route::get('/api/agent/daily-sales', [App\Http\Controllers\AgentController::class, 'getDailySales'])->name('api.agent.daily-sales');

    // Debug route to test basic functionality
    Route::get('/test-daily-sales', function() {
        try {
            $totalBookings = \App\Models\Ticket::count();
            $totalRevenue = \App\Models\payment::where('status', 'paid')->sum('amount') ?: 0;

            return response()->json([
                'success' => true,
                'debug' => [
                    'total_tickets' => $totalBookings,
                    'total_revenue' => $totalRevenue,
                    'today' => today()->format('Y-m-d'),
                    'models_exist' => [
                        'Ticket' => class_exists(\App\Models\Ticket::class),
                        'payment' => class_exists(\App\Models\payment::class),
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    });

    // Temporary test routes (remove in production)
    Route::get('/test-walk-in', function() {
        return view('agent.modern-walk-in');
    })->name('test-walk-in');

    Route::get('/test-basic', function() {
        return view('test-basic');
    })->name('test-basic');

    // Payment Gateway routes
    Route::get('/payment/gateway', [App\Http\Controllers\PaymentGatewayController::class, 'showGateway'])->name('payment.gateway');
    Route::post('/payment/callback', [App\Http\Controllers\PaymentGatewayController::class, 'processCallback'])->name('payment.callback');
    Route::get('/payment/success', [App\Http\Controllers\PaymentGatewayController::class, 'paymentSuccess'])->name('payment.success');
    Route::get('/payment/cancel', [App\Http\Controllers\PaymentGatewayController::class, 'paymentCancel'])->name('payment.cancel');
    Route::get('/payment/qr', [App\Http\Controllers\PaymentGatewayController::class, 'generateQRCode'])->name('payment.qr');
    Route::get('/payment/status', [App\Http\Controllers\PaymentGatewayController::class, 'checkPaymentStatus'])->name('payment.status');

    // Legacy booking routes (keep for backward compatibility)
    Route::post('/Book_ticket',[TicketController::class,'store_ticket'])->name('book.trip');
    Route::post('/book-seat/{trip}', [TicketController::class, 'bookSeat'])->name('book.seat');
    Route::get('/available-seats/{trip}', [TicketController::class, 'getAvailableSeats'])->name('available.seats');

    // Debug route
    Route::post('/debug-booking/{trip}', function($tripId) {
        return response()->json([
            'trip_id' => $tripId,
            'user_authenticated' => Auth::check(),
            'user_id' => Auth::id(),
            'request_data' => request()->all(),
            'csrf_token' => csrf_token()
        ]);
    })->name('debug.booking');

    // Simple test booking route
    Route::post('/test-booking/{trip}', function($tripId) {
        try {
            $user = Auth::user();
            $trip = \App\Models\Trip::find($tripId);

            if (!$trip) {
                return response()->json(['error' => 'Trip not found'], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Test booking successful',
                'trip' => $trip->id,
                'user' => $user->id,
                'bus_seats' => $trip->bus->seat,
                'route' => $trip->route->start_point . ' → ' . $trip->route->end_point
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Test failed: ' . $e->getMessage()
            ], 500);
        }
    })->name('test.booking');

    // Agent Routes
    Route::prefix('agent')->name('agent.')->middleware('auth')->group(function () {
        Route::get('/dashboard', function(\Illuminate\Http\Request $request) {
            $showAll = $request->get('view') === 'all';

            $data = [
                'trips' => $showAll
                    ? \App\Models\Trip::with(['bus', 'route', 'schedule', 'tickets'])->paginate(10)
                    : \App\Models\Trip::with(['bus', 'route', 'schedule', 'tickets'])
                        ->available() // Only show available (non-expired) trips
                        ->whereHas('schedule', function($query) {
                            $query->whereDate('trip_date', today());
                        })
                        ->paginate(10),
                'recent_bookings' => \App\Models\Ticket::with('trip', 'user', 'payments')
                    ->whereHas('payments') // Only include tickets that have payments
                    ->orderBy('created_at', 'desc')
                    ->paginate(10),
                'today_trips_count' => \App\Models\Trip::available()
                    ->whereHas('schedule', function($query) {
                        $query->whereDate('trip_date', today());
                    })->count(),
                'available_trips_count' => \App\Models\Trip::available()->count(),
                'expired_trips_count' => \App\Models\Trip::expired()->count(),
                'total_trips_count' => \App\Models\Trip::count(),
                'today_bookings' => \App\Models\Ticket::whereDate('created_at', today())->count(),
                'today_revenue' => \App\Models\payment::where('status', 'paid')
                    ->whereDate('created_at', today())
                    ->sum('amount'),
                'total_revenue' => \App\Models\payment::where('status', 'paid')->sum('amount'),
                'showing_all' => $showAll
            ];
            return view('agent-dashboard-simple', compact('data'));
        })->name('dashboard');

        Route::get('/walk-in-booking/{trip}', [App\Http\Controllers\AgentController::class, 'walkInBooking'])->name('walk-in-booking');
        Route::post('/walk-in-booking/{trip}', [App\Http\Controllers\AgentController::class, 'processWalkInBooking'])->name('process-walk-in-booking');
        Route::get('/available-seats/{trip}', [App\Http\Controllers\AgentController::class, 'getAvailableSeats'])->name('available-seats');
        Route::get('/ticket-details/{ticket}', [App\Http\Controllers\AgentController::class, 'getTicketDetails'])->name('ticket-details');
        Route::get('/print-ticket/{ticket}', [App\Http\Controllers\AgentController::class, 'printTicket'])->name('print-ticket');
        Route::get('/print-receipt/{ticket}', [App\Http\Controllers\AgentController::class, 'printReceipt'])->name('print-receipt');
        Route::get('/improved-walk-in', function() {
            return view('agent.modern-walk-in');
        })->name('improved-walk-in');
        Route::post('/complete-payment/{payment}', [App\Http\Controllers\AgentController::class, 'completePayment'])->name('complete-payment');
    });

    // Admin User Management Routes
    Route::prefix('admin')->name('admin.')->middleware('auth')->group(function () {
        Route::get('/users', [App\Http\Controllers\AdminUserController::class, 'index'])->name('users.index');
        Route::get('/users/{user}', [App\Http\Controllers\AdminUserController::class, 'show'])->name('users.show');
        Route::post('/users', [App\Http\Controllers\AdminUserController::class, 'store'])->name('users.store');
        Route::put('/users/{user}', [App\Http\Controllers\AdminUserController::class, 'update'])->name('users.update');
        Route::delete('/users/{user}', [App\Http\Controllers\AdminUserController::class, 'destroy'])->name('users.destroy');
        Route::post('/users/create-agent', [App\Http\Controllers\AdminUserController::class, 'createAgent'])->name('users.create-agent');
    });
    Route::get('/invoice/{ticket}', [TicketController::class, 'generateInvoice'])->name('generate.invoice');
    Route::get('/invoice/view/{ticket}', [TicketController::class, 'viewInvoice'])->name('view.invoice');
    Route::get('/receipt/{ticket}', [TicketController::class, 'getReceipt'])->name('get.receipt');
    Route::post('/payment-status/{ticket}', [TicketController::class, 'updatePaymentStatus'])->name('update.payment.status');
    Route::post('/cancel_ticket/{ticket}',[TicketController::class,'cancel_ticket'])->middleware('can:cancel_or_complete,ticket')->name('cancel.ticket');
    Route::post('/complete-payment/{ticket}',[TicketController::class,'complete_payment'])->middleware('can:cancel_or_complete,ticket')->name('complete.payment');

    // Fare management routes (admin only)
    Route::middleware('can:admin')->group(function () {
        Route::get('/fares', [\App\Http\Controllers\FareController::class, 'index'])->name('fares.index');
        Route::get('/fares/{fare}', [\App\Http\Controllers\FareController::class, 'show'])->name('fares.show');
        Route::post('/fares', [\App\Http\Controllers\FareController::class, 'store'])->name('fares.store');
        Route::put('/fares/{fare}', [\App\Http\Controllers\FareController::class, 'update'])->name('fares.update');
        Route::delete('/fares/{fare}', [\App\Http\Controllers\FareController::class, 'destroy'])->name('fares.destroy');
        Route::post('/fares/{fare}/activate', [\App\Http\Controllers\FareController::class, 'activate'])->name('fares.activate');
    });



    // Reports routes (admin and agent access)
    Route::middleware('auth')->group(function () {
        Route::get('/reports', [\App\Http\Controllers\ReportController::class, 'index'])->name('reports.dashboard');
        Route::get('/reports/bookings', [\App\Http\Controllers\ReportController::class, 'bookingReport'])->name('reports.bookings');
        Route::get('/reports/revenue', [\App\Http\Controllers\ReportController::class, 'revenueReport'])->name('reports.revenue');
        Route::get('/reports/utilization', [\App\Http\Controllers\ReportController::class, 'busUtilizationReport'])->name('reports.utilization');
        Route::get('/reports/export/bookings', [\App\Http\Controllers\ReportController::class, 'exportBookings'])->name('reports.export.bookings');

        // PDF Report Routes
        Route::get('/reports/pdf/monthly-revenue/{year?}/{month?}', [\App\Http\Controllers\ReportController::class, 'monthlyRevenueReport'])->name('reports.pdf.monthly-revenue');
        Route::get('/reports/pdf/daily-sales/{date?}', [\App\Http\Controllers\ReportController::class, 'dailySalesReport'])->name('reports.pdf.daily-sales');
        Route::get('/reports/pdf/bus-utilization/{month?}/{year?}', [\App\Http\Controllers\ReportController::class, 'busUtilizationReportPdf'])->name('reports.pdf.bus-utilization');

    });

    Route::get('/search', [HomeController::class, 'search'])->name('search');
    Route::get('/searchResult/{trip}', [HomeController::class, 'searchResult'])->name('SearchResult');


});
Route::middleware('adminAuth')->group(function () {
    Route::get('/admin', function() {
        $data=[
            'users' => \App\Models\User::with('roles')->paginate(10),
            'routes' => \App\Models\Route::paginate(10),
            'schedules' => \App\Models\Schedule::paginate(10),
            'buses' => \App\Models\Bus::paginate(10),
            'trips' => \App\Models\Trip::with('bus','route','schedule')->paginate(10),
            'available_trips' => \App\Models\Trip::with('bus','route','schedule')->available()->paginate(10),
            'expired_trips' => \App\Models\Trip::with('bus','route','schedule')->expired()->paginate(10),
            'tickets' => \App\Models\Ticket::with('trip')->paginate(10)
        ];
        return view('admin-simple', compact('data'));
    })->name('admin');
    Route::post('/Add_bus',[BusController::class,'store'])->name('Add_bus');
    Route::post('/Add_route',[BusController::class,'store_route'])->name('add_route');
    Route::post('/Add_schedule',[BusController::class,'store_schedule'])->name('add_schedule');
    Route::post('/Add_trip',[TripController::class,'store_trip'])->name('add_trip');
    Route::patch('/Edit_trip/{trip}',[TripController::class,'edit_trip'])->name('edit_trip');
    Route::delete('/Delete_trip/{trip}',[TripController::class,'delete_trip'])->name('delete_trip');
});

// Public debug route
Route::get('/debug-public', function() {
    return response()->json([
        'authenticated' => auth()->check(),
        'user' => auth()->user(),
        'routes' => [
            'profile.edit' => route('profile.edit'),
            'login' => route('login'),
        ]
    ]);
})->name('debug.public');

// Public profile test route (for testing the form)
Route::get('/profile-demo', function() {
    // Create a fake user for testing
    $fakeUser = new \App\Models\User([
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'phone' => '+63 ************',
        'address' => '123 Test Street, Test City',
        'preferred_pickup' => 'Baguio',
        'emergency_contact' => '+63 ************'
    ]);
    $fakeUser->id = 1;

    // Get pickup locations from routes
    $pickupLocations = collect([
        \App\Models\Route::pluck('start_point'),
        \App\Models\Route::pluck('end_point')
    ])->flatten()->unique()->sort()->values();

    return view('profile.edit', [
        'user' => $fakeUser,
        'pickupLocations' => $pickupLocations
    ]);
})->name('profile.demo');

// Simple profile test route
Route::get('/profile-simple', function() {
    // Create a fake user for testing
    $fakeUser = new \App\Models\User([
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'phone' => '+63 ************',
        'address' => '123 Test Street, Test City',
        'preferred_pickup' => 'Baguio',
        'emergency_contact' => '+63 ************'
    ]);
    $fakeUser->id = 1;

    // Get pickup locations from routes
    $pickupLocations = collect([
        \App\Models\Route::pluck('start_point'),
        \App\Models\Route::pluck('end_point')
    ])->flatten()->unique()->sort()->values();

    return view('profile.test', [
        'user' => $fakeUser,
        'pickupLocations' => $pickupLocations
    ]);
})->name('profile.simple');

require __DIR__.'/auth.php';
