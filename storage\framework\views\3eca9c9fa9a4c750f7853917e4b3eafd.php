<?php $__env->startSection('content'); ?>
<style>
/* Custom GL Bus Color Scheme */
.btn-primary {
    background-color: #FCB404 !important;
    border-color: #FCB404 !important;
    color: white !important;
}
.btn-primary:hover {
    background-color: #E6A200 !important;
    border-color: #E6A200 !important;
}
.text-primary {
    color: #FCB404 !important;
}
.bg-primary {
    background-color: #FCB404 !important;
}
.border-primary {
    border-color: #FCB404 !important;
}
.focus\:ring-primary:focus {
    --tw-ring-color: #FCB404 !important;
}
.focus\:border-primary:focus {
    border-color: #FCB404 !important;
}
input[type="radio"]:checked {
    accent-color: #FCB404 !important;
}
.step-active .step-circle {
    background-color: #FCB404 !important;
    color: white !important;
}
.step-active .step-text {
    color: #FCB404 !important;
}

/* Round Trip Styling */
.trip-card {
    transition: all 0.3s ease;
}

.trip-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.outbound-trip-card.border-blue-500 {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.return-trip-card.border-green-500 {
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.3);
}

.round-trip-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

#return-date-section {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
<div class="min-h-screen bg-gray-50">
    <!-- Header with Progress Steps -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-6xl mx-auto px-4 py-6">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-2xl font-bold text-gray-900">Book Your Trip</h1>
                <div class="text-sm text-gray-500">
                    Session expires in <span id="session-timer" class="font-semibold" style="color: #FCB404;">15:00</span>
                </div>
            </div>
            
            <!-- Progress Steps -->
            <div class="flex items-center justify-center space-x-8">
                <div class="flex items-center">
                    <div class="w-8 h-8 text-white rounded-full flex items-center justify-center text-sm font-semibold" style="background-color: #FCB404;">1</div>
                    <span class="ml-2 text-sm font-medium" style="color: #FCB404;">Select Trip</span>
                </div>
                <div class="w-16 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-semibold">2</div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Passenger Info</span>
                </div>
                <div class="w-16 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-semibold">3</div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Select Seats</span>
                </div>
                <div class="w-16 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-semibold">4</div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Summary</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Step 1: Trip Selection -->
        <div id="step-1" class="step-content">
            <!-- Date Selection Calendar -->
            <div class="bg-white rounded-lg shadow-sm border mb-6">
                <div class="p-6 border-b">
                    <h2 class="text-lg font-semibold text-gray-900">Select Travel Date</h2>
                </div>
                <div class="p-6">
                    <div class="flex items-center justify-center space-x-4 mb-6">
                        <button id="prev-week" class="p-2 hover:bg-gray-100 rounded-lg">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </button>
                        
                        <div class="grid grid-cols-7 gap-2" id="date-calendar">
                            <!-- Calendar will be populated by JavaScript -->
                        </div>
                        
                        <button id="next-week" class="p-2 hover:bg-gray-100 rounded-lg">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Route Selection -->
            <div class="bg-white rounded-lg shadow-sm border mb-6">
                <div class="p-6 border-b">
                    <h2 class="text-lg font-semibold text-gray-900">Select Route & Trip Type</h2>
                </div>
                <div class="p-6">
                    <!-- Trip Type Selection -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">Trip Type</label>
                        <div class="flex space-x-4">
                            <label class="flex items-center">
                                <input type="radio" name="trip_type" value="one_way" checked
                                       class="w-4 h-4 border-gray-300" style="accent-color: #FCB404;">
                                <span class="ml-2 text-sm font-medium text-gray-700">One Way</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="trip_type" value="round_trip"
                                       class="w-4 h-4 border-gray-300" style="accent-color: #FCB404;">
                                <span class="ml-2 text-sm font-medium text-gray-700">Round Trip</span>
                            </label>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">From</label>
                            <select id="origin" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:border-gray-400" style="--tw-ring-color: #FCB404; --tw-border-opacity: 1; border-color: rgb(209 213 219); --tw-ring-opacity: 0.5;">
                                <option value="">Select Origin</option>
                                <?php $__currentLoopData = $origins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $origin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($origin); ?>"><?php echo e($origin); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">To</label>
                            <select id="destination" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500">
                                <option value="">Select Destination</option>
                                <?php $__currentLoopData = $destinations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $destination): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($destination); ?>"><?php echo e($destination); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button id="search-trips" class="w-full btn-primary font-semibold py-2 px-4 rounded-lg transition-colors">
                                Search Trips
                            </button>
                        </div>
                    </div>

                    <!-- Return Date (for Round Trip) -->
                    <div id="return-date-section" class="mt-4 hidden">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <label class="block text-sm font-medium text-blue-800 mb-2">
                                <i class="fas fa-calendar-alt mr-2"></i>Return Date
                            </label>
                            <input type="date" id="return-date"
                                   class="w-full md:w-1/2 border border-blue-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   min="">
                            <p class="text-xs text-blue-600 mt-1">Return date must be after departure date</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Available Trips -->
            <div id="trips-container" class="bg-white rounded-lg shadow-sm border">
                <div class="p-6 border-b">
                    <h2 class="text-lg font-semibold text-gray-900">Available Trips</h2>
                    <p class="text-sm text-gray-600 mt-1">Select a trip to continue with your booking</p>
                </div>
                <div id="trips-list" class="divide-y divide-gray-200">
                    <!-- Trips will be loaded here -->
                    <div class="p-8 text-center text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z"></path>
                        </svg>
                        <p>Please select a date and route to view available trips</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 2: Passenger Information (Hidden initially) -->
        <div id="step-2" class="step-content hidden">
            <div class="bg-white rounded-lg shadow-sm border">
                <div class="p-6 border-b">
                    <h2 class="text-lg font-semibold text-gray-900">Passenger Information</h2>
                    <p class="text-sm text-gray-600 mt-1">Please provide passenger details for your booking</p>
                </div>
                <div class="p-6">
                    <form id="passenger-form">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    First Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="first_name" name="first_name" required 
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Email <span class="text-red-500">*</span>
                                </label>
                                <input type="email" id="email" name="email" required 
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Middle Name</label>
                                <input type="text" id="middle_name" name="middle_name" 
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Confirm Email <span class="text-red-500">*</span>
                                </label>
                                <input type="email" id="confirm_email" name="confirm_email" required 
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Last Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="last_name" name="last_name" required 
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Mobile No. <span class="text-red-500">*</span>
                                </label>
                                <input type="tel" id="mobile" name="mobile" required 
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    City <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="city" name="city" required 
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Full Address <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="address" name="address" required 
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            </div>
                        </div>
                        
                        <div class="flex justify-between mt-8">
                            <button type="button" id="back-to-step-1" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                ← Back to Step 1
                            </button>
                            <button type="button" id="continue-to-step-3" class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                                Next →
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Step 3: Seat Selection (Hidden initially) -->
        <div id="step-3" class="step-content hidden">
            <div class="bg-white rounded-lg shadow-sm border">
                <div class="p-6 border-b">
                    <h2 class="text-lg font-semibold text-gray-900">Select Your Seats</h2>
                    <p class="text-sm text-gray-600 mt-1">Choose your preferred seats from the available options</p>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Trip Info -->
                        <div>
                            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                                <h3 class="font-semibold text-gray-900 mb-2" id="trip-info-title">SAMPALOC → TABUK via ROXAS - SAN MATEO</h3>
                                <div class="text-sm text-gray-600 space-y-1">
                                    <div id="trip-info-date">08/22/2025 | One Way | Total Passenger: 1</div>
                                    <div id="trip-info-class">Deluxe (INNER CITIES) | 41 Seaters | 1 seat/s</div>
                                </div>
                            </div>

                            <!-- Seat Map -->
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <div class="text-center mb-4">
                                    <div class="inline-flex items-center space-x-2 bg-blue-500 text-white px-4 py-2 rounded-t-lg">
                                        <span>Driver</span>
                                        <span class="ml-4">Conductor</span>
                                    </div>
                                </div>

                                <div class="grid grid-cols-4 gap-2 max-w-sm mx-auto" id="seat-map">
                                    <!-- Seats will be populated by JavaScript -->
                                </div>

                                <!-- Legend -->
                                <div class="flex justify-center space-x-6 mt-6 text-xs">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-yellow-400 rounded mr-2"></div>
                                        <span>Priority Seats</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-red-500 rounded mr-2"></div>
                                        <span>Booked seats</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-green-500 rounded mr-2"></div>
                                        <span>Selected seats</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-gray-200 border border-gray-300 rounded mr-2"></div>
                                        <span>Available seats</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-orange-400 rounded mr-2"></div>
                                        <span>Saved Seats</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Booking Details -->
                        <div>
                            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                                <h3 class="font-semibold text-gray-900 mb-4">Selected Seats</h3>
                                <div id="selected-seats-info" class="text-sm text-gray-600">
                                    No seats selected yet
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Number of Passengers</label>
                                    <select id="passenger-count" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500">
                                        <option value="1">1 Passenger</option>
                                        <option value="2">2 Passengers</option>
                                        <option value="3">3 Passengers</option>
                                        <option value="4">4 Passengers</option>
                                        <option value="5">5 Passengers</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                                    <div class="space-y-3">
                                        <!-- QR and e-Wallets Section -->
                                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                            <h4 class="text-sm font-medium text-blue-900 mb-3 flex items-center">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                                </svg>
                                                QR and e-Wallets
                                            </h4>
                                            <div class="grid grid-cols-1 gap-3">
                                                <!-- Maya -->
                                                <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                                    <input type="radio" name="payment_method" value="maya" id="payment-maya" class="sr-only">
                                                    <div class="flex items-center w-full">
                                                        <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                                            </svg>
                                                        </div>
                                                        <div class="flex-1">
                                                            <div class="font-medium text-gray-900">Pay with Maya</div>
                                                            <div class="text-sm text-gray-500">wallet • credit</div>
                                                        </div>
                                                        <div class="w-4 h-4 border-2 border-gray-300 rounded-full payment-radio-indicator"></div>
                                                    </div>
                                                </label>

                                                <!-- QRPh -->
                                                <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                                    <input type="radio" name="payment_method" value="qrph" id="payment-qrph" class="sr-only">
                                                    <div class="flex items-center w-full">
                                                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
                                                            </svg>
                                                        </div>
                                                        <div class="flex-1">
                                                            <div class="font-medium text-gray-900">Pay with QRPh</div>
                                                            <div class="text-sm text-gray-500">• other banks</div>
                                                        </div>
                                                        <div class="w-4 h-4 border-2 border-gray-300 rounded-full payment-radio-indicator"></div>
                                                    </div>
                                                </label>

                                                <!-- GCash -->
                                                <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                                    <input type="radio" name="payment_method" value="gcash" id="payment-gcash" class="sr-only">
                                                    <div class="flex items-center w-full">
                                                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                                            </svg>
                                                        </div>
                                                        <div class="flex-1">
                                                            <div class="font-medium text-gray-900">Pay with GCash</div>
                                                            <div class="text-sm text-gray-500">Mobile wallet</div>
                                                        </div>
                                                        <div class="w-4 h-4 border-2 border-gray-300 rounded-full payment-radio-indicator"></div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Hidden select for backward compatibility -->
                                    <select id="payment-method" class="hidden">
                                        <option value="">Select Payment Method</option>
                                        <option value="maya">Maya</option>
                                        <option value="qrph">QRPh</option>
                                        <option value="gcash">GCash</option>
                                    </select>
                                </div>

                                <div id="payment-reference-div" class="hidden">
                                    <!-- GL Bus Account Details -->
                                    <div id="payment-account-details" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                                        <h4 class="font-medium text-blue-900 mb-3 flex items-center">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            Send Payment To:
                                        </h4>
                                        <div id="account-details-content" class="space-y-2">
                                            <!-- Account details will be populated by JavaScript -->
                                        </div>
                                    </div>

                                    <!-- Reference Number Input -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            Payment Reference Number
                                            <span class="text-gray-500">(Optional for cash payments)</span>
                                        </label>
                                        <input type="text" id="payment-reference" required
                                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                               placeholder="Enter reference number after payment">
                                        <p class="text-xs text-gray-500 mt-1">
                                            <strong>Note:</strong> For digital payments, you'll be redirected to the payment gateway. Reference number is optional for cash payments.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-between mt-8">
                                <button type="button" id="back-to-step-2" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                    ← Back to Step 2
                                </button>
                                <button type="button" id="continue-to-step-4" class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                                    Continue to Summary →
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 4: Booking Summary (Hidden initially) -->
        <div id="step-4" class="step-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Trip Details -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm border">
                        <div class="p-6 border-b">
                            <h2 class="text-lg font-semibold text-gray-900">Summary of Charges</h2>
                        </div>
                        <div class="p-6">
                            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                                <h3 class="font-semibold text-gray-900 mb-2 flex items-center">
                                    <span class="text-red-600 mr-2">▶</span>
                                    <span id="summary-route">SAMPALOC → TABUK via ROXAS - SAN MATEO</span>
                                </h3>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-600">Departure Schedule</span>
                                        <div class="font-semibold" id="summary-schedule">Date & Time: 06:45 PM</div>
                                    </div>
                                    <div>
                                        <span class="text-gray-600">Selected Seat Number/s:</span>
                                        <div class="font-semibold" id="summary-seats">4</div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-sm text-gray-600 mb-6">
                                *Session will expire in <span id="summary-timer" class="font-semibold text-red-600">15 minutes</span>.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fare Breakdown -->
                <div>
                    <div class="bg-red-600 text-white p-4 rounded-t-lg">
                        <h3 class="font-semibold">FARE AMOUNT</h3>
                    </div>
                    <div class="bg-white border border-t-0 rounded-b-lg p-4">
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between">
                                <span>Fare amount:</span>
                                <span id="fare-amount">₱1,363.00</span>
                            </div>
                            <div class="flex justify-between">
                                <span id="passenger-count-text">1 Passenger x</span>
                                <span id="fare-per-passenger">₱1,363.00</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Reservation fee:</span>
                                <span id="reservation-fee">₱50.00</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span id="reservation-calculation">(₱50.00) x 1 Passenger</span>
                                <span id="total-reservation">₱50.00</span>
                            </div>
                            <hr class="my-3">
                            <div class="flex justify-between font-semibold">
                                <span>Subtotal:</span>
                                <span id="subtotal">₱1,413.00</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-100 p-4 mt-4 rounded-lg">
                        <div class="flex justify-between font-bold text-lg">
                            <span>Total Amount to be Paid:</span>
                            <span id="total-amount">₱1,413.00</span>
                        </div>
                    </div>

                    <div class="mt-6">
                        <label class="flex items-center text-sm">
                            <input type="checkbox" id="terms-checkbox" class="mr-2">
                            <span>I have read and agree to the <a href="#" class="text-blue-600 hover:underline">Terms and Conditions</a>.</span>
                        </label>
                    </div>

                    <div class="flex space-x-3 mt-6">
                        <button type="button" id="back-to-step-3" class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                            ← Back
                        </button>
                        <button type="button" id="pay-now" class="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-semibold">
                            Pay Now
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Session Timer Script -->
<script>
let sessionTime = 15 * 60; // 15 minutes in seconds
let currentStep = 1;
let selectedTrip = null;
let passengerData = {};
let selectedSeats = [];
let tripPrices = {}; // Store trip prices for calculation

function updateTimer() {
    const minutes = Math.floor(sessionTime / 60);
    const seconds = sessionTime % 60;
    document.getElementById('session-timer').textContent =
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    if (sessionTime <= 0) {
        toastr.error('Session expired. Please start over.');
        setTimeout(() => {
            window.location.reload();
        }, 2000);
        return;
    }

    sessionTime--;
}

// Initialize calendar
function initCalendar() {
    const calendar = document.getElementById('date-calendar');
    const today = new Date();
    let currentWeekStart = new Date(today);
    currentWeekStart.setDate(today.getDate() - today.getDay());

    function renderWeek(startDate) {
        calendar.innerHTML = '';
        const days = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];

        for (let i = 0; i < 7; i++) {
            const date = new Date(startDate);
            date.setDate(startDate.getDate() + i);

            const dayDiv = document.createElement('div');
            dayDiv.className = `text-center p-4 border rounded-lg cursor-pointer transition-colors ${
                date.toDateString() === today.toDateString() ? 'bg-red-600 text-white' :
                date < today ? 'bg-gray-100 text-gray-400 cursor-not-allowed' :
                'bg-white hover:bg-red-50 border-gray-200'
            }`;

            if (date >= today) {
                dayDiv.onclick = () => selectDate(date);
            }

            dayDiv.innerHTML = `
                <div class="text-xs font-medium">${days[i]}</div>
                <div class="text-lg font-bold">${date.getDate()}</div>
                <div class="text-xs">${date.toLocaleDateString('en-US', { month: 'short' })}</div>
            `;

            calendar.appendChild(dayDiv);
        }
    }

    document.getElementById('prev-week').onclick = () => {
        currentWeekStart.setDate(currentWeekStart.getDate() - 7);
        renderWeek(currentWeekStart);
    };

    document.getElementById('next-week').onclick = () => {
        currentWeekStart.setDate(currentWeekStart.getDate() + 7);
        renderWeek(currentWeekStart);
    };

    renderWeek(currentWeekStart);
}

function selectDate(date) {
    // Remove previous selection
    document.querySelectorAll('#date-calendar > div').forEach(div => {
        div.classList.remove('bg-red-600', 'text-white');
        if (!div.classList.contains('bg-gray-100')) {
            div.classList.add('bg-white', 'hover:bg-red-50');
        }
    });

    // Add selection to clicked date
    event.target.closest('div').classList.remove('bg-white', 'hover:bg-red-50');
    event.target.closest('div').classList.add('bg-red-600', 'text-white');

    // Store selected date (use local date format to avoid timezone issues)
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    window.selectedDate = `${year}-${month}-${day}`;

    console.log('Selected date:', window.selectedDate, 'from date object:', date);

    // Update return date minimum if round trip is selected
    const tripType = document.querySelector('input[name="trip_type"]:checked').value;
    if (tripType === 'round_trip') {
        const returnDateInput = document.getElementById('return-date');
        const minReturnDate = new Date(date);
        minReturnDate.setDate(minReturnDate.getDate() + 1);
        returnDateInput.min = minReturnDate.toISOString().split('T')[0];

        // Clear return date if it's now invalid
        if (returnDateInput.value && new Date(returnDateInput.value) <= date) {
            returnDateInput.value = '';
            toastr.info('Return date cleared. Please select a date after departure.');
        }
    }

    // Search trips if route is selected
    if (document.getElementById('origin').value && document.getElementById('destination').value) {
        searchTrips();
    }
}

function searchTrips() {
    const origin = document.getElementById('origin').value;
    const destination = document.getElementById('destination').value;
    const date = window.selectedDate;
    const tripType = document.querySelector('input[name="trip_type"]:checked').value;
    const returnDate = document.getElementById('return-date').value;

    console.log('Searching trips with:', { origin, destination, date, tripType, returnDate });

    // Validation
    if (!origin || !destination || !date) {
        toastr.warning('Please select origin, destination, and departure date');
        return;
    }

    if (tripType === 'round_trip') {
        if (!returnDate) {
            toastr.warning('Please select return date for round trip');
            return;
        }

        if (new Date(returnDate) <= new Date(date)) {
            toastr.warning('Return date must be after departure date');
            return;
        }
    }

    // Show loading
    document.getElementById('trips-list').innerHTML = `
        <div class="p-8 text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Searching for available trips...</p>
        </div>
    `;

    // Search for outbound trips
    searchOutboundTrips(origin, destination, date, tripType, returnDate);
}

function searchOutboundTrips(origin, destination, date, tripType, returnDate) {
    fetch(`/api/search-trips?origin=${origin}&destination=${destination}&date=${date}`)
        .then(response => response.json())
        .then(data => {
            if (tripType === 'round_trip') {
                // Search for return trips
                searchReturnTrips(destination, origin, returnDate, data.trips);
            } else {
                renderTrips(data.trips, 'outbound');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('trips-list').innerHTML = `
                <div class="p-8 text-center text-red-600">
                    <p>Error loading trips. Please try again.</p>
                </div>
            `;
        });
}

function searchReturnTrips(origin, destination, date, outboundTrips) {
    fetch(`/api/search-trips?origin=${origin}&destination=${destination}&date=${date}`)
        .then(response => response.json())
        .then(data => {
            renderRoundTripResults(outboundTrips, data.trips);
        })
        .catch(error => {
            console.error('Error searching return trips:', error);
            toastr.error('Error searching return trips. Showing outbound trips only.');
            renderTrips(outboundTrips, 'outbound');
        });
}

function renderTrips(trips, type = 'outbound') {
    const tripsList = document.getElementById('trips-list');

    if (!trips || trips.length === 0) {
        tripsList.innerHTML = `
            <div class="p-8 text-center text-gray-500">
                <div class="text-gray-400 text-6xl mb-4">🚌</div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">No trips found</h3>
                <p class="text-gray-600">Try selecting different dates or routes.</p>
            </div>
        `;
        return;
    }

    // Populate tripPrices object for later use
    tripPrices = {};
    trips.forEach(trip => {
        tripPrices[trip.id] = parseFloat(trip.route.price);
    });

    const title = type === 'return' ? 'Return Trips' : 'Outbound Trips';
    const tripTypeText = type === 'return' ? 'Return' : 'One Way';

    tripsList.innerHTML = `
        <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 class="text-lg font-semibold text-blue-900">${title}</h3>
            <p class="text-sm text-blue-700">Select your preferred ${type} trip</p>
        </div>
        ${trips.map(trip => `
            <div class="border border-gray-200 rounded-lg p-6 hover:border-red-300 transition-colors mb-4 trip-card"
                 data-trip-id="${trip.id}" data-trip-type="${type}">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-4 mb-2">
                            <h3 class="text-lg font-semibold text-gray-900">${trip.route.origin} → ${trip.route.destination}</h3>
                            <span class="text-sm text-gray-500">${trip.schedule.date} | ${tripTypeText} | Total Passenger: 1</span>
                        </div>

                        <div class="grid grid-cols-4 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Departure Time</span>
                                <div class="font-semibold">${trip.schedule.departure_time}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Class</span>
                                <div class="font-semibold">${trip.bus.class || 'Regular'}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Available Seats</span>
                                <div class="font-semibold text-blue-600">${trip.available_seats}</div>
                            </div>
                            <div>
                                <span class="text-gray-600">Fare</span>
                                <div class="font-semibold text-lg text-green-600">₱${trip.route.price}</div>
                            </div>
                        </div>
                    </div>

                    <button onclick="selectTrip(${trip.id}, '${type}')"
                            class="ml-6 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-semibold">
                        Select Trip
                    </button>
                </div>
            </div>
        `).join('')}
    `;
}

function renderRoundTripResults(outboundTrips, returnTrips) {
    const tripsList = document.getElementById('trips-list');

    if ((!outboundTrips || outboundTrips.length === 0) && (!returnTrips || returnTrips.length === 0)) {
        tripsList.innerHTML = `
            <div class="p-8 text-center text-gray-500">
                <div class="text-gray-400 text-6xl mb-4">🚌</div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">No round trip combinations found</h3>
                <p class="text-gray-600">Try selecting different dates or routes.</p>
            </div>
        `;
        return;
    }

    // Store both trip types for pricing
    window.outboundTrips = outboundTrips || [];
    window.returnTrips = returnTrips || [];
    window.selectedOutboundTrip = null;
    window.selectedReturnTrip = null;

    tripsList.innerHTML = `
        <div class="space-y-6">
            <!-- Outbound Trips Section -->
            <div class="bg-white border border-gray-200 rounded-lg">
                <div class="bg-blue-50 border-b border-blue-200 p-4">
                    <h3 class="text-lg font-semibold text-blue-900">🛫 Outbound Trips</h3>
                    <p class="text-sm text-blue-700">Select your departure trip</p>
                </div>
                <div class="p-4">
                    ${outboundTrips && outboundTrips.length > 0 ?
                        outboundTrips.map(trip => `
                            <div class="border border-gray-200 rounded-lg p-4 mb-3 hover:border-blue-300 transition-colors outbound-trip-card ${window.selectedOutboundTrip === trip.id ? 'border-blue-500 bg-blue-50' : ''}"
                                 data-trip-id="${trip.id}">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-4 mb-2">
                                            <h4 class="font-semibold text-gray-900">${trip.route.origin} → ${trip.route.destination}</h4>
                                            <span class="text-sm text-gray-500">${trip.schedule.date}</span>
                                        </div>
                                        <div class="grid grid-cols-3 gap-4 text-sm">
                                            <div>
                                                <span class="text-gray-600">Departure:</span>
                                                <div class="font-semibold">${trip.schedule.departure_time}</div>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">Available Seats:</span>
                                                <div class="font-semibold text-blue-600">${trip.available_seats}</div>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">Fare:</span>
                                                <div class="font-semibold text-green-600">₱${trip.route.price}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <button onclick="selectOutboundTrip(${trip.id})"
                                            class="ml-4 px-4 py-2 ${window.selectedOutboundTrip === trip.id ? 'bg-blue-600' : 'bg-gray-600'} text-white rounded-lg hover:bg-blue-700 transition-colors">
                                        ${window.selectedOutboundTrip === trip.id ? 'Selected' : 'Select'}
                                    </button>
                                </div>
                            </div>
                        `).join('')
                        : '<div class="p-4 text-center text-gray-500">No outbound trips available</div>'
                    }
                </div>
            </div>

            <!-- Return Trips Section -->
            <div class="bg-white border border-gray-200 rounded-lg">
                <div class="bg-green-50 border-b border-green-200 p-4">
                    <h3 class="text-lg font-semibold text-green-900">🛬 Return Trips</h3>
                    <p class="text-sm text-green-700">Select your return trip</p>
                </div>
                <div class="p-4">
                    ${returnTrips && returnTrips.length > 0 ?
                        returnTrips.map(trip => `
                            <div class="border border-gray-200 rounded-lg p-4 mb-3 hover:border-green-300 transition-colors return-trip-card ${window.selectedReturnTrip === trip.id ? 'border-green-500 bg-green-50' : ''}"
                                 data-trip-id="${trip.id}">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-4 mb-2">
                                            <h4 class="font-semibold text-gray-900">${trip.route.origin} → ${trip.route.destination}</h4>
                                            <span class="text-sm text-gray-500">${trip.schedule.date}</span>
                                        </div>
                                        <div class="grid grid-cols-3 gap-4 text-sm">
                                            <div>
                                                <span class="text-gray-600">Departure:</span>
                                                <div class="font-semibold">${trip.schedule.departure_time}</div>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">Available Seats:</span>
                                                <div class="font-semibold text-blue-600">${trip.available_seats}</div>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">Fare:</span>
                                                <div class="font-semibold text-green-600">₱${trip.route.price}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <button onclick="selectReturnTrip(${trip.id})"
                                            class="ml-4 px-4 py-2 ${window.selectedReturnTrip === trip.id ? 'bg-green-600' : 'bg-gray-600'} text-white rounded-lg hover:bg-green-700 transition-colors">
                                        ${window.selectedReturnTrip === trip.id ? 'Selected' : 'Select'}
                                    </button>
                                </div>
                            </div>
                        `).join('')
                        : '<div class="p-4 text-center text-gray-500">No return trips available</div>'
                    }
                </div>
            </div>

            <!-- Continue Button -->
            <div class="text-center">
                <button id="continue-round-trip" onclick="continueWithRoundTrip()"
                        class="px-8 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-semibold disabled:bg-gray-400 disabled:cursor-not-allowed"
                        disabled>
                    Continue with Selected Trips
                </button>
                <p class="text-sm text-gray-600 mt-2">Please select both outbound and return trips to continue</p>
            </div>
        </div>
    `;
}

function selectOutboundTrip(tripId) {
    window.selectedOutboundTrip = tripId;

    // Update UI
    document.querySelectorAll('.outbound-trip-card').forEach(card => {
        const cardTripId = parseInt(card.dataset.tripId);
        const button = card.querySelector('button');

        if (cardTripId === tripId) {
            card.classList.add('border-blue-500', 'bg-blue-50');
            card.classList.remove('border-gray-200');
            button.textContent = 'Selected';
            button.classList.remove('bg-gray-600');
            button.classList.add('bg-blue-600');
        } else {
            card.classList.remove('border-blue-500', 'bg-blue-50');
            card.classList.add('border-gray-200');
            button.textContent = 'Select';
            button.classList.remove('bg-blue-600');
            button.classList.add('bg-gray-600');
        }
    });

    checkRoundTripSelection();
}

function selectReturnTrip(tripId) {
    window.selectedReturnTrip = tripId;

    // Update UI
    document.querySelectorAll('.return-trip-card').forEach(card => {
        const cardTripId = parseInt(card.dataset.tripId);
        const button = card.querySelector('button');

        if (cardTripId === tripId) {
            card.classList.add('border-green-500', 'bg-green-50');
            card.classList.remove('border-gray-200');
            button.textContent = 'Selected';
            button.classList.remove('bg-gray-600');
            button.classList.add('bg-green-600');
        } else {
            card.classList.remove('border-green-500', 'bg-green-50');
            card.classList.add('border-gray-200');
            button.textContent = 'Select';
            button.classList.remove('bg-green-600');
            button.classList.add('bg-gray-600');
        }
    });

    checkRoundTripSelection();
}

function checkRoundTripSelection() {
    const continueButton = document.getElementById('continue-round-trip');

    if (window.selectedOutboundTrip && window.selectedReturnTrip) {
        continueButton.disabled = false;
        continueButton.classList.remove('disabled:bg-gray-400', 'disabled:cursor-not-allowed');

        // Calculate total price
        const outboundTrip = window.outboundTrips.find(trip => trip.id === window.selectedOutboundTrip);
        const returnTrip = window.returnTrips.find(trip => trip.id === window.selectedReturnTrip);
        const totalPrice = parseFloat(outboundTrip.route.price) + parseFloat(returnTrip.route.price);

        continueButton.innerHTML = `Continue with Selected Trips (Total: ₱${totalPrice.toFixed(2)})`;
    } else {
        continueButton.disabled = true;
        continueButton.classList.add('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
        continueButton.innerHTML = 'Continue with Selected Trips';
    }
}

function continueWithRoundTrip() {
    if (!window.selectedOutboundTrip || !window.selectedReturnTrip) {
        toastr.warning('Please select both outbound and return trips');
        return;
    }

    // Store both trips for booking
    window.selectedTrips = {
        outbound: window.selectedOutboundTrip,
        return: window.selectedReturnTrip,
        type: 'round_trip'
    };

    // Calculate total price
    const outboundTrip = window.outboundTrips.find(trip => trip.id === window.selectedOutboundTrip);
    const returnTrip = window.returnTrips.find(trip => trip.id === window.selectedReturnTrip);
    const totalPrice = parseFloat(outboundTrip.route.price) + parseFloat(returnTrip.route.price);

    // Store trip details for round trip processing
    window.roundTripDetails = {
        outbound: outboundTrip,
        return: returnTrip,
        totalPrice: totalPrice,
        currentLeg: 'outbound', // Track which leg we're booking
        outboundSeats: [],
        returnSeats: []
    };

    // Update global variables for compatibility
    selectedTrip = window.selectedOutboundTrip; // Primary trip for seat selection
    tripPrices = {};
    tripPrices[window.selectedOutboundTrip] = parseFloat(outboundTrip.route.price);
    tripPrices[window.selectedReturnTrip] = parseFloat(returnTrip.route.price);

    // Go to passenger info first
    goToStep(2);

    toastr.success('Round trip selected! Please fill in passenger details, then select seats for both trips.');
}

function selectTrip(tripId, type = 'outbound') {
    if (type === 'outbound') {
        selectOutboundTrip(tripId);
        return;
    } else if (type === 'return') {
        selectReturnTrip(tripId);
        return;
    }

    // For one-way trips
    selectedTrip = tripId;

    // Update payment amounts immediately
    updatePaymentAmount();

    // Move to step 2
    goToStep(2);
}

function goToStep(step) {
    // Hide all steps
    document.querySelectorAll('.step-content').forEach(el => el.classList.add('hidden'));

    // Show target step
    document.getElementById(`step-${step}`).classList.remove('hidden');

    // Update progress indicators
    updateProgressIndicators(step);

    currentStep = step;
}

function updateProgressIndicators(activeStep) {
    for (let i = 1; i <= 4; i++) {
        const stepDiv = document.querySelector(`.flex.items-center.justify-center.space-x-8 > div:nth-child(${i * 2 - 1})`);
        const circle = stepDiv.querySelector('div');
        const text = stepDiv.querySelector('span');

        if (i <= activeStep) {
            circle.className = 'w-8 h-8 text-white rounded-full flex items-center justify-center text-sm font-semibold';
            circle.style.backgroundColor = '#FCB404';
            text.className = 'ml-2 text-sm font-medium';
            text.style.color = '#FCB404';
        } else {
            circle.className = 'w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-semibold';
            circle.style.backgroundColor = '';
            text.className = 'ml-2 text-sm font-medium text-gray-500';
            text.style.color = '';
        }
    }
}

// Configure toastr
toastr.options = {
    "closeButton": true,
    "debug": false,
    "newestOnTop": true,
    "progressBar": true,
    "positionClass": "toast-top-right",
    "preventDuplicates": false,
    "onclick": null,
    "showDuration": "300",
    "hideDuration": "1000",
    "timeOut": "5000",
    "extendedTimeOut": "1000",
    "showEasing": "swing",
    "hideEasing": "linear",
    "showMethod": "fadeIn",
    "hideMethod": "fadeOut"
};

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    initCalendar();

    // Trip type change handler
    document.querySelectorAll('input[name="trip_type"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const returnDateSection = document.getElementById('return-date-section');
            const returnDateInput = document.getElementById('return-date');

            if (this.value === 'round_trip') {
                returnDateSection.classList.remove('hidden');

                // Set minimum return date to tomorrow if departure date is selected
                if (window.selectedDate) {
                    const departureDate = new Date(window.selectedDate);
                    departureDate.setDate(departureDate.getDate() + 1);
                    returnDateInput.min = departureDate.toISOString().split('T')[0];
                }

                // Clear any existing search results when switching to round trip
                document.getElementById('trips-list').innerHTML = `
                    <div class="p-8 text-center text-gray-500">
                        <p>Please search for round trip options</p>
                    </div>
                `;
            } else {
                returnDateSection.classList.add('hidden');
                returnDateInput.value = '';

                // Clear search results when switching to one way
                document.getElementById('trips-list').innerHTML = `
                    <div class="p-8 text-center text-gray-500">
                        <p>Please search for one way trips</p>
                    </div>
                `;
            }
        });
    });

    document.getElementById('search-trips').onclick = searchTrips;

    document.getElementById('back-to-step-1').onclick = () => goToStep(1);

    document.getElementById('continue-to-step-3').onclick = function() {
        // Validate passenger form
        const form = document.getElementById('passenger-form');
        const formData = new FormData(form);

        // Basic validation
        const required = ['first_name', 'last_name', 'email', 'confirm_email', 'mobile', 'city', 'address'];
        let isValid = true;

        required.forEach(field => {
            const value = formData.get(field);
            if (!value || value.trim() === '') {
                isValid = false;
                document.getElementById(field).classList.add('border-red-500');
            } else {
                document.getElementById(field).classList.remove('border-red-500');
            }
        });

        // Email confirmation
        if (formData.get('email') !== formData.get('confirm_email')) {
            isValid = false;
            document.getElementById('confirm_email').classList.add('border-red-500');
            toastr.error('Email addresses do not match');
        }

        if (isValid) {
            // Store passenger data
            passengerData = Object.fromEntries(formData);

            // Load seat selection for step 3
            loadSeatSelection();
            goToStep(3);
        } else {
            toastr.error('Please fill in all required fields correctly');
        }
    };

    // Step 3 event listeners
    document.getElementById('back-to-step-2').onclick = () => goToStep(2);

    document.getElementById('passenger-count').onchange = function() {
        const count = parseInt(this.value);
        // Clear selected seats if count changed
        selectedSeats = [];
        updateSelectedSeatsDisplay();

        // Update seat selection limit
        window.maxPassengers = count;

        // Update payment amounts
        updatePaymentAmount();
    };

    // Payment method radio button handlers
    document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
        radio.addEventListener('change', function() {
            // Update hidden select for backward compatibility
            document.getElementById('payment-method').value = this.value;

            // Update visual indicators
            document.querySelectorAll('.payment-radio-indicator').forEach(indicator => {
                indicator.classList.remove('bg-blue-500', 'border-blue-500');
                indicator.classList.add('border-gray-300');
                indicator.innerHTML = '';
            });

            // Mark selected option
            const selectedIndicator = this.closest('label').querySelector('.payment-radio-indicator');
            selectedIndicator.classList.remove('border-gray-300');
            selectedIndicator.classList.add('bg-blue-500', 'border-blue-500');
            selectedIndicator.innerHTML = '<svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="3"/></svg>';

            // Show/hide reference number field and account details for digital payments
            const referenceDiv = document.getElementById('payment-reference-div');
            const accountDetailsContent = document.getElementById('account-details-content');

            if (this.value === 'gcash' || this.value === 'maya' || this.value === 'qrph') {
                // For gateway payments, hide reference input since payment will be processed through gateway
                referenceDiv.classList.add('hidden');
                document.getElementById('payment-reference').required = false;

                // Show account details based on payment method
                let accountDetails = '';
                if (this.value === 'gcash') {
                    accountDetails = `
                        <div class="bg-white p-3 rounded border">
                            <div class="flex items-center mb-2">
                                <div class="w-6 h-6 bg-blue-600 rounded flex items-center justify-center mr-2">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <strong class="text-blue-900">GCash Account</strong>
                            </div>
                            <div class="text-sm space-y-1">
                                <div><strong>Number:</strong> ***********</div>
                                <div><strong>Name:</strong> GL Bus Transport Corp</div>
                                <div><strong>Amount:</strong> <span class="font-bold text-green-600">₱<span id="gcash-amount">0.00</span></span></div>
                            </div>
                        </div>
                    `;
                } else if (this.value === 'maya') {
                    accountDetails = `
                        <div class="bg-white p-3 rounded border">
                            <div class="flex items-center mb-2">
                                <div class="w-6 h-6 bg-green-500 rounded flex items-center justify-center mr-2">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                    </svg>
                                </div>
                                <strong class="text-green-900">Maya Account</strong>
                            </div>
                            <div class="text-sm space-y-1">
                                <div><strong>Number:</strong> ***********</div>
                                <div><strong>Name:</strong> GL Bus Transport Corp</div>
                                <div><strong>Amount:</strong> <span class="font-bold text-green-600">₱<span id="maya-amount">0.00</span></span></div>
                            </div>
                        </div>
                    `;
                } else if (this.value === 'qrph') {
                    accountDetails = `
                        <div class="bg-white p-3 rounded border">
                            <div class="flex items-center mb-2">
                                <div class="w-6 h-6 bg-purple-500 rounded flex items-center justify-center mr-2">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
                                    </svg>
                                </div>
                                <strong class="text-purple-900">QRPh Payment</strong>
                            </div>
                            <div class="text-sm space-y-1">
                                <div><strong>Account Name:</strong> GL Bus Transport Corp</div>
                                <div><strong>Bank Account:</strong> BPI - **********</div>
                                <div><strong>Amount:</strong> <span class="font-bold text-green-600">₱<span id="qrph-amount">0.00</span></span></div>
                            </div>
                        </div>
                    `;
                }
                accountDetailsContent.innerHTML = accountDetails;

                // Update amount in account details
                updatePaymentAmount();
            } else if (this.value === 'cash') {
                // For cash payments, show reference field (optional)
                referenceDiv.classList.remove('hidden');
                document.getElementById('payment-reference').placeholder = 'Enter cash payment reference (optional)';
                document.getElementById('payment-reference').required = false;

                // Clear account details for cash
                accountDetailsContent.innerHTML = `
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <div class="w-4 h-4 bg-green-600 rounded flex items-center justify-center mr-2">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <strong class="text-green-900">Cash Payment</strong>
                        </div>
                        <div class="text-sm text-green-800">
                            <p>Pay in cash at the terminal or to the conductor.</p>
                            <p class="mt-1"><strong>Note:</strong> Please arrive early for cash payments.</p>
                        </div>
                    </div>
                `;
            } else {
                referenceDiv.classList.add('hidden');
                document.getElementById('payment-reference').required = false;
            }
        });
    });

    document.getElementById('continue-to-step-4').onclick = function() {
        // Validate seat selection
        const passengerCount = parseInt(document.getElementById('passenger-count').value);
        const paymentMethod = document.getElementById('payment-method').value;

        if (selectedSeats.length === 0) {
            toastr.warning('Please select at least one seat');
            return;
        }

        if (selectedSeats.length !== passengerCount) {
            toastr.warning(`Please select exactly ${passengerCount} seat(s)`);
            return;
        }

        if (!paymentMethod) {
            toastr.warning('Please select a payment method');
            return;
        }

        // Check if this is a round trip
        const isRoundTrip = window.selectedTrips && window.selectedTrips.type === 'round_trip';

        if (isRoundTrip) {
            handleRoundTripSeatSelection();
            return;
        }

        // Load booking summary for one-way trip
        loadBookingSummary();
        goToStep(4);
    };

    // Step 4 event listeners
    document.getElementById('back-to-step-3').onclick = () => goToStep(3);

    document.getElementById('pay-now').onclick = function() {
        const termsChecked = document.getElementById('terms-checkbox').checked;

        if (!termsChecked) {
            toastr.warning('Please agree to the Terms and Conditions');
            return;
        }

        // Process booking
        processBooking();
    };
});

function loadSeatSelection() {
    // Check if this is a round trip
    const isRoundTrip = window.selectedTrips && window.selectedTrips.type === 'round_trip';

    if (isRoundTrip) {
        loadRoundTripSeatSelection();
        return;
    }

    // Regular one-way trip seat selection
    fetch(`/api/trip-details/${selectedTrip}`)
        .then(response => response.json())
        .then(data => {
            const trip = data.trip;

            // Update trip info
            document.getElementById('trip-info-title').textContent =
                `${trip.route.origin} → ${trip.route.destination}`;
            document.getElementById('trip-info-date').textContent =
                `${trip.schedule.date} | One Way | Total Passenger: 1`;
            document.getElementById('trip-info-class').textContent =
                `${trip.bus.class || 'Regular'} | ${trip.bus.seat} Seaters | 1 seat/s`;

            // Render seat map
            renderSeatMap(trip.bus.seat, data.bookedSeats);
        })
        .catch(error => {
            console.error('Error loading trip details:', error);
        });
}

function loadRoundTripSeatSelection() {
    const currentLeg = window.roundTripDetails.currentLeg;
    const tripId = currentLeg === 'outbound' ? window.selectedTrips.outbound : window.selectedTrips.return;
    const tripDetails = currentLeg === 'outbound' ? window.roundTripDetails.outbound : window.roundTripDetails.return;

    // Update the seat selection header to show round trip progress
    const seatSelectionHeader = document.querySelector('#step-3 .p-6.border-b h2');
    const seatSelectionSubtext = document.querySelector('#step-3 .p-6.border-b p');

    if (currentLeg === 'outbound') {
        seatSelectionHeader.textContent = 'Select Outbound Trip Seats (1/2)';
        seatSelectionSubtext.textContent = 'Choose your seats for the outbound journey';
    } else {
        seatSelectionHeader.textContent = 'Select Return Trip Seats (2/2)';
        seatSelectionSubtext.textContent = 'Choose your seats for the return journey';
    }

    // Fetch trip details and seat map
    fetch(`/api/trip-details/${tripId}`)
        .then(response => response.json())
        .then(data => {
            const trip = data.trip;

            // Update trip info
            document.getElementById('trip-info-title').textContent =
                `${trip.route.origin} → ${trip.route.destination}`;
            document.getElementById('trip-info-date').textContent =
                `${trip.schedule.date} | Round Trip (${currentLeg === 'outbound' ? 'Outbound' : 'Return'}) | Total Passenger: 1`;
            document.getElementById('trip-info-class').textContent =
                `${trip.bus.class || 'Regular'} | ${trip.bus.seat} Seaters | 1 seat/s`;

            // Clear previous seat selections for this leg
            selectedSeats = [];
            updateSelectedSeatsDisplay();

            // Render seat map
            renderSeatMap(trip.bus.seat, data.bookedSeats);

            // Update continue button text
            const continueBtn = document.getElementById('continue-to-step-4');
            if (currentLeg === 'outbound') {
                continueBtn.textContent = 'Continue to Return Trip Seats';
            } else {
                continueBtn.textContent = 'Continue to Payment';
            }
        })
        .catch(error => {
            console.error('Error loading trip details:', error);
            toastr.error('Error loading trip details. Please try again.');
        });
}

function handleRoundTripSeatSelection() {
    const currentLeg = window.roundTripDetails.currentLeg;

    if (currentLeg === 'outbound') {
        // Store outbound seats and move to return trip
        window.roundTripDetails.outboundSeats = [...selectedSeats];
        window.roundTripDetails.currentLeg = 'return';

        toastr.success(`Outbound seats selected: ${selectedSeats.join(', ')}. Now select return trip seats.`);

        // Load return trip seat selection
        loadRoundTripSeatSelection();

    } else {
        // Store return seats and proceed to payment
        window.roundTripDetails.returnSeats = [...selectedSeats];

        toastr.success(`Return seats selected: ${selectedSeats.join(', ')}. Proceeding to payment.`);

        // Load booking summary with both trips
        loadRoundTripBookingSummary();
        goToStep(4);
    }
}

function loadRoundTripBookingSummary() {
    const outboundTrip = window.roundTripDetails.outbound;
    const returnTrip = window.roundTripDetails.return;
    const outboundSeats = window.roundTripDetails.outboundSeats;
    const returnSeats = window.roundTripDetails.returnSeats;
    const totalPrice = window.roundTripDetails.totalPrice;

    // Update the trip summary section for round trip
    const summaryRouteElement = document.getElementById('summary-route');
    const summaryScheduleElement = document.getElementById('summary-schedule');
    const summarySeatsElement = document.getElementById('summary-seats');

    // Update route display
    summaryRouteElement.innerHTML = `
        <div class="space-y-2">
            <div class="text-blue-800">🛫 ${outboundTrip.route.origin} → ${outboundTrip.route.destination}</div>
            <div class="text-green-800">🛬 ${returnTrip.route.origin} → ${returnTrip.route.destination}</div>
        </div>
    `;

    // Update schedule display
    summaryScheduleElement.innerHTML = `
        <div class="space-y-1">
            <div>Outbound: ${outboundTrip.schedule.date} at ${outboundTrip.schedule.departure_time}</div>
            <div>Return: ${returnTrip.schedule.date} at ${returnTrip.schedule.departure_time}</div>
        </div>
    `;

    // Update seats display
    summarySeatsElement.innerHTML = `
        <div class="space-y-1">
            <div>Outbound: ${outboundSeats.join(', ')}</div>
            <div>Return: ${returnSeats.join(', ')}</div>
        </div>
    `;

    // Update fare breakdown
    const fareAmount = document.getElementById('fare-amount');
    const farePerPassenger = document.getElementById('fare-per-passenger');
    const passengerCountText = document.getElementById('passenger-count-text');
    const subtotal = document.getElementById('subtotal');
    const totalAmount = document.getElementById('total-amount');

    const totalSeats = outboundSeats.length + returnSeats.length;
    const reservationFee = 50.00;
    const totalWithReservation = totalPrice + reservationFee;

    fareAmount.textContent = `₱${totalPrice.toFixed(2)}`;
    farePerPassenger.textContent = `₱${totalPrice.toFixed(2)}`;
    passengerCountText.textContent = `Round Trip (${totalSeats} seats) x`;
    subtotal.textContent = `₱${totalWithReservation.toFixed(2)}`;
    totalAmount.textContent = `₱${totalWithReservation.toFixed(2)}`;

    // Update reservation fee calculation
    document.getElementById('reservation-calculation').textContent = `(₱50.00) x Round Trip`;
    document.getElementById('total-reservation').textContent = `₱${reservationFee.toFixed(2)}`;
}

function renderSeatMap(totalSeats, bookedSeats) {
    const seatMap = document.getElementById('seat-map');
    seatMap.innerHTML = '';

    for (let i = 1; i <= totalSeats; i++) {
        const seatBtn = document.createElement('button');
        seatBtn.type = 'button';
        seatBtn.textContent = i;
        seatBtn.dataset.seat = i;

        const isBooked = bookedSeats.includes(i);
        const isPriority = [1, 2, 3, 4].includes(i); // First 4 seats are priority

        if (isBooked) {
            seatBtn.className = 'w-12 h-12 bg-red-500 text-white rounded border-2 border-red-600 cursor-not-allowed';
            seatBtn.disabled = true;
        } else if (isPriority) {
            seatBtn.className = 'w-12 h-12 bg-yellow-400 text-black rounded border-2 border-yellow-500 hover:bg-yellow-500 transition-colors';
        } else {
            seatBtn.className = 'w-12 h-12 bg-gray-200 text-gray-800 rounded border-2 border-gray-300 hover:bg-gray-300 transition-colors';
        }

        seatBtn.onclick = () => toggleSeat(i, seatBtn);
        seatMap.appendChild(seatBtn);
    }
}

function toggleSeat(seatNumber, seatBtn) {
    const maxPassengers = parseInt(document.getElementById('passenger-count').value);
    const isSelected = selectedSeats.includes(seatNumber);

    if (isSelected) {
        // Deselect seat
        selectedSeats = selectedSeats.filter(seat => seat !== seatNumber);
        seatBtn.classList.remove('bg-green-500', 'border-green-600');

        if ([1, 2, 3, 4].includes(seatNumber)) {
            seatBtn.className = 'w-12 h-12 bg-yellow-400 text-black rounded border-2 border-yellow-500 hover:bg-yellow-500 transition-colors';
        } else {
            seatBtn.className = 'w-12 h-12 bg-gray-200 text-gray-800 rounded border-2 border-gray-300 hover:bg-gray-300 transition-colors';
        }
    } else {
        // Select seat if under limit
        if (selectedSeats.length < maxPassengers) {
            selectedSeats.push(seatNumber);
            seatBtn.className = 'w-12 h-12 bg-green-500 text-white rounded border-2 border-green-600';
        } else {
            toastr.warning(`You can only select ${maxPassengers} seat(s)`);
            return;
        }
    }

    updateSelectedSeatsDisplay();
}

function updateSelectedSeatsDisplay() {
    const display = document.getElementById('selected-seats-info');

    if (selectedSeats.length === 0) {
        display.textContent = 'No seats selected yet';
    } else {
        display.innerHTML = `
            <div class="font-semibold">Selected Seats: ${selectedSeats.join(', ')}</div>
            <div class="text-xs text-gray-500 mt-1">${selectedSeats.length} seat(s) selected</div>
        `;
    }

    // Update payment amounts when seats change
    updatePaymentAmount();
}

function updatePaymentAmount() {
    // Get passenger count from dropdown or selected seats
    const passengerCountElement = document.getElementById('passenger-count');
    const passengerCount = passengerCountElement ? parseInt(passengerCountElement.value) : selectedSeats.length;

    if (selectedTrip && tripPrices[selectedTrip] && passengerCount > 0) {
        const farePerSeat = tripPrices[selectedTrip];
        const reservationFee = 50.00;
        const totalAmount = (farePerSeat * passengerCount + reservationFee).toFixed(2);

        // Update amount in payment account details
        const gcashAmount = document.getElementById('gcash-amount');
        const mayaAmount = document.getElementById('maya-amount');
        const qrphAmount = document.getElementById('qrph-amount');

        if (gcashAmount) gcashAmount.textContent = totalAmount;
        if (mayaAmount) mayaAmount.textContent = totalAmount;
        if (qrphAmount) qrphAmount.textContent = totalAmount;

        console.log('Updated payment amount:', totalAmount, 'for', passengerCount, 'passengers');
    } else {
        console.log('Cannot update payment amount - missing data:', {
            selectedTrip,
            tripPrice: tripPrices[selectedTrip],
            passengerCount
        });
    }
}

function loadBookingSummary() {
    // Update summary with booking details
    fetch(`/api/trip-details/${selectedTrip}`)
        .then(response => response.json())
        .then(data => {
            const trip = data.trip;
            const passengerCount = selectedSeats.length;
            const farePerSeat = parseFloat(trip.route.price);
            const reservationFee = 50.00;

            // Update summary details
            document.getElementById('summary-route').textContent =
                `${trip.route.origin} → ${trip.route.destination}`;
            document.getElementById('summary-schedule').textContent =
                `Date & Time: ${trip.schedule.departure_time}`;
            document.getElementById('summary-seats').textContent = selectedSeats.join(', ');

            // Update fare breakdown
            document.getElementById('fare-amount').textContent = `₱${(farePerSeat * passengerCount).toFixed(2)}`;
            document.getElementById('passenger-count-text').textContent = `${passengerCount} Passenger${passengerCount > 1 ? 's' : ''} x`;
            document.getElementById('fare-per-passenger').textContent = `₱${farePerSeat.toFixed(2)}`;
            document.getElementById('reservation-fee').textContent = `₱${reservationFee.toFixed(2)}`;
            document.getElementById('reservation-calculation').textContent = `(₱${reservationFee.toFixed(2)}) x ${passengerCount} Passenger${passengerCount > 1 ? 's' : ''}`;
            document.getElementById('total-reservation').textContent = `₱${(reservationFee * passengerCount).toFixed(2)}`;

            const subtotal = (farePerSeat * passengerCount) + (reservationFee * passengerCount);
            document.getElementById('subtotal').textContent = `₱${subtotal.toFixed(2)}`;
            document.getElementById('total-amount').textContent = `₱${subtotal.toFixed(2)}`;
        });
}

function processBooking() {
    // Check if this is a round trip booking
    const isRoundTrip = window.selectedTrips && window.selectedTrips.type === 'round_trip';

    let bookingData;

    if (isRoundTrip) {
        // Calculate total amount including reservation fee
        const totalSeats = window.roundTripDetails.outboundSeats.length + window.roundTripDetails.returnSeats.length;
        const reservationFee = 50.00;
        const totalAmount = window.roundTripDetails.totalPrice + reservationFee;

        // Round trip booking data
        bookingData = {
            booking_type: 'round_trip',
            outbound_trip_id: window.selectedTrips.outbound,
            return_trip_id: window.selectedTrips.return,
            outbound_seats: window.roundTripDetails.outboundSeats.join(','),
            return_seats: window.roundTripDetails.returnSeats.join(','),
            passenger_data: passengerData,
            payment_method: document.getElementById('payment-method').value,
            payment_reference: document.getElementById('payment-reference').value || null,
            total_amount: totalAmount
        };
    } else {
        // One-way trip booking data
        bookingData = {
            booking_type: 'one_way',
            trip_id: selectedTrip,
            passenger_data: passengerData,
            seat_numbers: selectedSeats.join(','),
            payment_method: document.getElementById('payment-method').value,
            payment_reference: document.getElementById('payment-reference').value || null
        };
    }

    // Show loading
    document.getElementById('pay-now').innerHTML = 'Processing...';
    document.getElementById('pay-now').disabled = true;

    fetch('/api/process-booking', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(bookingData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Check if payment gateway redirect is required
            if (data.payment_gateway && data.payment_gateway.redirect_required) {
                toastr.success('Booking created! Redirecting to payment gateway...');
                setTimeout(() => {
                    window.location.href = data.redirect_url;
                }, 1500);
            } else if (data.payment_gateway && data.payment_gateway.show_qr) {
                // Handle QR code display
                toastr.success('Booking created! Please scan the QR code to complete payment');
                setTimeout(() => {
                    window.location.href = data.redirect_url || `/booking-confirmation/${data.booking_id}`;
                }, 1500);
            } else {
                // Regular confirmation redirect (cash payments)
                toastr.success('Booking successful! Redirecting to confirmation page...');
                setTimeout(() => {
                    window.location.href = `/booking-confirmation/${data.booking_id}`;
                }, 1500);
            }
        } else {
            toastr.error('Booking failed: ' + data.message);
            document.getElementById('pay-now').innerHTML = 'Pay Now';
            document.getElementById('pay-now').disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toastr.error('An error occurred while processing your booking');
        document.getElementById('pay-now').innerHTML = 'Pay Now';
        document.getElementById('pay-now').disabled = false;
    });
}

setInterval(updateTimer, 1000);
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\XAMPP\htdocs\GL_BUS\resources\views/booking/index.blade.php ENDPATH**/ ?>