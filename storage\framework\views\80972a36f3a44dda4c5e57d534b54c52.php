<?php
$pageTitle = 'Agent Walk-in Booking';
?>

<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    🎫 Walk-in Booking Terminal
                </h2>
                <p class="text-sm text-gray-600 mt-1">Book tickets for walk-in customers</p>
            </div>
            <div class="flex space-x-3">
                <button onclick="showAdvancedBooking()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    📅 Advanced Booking
                </button>
                <a href="<?php echo e(route('agent.dashboard')); ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                    ← Back to Dashboard
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Booking Type Selection -->
            <div class="bg-white rounded-lg shadow-sm border mb-6">
                <div class="p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">📋 Booking Type</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="booking-type-card active" data-type="today" onclick="selectBookingType('today')">
                            <div class="p-4 border-2 border-blue-500 bg-blue-50 rounded-lg cursor-pointer transition-all">
                                <div class="flex items-center">
                                    <div class="p-2 bg-blue-100 rounded-full mr-3">
                                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-blue-900">Today's Trips</h4>
                                        <p class="text-sm text-blue-700">Book for today's available trips</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="booking-type-card" data-type="advanced" onclick="selectBookingType('advanced')">
                            <div class="p-4 border-2 border-gray-300 bg-gray-50 rounded-lg cursor-pointer transition-all">
                                <div class="flex items-center">
                                    <div class="p-2 bg-gray-100 rounded-full mr-3">
                                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-700">Advanced Booking</h4>
                                        <p class="text-sm text-gray-600">Book for future dates</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Booking Date Selection (Hidden by default) -->
            <div id="advanced-booking-section" class="bg-white rounded-lg shadow-sm border mb-6 hidden">
                <div class="p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">📅 Select Travel Date</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Travel Date</label>
                            <input type="date" id="travel-date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" min="<?php echo e(date('Y-m-d')); ?>">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Origin</label>
                            <select id="origin-select" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select Origin</option>
                                <option value="Tabuk">Tabuk</option>
                                <option value="Baguio">Baguio</option>
                                <option value="Manila">Manila</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Destination</label>
                            <select id="destination-select" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select Destination</option>
                                <option value="Tabuk">Tabuk</option>
                                <option value="Baguio">Baguio</option>
                                <option value="Manila">Manila</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button onclick="searchAdvancedTrips()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                            🔍 Search Trips
                        </button>
                    </div>
                </div>
            </div>

            <!-- Trip Selection -->
            <div class="bg-white rounded-lg shadow-sm border mb-6">
                <div class="p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">🚌 Available Trips</h3>
                    <p class="text-sm text-gray-600 mt-1" id="trips-subtitle">Today's available trips</p>
                </div>
                <div class="p-6">
                    <div id="trips-container">
                        <div class="text-center py-8">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                            <p class="text-gray-600 mt-2">Loading trips...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Information Form (Hidden until trip is selected) -->
            <div id="customer-form-section" class="bg-white rounded-lg shadow-sm border mb-6 hidden">
                <div class="p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">👤 Customer Information</h3>
                </div>
                <div class="p-6">
                    <form id="booking-form">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                                <input type="text" id="first-name" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                                <input type="text" id="last-name" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                <input type="email" id="email" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Mobile Number *</label>
                                <input type="tel" id="mobile" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Address *</label>
                                <textarea id="address" required rows="2" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Seat Selection (Hidden until customer info is filled) -->
            <div id="seat-selection-section" class="bg-white rounded-lg shadow-sm border mb-6 hidden">
                <div class="p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">💺 Select Seats</h3>
                    <p class="text-sm text-gray-600 mt-1">Click on available seats to select</p>
                </div>
                <div class="p-6">
                    <div id="seat-map-container">
                        <!-- Seat map will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Booking Summary and Payment (Hidden until seats are selected) -->
            <div id="booking-summary-section" class="bg-white rounded-lg shadow-sm border hidden">
                <div class="p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">💳 Booking Summary & Payment</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-3">Booking Details</h4>
                            <div id="booking-details" class="space-y-2 text-sm">
                                <!-- Booking details will be populated here -->
                            </div>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-3">Payment Method</h4>
                            <select id="payment-method" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 mb-4">
                                <option value="cash">💵 Cash Payment</option>
                                <option value="gcash">📱 GCash</option>
                                <option value="maya">💳 Maya</option>
                            </select>
                            <button onclick="processWalkInBooking()" class="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-semibold transition-colors">
                                🎫 Complete Booking
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <style>
        .booking-type-card.active .p-4 {
            border-color: #3B82F6 !important;
            background-color: #EFF6FF !important;
        }
        
        .booking-type-card:not(.active) .p-4 {
            border-color: #D1D5DB !important;
            background-color: #F9FAFB !important;
        }
        
        .seat {
            width: 40px;
            height: 40px;
            margin: 2px;
            border: 2px solid #ddd;
            border-radius: 6px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            transition: all 0.2s;
        }
        
        .seat.available {
            background-color: #10B981;
            color: white;
            border-color: #059669;
        }
        
        .seat.selected {
            background-color: #3B82F6;
            color: white;
            border-color: #2563EB;
        }
        
        .seat.booked {
            background-color: #EF4444;
            color: white;
            border-color: #DC2626;
            cursor: not-allowed;
        }
        
        .seat:hover:not(.booked) {
            transform: scale(1.1);
        }
    </style>

    <script>
        let selectedTrip = null;
        let selectedSeats = [];
        let currentBookingType = 'today';
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadTodaysTrips();
        });
        
        function selectBookingType(type) {
            currentBookingType = type;
            
            // Update UI
            document.querySelectorAll('.booking-type-card').forEach(card => {
                card.classList.remove('active');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('active');
            
            // Show/hide advanced booking section
            const advancedSection = document.getElementById('advanced-booking-section');
            if (type === 'advanced') {
                advancedSection.classList.remove('hidden');
                document.getElementById('trips-subtitle').textContent = 'Select date and route to search trips';
                document.getElementById('trips-container').innerHTML = '<div class="text-center py-8 text-gray-500">Please select travel date and route above</div>';
            } else {
                advancedSection.classList.add('hidden');
                document.getElementById('trips-subtitle').textContent = "Today's available trips";
                loadTodaysTrips();
            }
            
            // Reset form
            resetForm();
        }
        
        function loadTodaysTrips() {
            document.getElementById('trips-container').innerHTML = '<div class="text-center py-8"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div><p class="text-gray-600 mt-2">Loading trips...</p></div>';
            
            fetch('/api/agent/todays-trips')
                .then(response => response.json())
                .then(data => {
                    displayTrips(data.trips);
                })
                .catch(error => {
                    console.error('Error loading trips:', error);
                    document.getElementById('trips-container').innerHTML = '<div class="text-center py-8 text-red-500">Error loading trips. Please try again.</div>';
                });
        }
        
        function searchAdvancedTrips() {
            const date = document.getElementById('travel-date').value;
            const origin = document.getElementById('origin-select').value;
            const destination = document.getElementById('destination-select').value;
            
            if (!date || !origin || !destination) {
                alert('Please select travel date, origin, and destination');
                return;
            }
            
            if (origin === destination) {
                alert('Origin and destination cannot be the same');
                return;
            }
            
            document.getElementById('trips-container').innerHTML = '<div class="text-center py-8"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div><p class="text-gray-600 mt-2">Searching trips...</p></div>';
            
            fetch(`/api/agent/search-trips?date=${date}&origin=${origin}&destination=${destination}`)
                .then(response => response.json())
                .then(data => {
                    displayTrips(data.trips);
                    document.getElementById('trips-subtitle').textContent = `Trips for ${date} from ${origin} to ${destination}`;
                })
                .catch(error => {
                    console.error('Error searching trips:', error);
                    document.getElementById('trips-container').innerHTML = '<div class="text-center py-8 text-red-500">Error searching trips. Please try again.</div>';
                });
        }
        
        function displayTrips(trips) {
            const container = document.getElementById('trips-container');
            
            if (!trips || trips.length === 0) {
                container.innerHTML = '<div class="text-center py-8 text-gray-500">No trips available for the selected criteria</div>';
                return;
            }
            
            let html = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">';
            
            trips.forEach(trip => {
                const availableSeats = trip.total_seats - trip.booked_seats;
                const statusColor = availableSeats > 20 ? 'green' : availableSeats > 0 ? 'yellow' : 'red';
                
                html += `
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="selectTrip(${trip.id})">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <h4 class="font-semibold text-gray-800">${trip.route.start_point} → ${trip.route.end_point}</h4>
                                <p class="text-sm text-gray-600">${trip.bus.name} (${trip.bus.bus_code})</p>
                            </div>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                Trip #${trip.id}
                            </span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Departure:</span>
                                <span class="font-medium">${trip.schedule.start_time}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Available Seats:</span>
                                <span class="font-medium text-${statusColor}-600">${availableSeats}/${trip.total_seats}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Fare:</span>
                                <span class="font-medium text-green-600">₱${trip.fare}</span>
                            </div>
                        </div>
                        <button class="w-full mt-3 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded text-sm transition-colors">
                            Select Trip
                        </button>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }
        
        function selectTrip(tripId) {
            selectedTrip = tripId;
            document.getElementById('customer-form-section').classList.remove('hidden');
            
            // Scroll to customer form
            document.getElementById('customer-form-section').scrollIntoView({ behavior: 'smooth' });
        }
        
        function resetForm() {
            selectedTrip = null;
            selectedSeats = [];
            document.getElementById('customer-form-section').classList.add('hidden');
            document.getElementById('seat-selection-section').classList.add('hidden');
            document.getElementById('booking-summary-section').classList.add('hidden');
        }
        
        // Validate customer form and proceed to seat selection
        function proceedToSeatSelection() {
            const firstName = document.getElementById('first-name').value.trim();
            const lastName = document.getElementById('last-name').value.trim();
            const mobile = document.getElementById('mobile').value.trim();
            const address = document.getElementById('address').value.trim();

            if (!firstName || !lastName || !mobile || !address) {
                alert('Please fill in all required fields');
                return;
            }

            // Load seat map
            loadSeatMap();
            document.getElementById('seat-selection-section').classList.remove('hidden');
            document.getElementById('seat-selection-section').scrollIntoView({ behavior: 'smooth' });
        }

        function loadSeatMap() {
            fetch(`/api/agent/trip-seats/${selectedTrip}`)
                .then(response => response.json())
                .then(data => {
                    renderSeatMap(data.seats, data.bookedSeats);
                })
                .catch(error => {
                    console.error('Error loading seat map:', error);
                });
        }

        function renderSeatMap(totalSeats, bookedSeats) {
            const container = document.getElementById('seat-map-container');
            let html = '<div class="seat-map">';

            // Create seat layout (assuming 45 seats in 2-2 configuration)
            for (let i = 1; i <= totalSeats; i++) {
                const isBooked = bookedSeats.includes(i.toString());
                const seatClass = isBooked ? 'booked' : 'available';

                html += `<div class="seat ${seatClass}" data-seat="${i}" onclick="toggleSeat(${i}, this)">${i}</div>`;

                // Add aisle space after every 4 seats
                if (i % 4 === 0) {
                    html += '<br>';
                }
            }

            html += '</div>';
            html += `
                <div class="mt-4 flex justify-between items-center">
                    <div class="flex space-x-4 text-sm">
                        <div class="flex items-center">
                            <div class="seat available mr-2"></div>
                            <span>Available</span>
                        </div>
                        <div class="flex items-center">
                            <div class="seat selected mr-2"></div>
                            <span>Selected</span>
                        </div>
                        <div class="flex items-center">
                            <div class="seat booked mr-2"></div>
                            <span>Booked</span>
                        </div>
                    </div>
                    <button onclick="proceedToPayment()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors">
                        Continue to Payment
                    </button>
                </div>
            `;

            container.innerHTML = html;
        }

        function toggleSeat(seatNumber, element) {
            if (element.classList.contains('booked')) {
                return; // Can't select booked seats
            }

            if (element.classList.contains('selected')) {
                // Deselect seat
                element.classList.remove('selected');
                element.classList.add('available');
                selectedSeats = selectedSeats.filter(seat => seat !== seatNumber);
            } else {
                // Select seat
                element.classList.remove('available');
                element.classList.add('selected');
                selectedSeats.push(seatNumber);
            }
        }

        function proceedToPayment() {
            if (selectedSeats.length === 0) {
                alert('Please select at least one seat');
                return;
            }

            // Load booking summary
            loadBookingSummary();
            document.getElementById('booking-summary-section').classList.remove('hidden');
            document.getElementById('booking-summary-section').scrollIntoView({ behavior: 'smooth' });
        }

        function loadBookingSummary() {
            fetch(`/api/agent/trip-details/${selectedTrip}`)
                .then(response => response.json())
                .then(data => {
                    const trip = data.trip;
                    const fare = parseFloat(trip.fare);
                    const totalAmount = fare * selectedSeats.length;

                    const firstName = document.getElementById('first-name').value;
                    const lastName = document.getElementById('last-name').value;

                    const html = `
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Passenger:</span>
                                <span class="font-medium">${firstName} ${lastName}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Route:</span>
                                <span class="font-medium">${trip.route.start_point} → ${trip.route.end_point}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Bus:</span>
                                <span class="font-medium">${trip.bus.name} (${trip.bus.bus_code})</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Departure:</span>
                                <span class="font-medium">${trip.schedule.start_time}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Seats:</span>
                                <span class="font-medium">${selectedSeats.join(', ')}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Fare per seat:</span>
                                <span class="font-medium">₱${fare.toFixed(2)}</span>
                            </div>
                            <div class="flex justify-between border-t pt-2">
                                <span class="font-semibold">Total Amount:</span>
                                <span class="font-semibold text-green-600">₱${totalAmount.toFixed(2)}</span>
                            </div>
                        </div>
                    `;

                    document.getElementById('booking-details').innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading booking summary:', error);
                });
        }

        function processWalkInBooking() {
            const bookingData = {
                trip_id: selectedTrip,
                seats: selectedSeats,
                passenger: {
                    first_name: document.getElementById('first-name').value,
                    last_name: document.getElementById('last-name').value,
                    email: document.getElementById('email').value,
                    mobile: document.getElementById('mobile').value,
                    address: document.getElementById('address').value
                },
                payment_method: document.getElementById('payment-method').value,
                booking_type: currentBookingType
            };

            fetch('/api/agent/process-walk-in-booking', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(bookingData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Booking completed successfully!');
                    window.location.href = `/agent/print-ticket/${data.ticket_id}`;
                } else {
                    alert('Booking failed: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error processing booking:', error);
                alert('An error occurred while processing the booking');
            });
        }

        // Add event listeners for form validation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('booking-form');
            const inputs = form.querySelectorAll('input[required], textarea[required]');

            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    // Check if all required fields are filled
                    const allFilled = Array.from(inputs).every(inp => inp.value.trim() !== '');

                    if (allFilled) {
                        // Add continue button if not exists
                        if (!document.getElementById('continue-to-seats')) {
                            const button = document.createElement('button');
                            button.id = 'continue-to-seats';
                            button.type = 'button';
                            button.className = 'mt-4 bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors';
                            button.textContent = 'Continue to Seat Selection';
                            button.onclick = proceedToSeatSelection;
                            form.appendChild(button);
                        }
                    }
                });
            });
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\XAMPP\htdocs\GL_BUS\resources\views/agent/improved-walk-in-booking.blade.php ENDPATH**/ ?>