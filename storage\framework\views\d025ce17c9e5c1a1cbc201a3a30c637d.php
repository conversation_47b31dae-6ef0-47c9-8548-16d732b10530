<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="description" content="Login to your account - GL Bus Reservation System">
    <meta name="keywords" content="login, sign in, account access, GL Bus">

    <title>Login - <?php echo e(config('app.name')); ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?php echo e(asset('images/gl-logo.png')); ?>">
    <link rel="shortcut icon" type="image/png" href="<?php echo e(asset('images/gl-logo.png')); ?>">
    <link rel="apple-touch-icon" href="<?php echo e(asset('images/gl-logo.png')); ?>">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    
    <style>
        :root {
            /* GL Bus Color Palette */
            --primary-gold: #FCB404;
            --primary-gold-dark: #E6A200;
            --secondary-blue: #1E3A8A;
            --secondary-blue-light: #3B82F6;
            --accent-green: #10B981;
            --accent-purple: #8B5CF6;
            --accent-orange: #F97316;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --gradient-hero: linear-gradient(135deg, var(--primary-gold) 0%, var(--accent-orange) 50%, var(--secondary-blue) 100%);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        body {
            font-family: 'Figtree', sans-serif;
            background: var(--gradient-hero);
            min-height: 100vh;
        }
        
        .form-container {
            background: white;
            border-radius: 1rem;
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .form-header {
            background: var(--gradient-hero);
            padding: 2rem;
            text-align: center;
            color: white;
        }
        
        .form-body {
            padding: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--gray-200);
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--gray-50);
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary-gold);
            background: white;
            box-shadow: 0 0 0 3px rgba(252, 180, 4, 0.1);
        }
        
        .btn-primary {
            width: 100%;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-dark));
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(252, 180, 4, 0.4);
        }
        
        .error-message {
            color: #dc2626;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }
        
        .success-message {
            color: #059669;
            font-size: 0.875rem;
            margin-bottom: 1rem;
            padding: 0.75rem;
            background: #d1fae5;
            border-radius: 0.5rem;
            border: 1px solid #a7f3d0;
        }
        
        .link {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }
        
        .link:hover {
            text-decoration: underline;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-sm {
            font-size: 0.875rem;
        }
        
        .mt-4 {
            margin-top: 1rem;
        }
        
        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .checkbox {
            width: 1rem;
            height: 1rem;
            accent-color: var(--primary-blue);
        }
        

        
        @media (max-width: 640px) {
            .form-container {
                margin: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-md">
            <div class="form-container">
                <!-- Header -->
                <div class="form-header">
                    <h1 class="text-3xl font-bold mb-2">🚌 Welcome Back</h1>
                    <p class="opacity-90">Login to your account</p>
                </div>
                
                <!-- Form -->
                <div class="form-body">
                    <!-- Session Status -->
                    <?php if(session('status')): ?>
                        <div class="success-message">
                            <?php echo e(session('status')); ?>

                        </div>
                    <?php endif; ?>

                    <form method="POST" action="<?php echo e(route('login')); ?>">
                        <?php echo csrf_field(); ?>

                        <!-- Email -->
                        <div class="form-group">
                            <label for="email" class="form-label">Email Address</label>
                            <input id="email" class="form-input" type="email" name="email" value="<?php echo e(old('email')); ?>" 
                                   required autofocus autocomplete="username" placeholder="Enter your email" />
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="error-message"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Password -->
                        <div class="form-group">
                            <label for="password" class="form-label">Password</label>
                            <input id="password" class="form-input" type="password" name="password" 
                                   required autocomplete="current-password" placeholder="Enter your password" />
                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="error-message"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Remember Me -->
                        <div class="form-group">
                            <div class="checkbox-container">
                                <input id="remember_me" type="checkbox" class="checkbox" name="remember">
                                <label for="remember_me" class="text-sm text-gray-600">Remember me</label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-group">
                            <button type="submit" class="btn-primary">
                                🔑 Login to Account
                            </button>
                        </div>

                        <!-- Links -->
                        <div class="text-center">
                            <?php if(Route::has('password.request')): ?>
                                <a href="<?php echo e(route('password.request')); ?>" class="link text-sm">
                                    Forgot your password?
                                </a>
                            <?php endif; ?>
                        </div>

                        <div class="text-center mt-4">
                            <p class="text-sm text-gray-600">
                                Don't have an account? 
                                <a href="<?php echo e(route('register')); ?>" class="link">Register here</a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Back to Home -->
            <div class="text-center mt-6">
                <a href="<?php echo e(route('welcome')); ?>" class="text-white/80 hover:text-white text-sm">
                    ← Back to Home
                </a>
            </div>
        </div>
    </div>


</body>
</html>
<?php /**PATH D:\XAMPP\htdocs\GL_BUS\resources\views/auth/login.blade.php ENDPATH**/ ?>